<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اتصل بنا - متجر VelaSweets</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap');
        
        :root {
            --primary-color: #5a287d;
            --secondary-color: #f8a5c2;
            --tertiary-color: #b07d28;
            --text-color: #333;
            --bg-color: #f8f9fa;
            --card-bg: #fff;
            --border-color: #dee2e6;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --success-color: #28a745;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
        }
        
        .navbar-custom {
            background-color: white;
            box-shadow: 0 2px 10px var(--shadow-color);
            padding: 15px 0;
        }
        
        .navbar-brand {
            font-family: 'Playfair Display', serif;
            color: var(--primary-color);
            font-size: 1.8rem;
            font-weight: 600;
        }
        
        .nav-link {
            color: var(--text-color);
            font-weight: 500;
            margin: 0 10px;
            transition: all 0.3s;
        }
        
        .nav-link:hover, .nav-link.active {
            color: var(--primary-color);
        }
        
        .hero-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 80px 0;
            text-align: center;
        }
        
        .hero-title {
            font-family: 'Playfair Display', serif;
            font-size: 3rem;
            margin-bottom: 20px;
        }
        
        .hero-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .contact-section {
            padding: 80px 0;
        }
        
        .section-title {
            font-family: 'Playfair Display', serif;
            color: var(--primary-color);
            margin-bottom: 50px;
            text-align: center;
            font-size: 2.5rem;
        }
        
        .contact-info {
            background-color: var(--card-bg);
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px var(--shadow-color);
            margin-bottom: 30px;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px;
            border-radius: 10px;
            transition: all 0.3s;
        }
        
        .contact-item:hover {
            background-color: var(--bg-color);
            transform: translateX(10px);
        }
        
        .contact-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 20px;
            font-size: 1.5rem;
            color: white;
        }
        
        .contact-details h4 {
            color: var(--primary-color);
            margin-bottom: 5px;
            font-weight: 600;
        }
        
        .contact-details p {
            color: #666;
            margin: 0;
            font-size: 1.1rem;
        }
        
        .contact-form {
            background-color: var(--card-bg);
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px var(--shadow-color);
        }
        
        .form-title {
            font-family: 'Playfair Display', serif;
            color: var(--primary-color);
            margin-bottom: 30px;
            text-align: center;
            font-size: 2rem;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-label {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 8px;
        }
        
        .form-control, .form-select {
            border: 2px solid var(--border-color);
            border-radius: 10px;
            padding: 12px 15px;
            font-size: 1rem;
            transition: all 0.3s;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(90, 40, 125, 0.25);
        }
        
        .form-control::placeholder {
            color: #999;
        }
        
        .btn-submit {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s;
            width: 100%;
        }
        
        .btn-submit:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(90, 40, 125, 0.3);
        }
        
        .btn-submit:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .map-section {
            padding: 60px 0;
            background-color: white;
        }
        
        .map-container {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px var(--shadow-color);
            height: 400px;
            background-color: var(--bg-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 1.2rem;
        }
        
        .hours-section {
            padding: 60px 0;
        }
        
        .hours-card {
            background-color: var(--card-bg);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 20px var(--shadow-color);
            text-align: center;
        }
        
        .hours-title {
            font-family: 'Playfair Display', serif;
            color: var(--primary-color);
            margin-bottom: 20px;
            font-size: 1.8rem;
        }
        
        .hours-list {
            list-style: none;
            padding: 0;
        }
        
        .hours-list li {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid var(--border-color);
        }
        
        .hours-list li:last-child {
            border-bottom: none;
        }
        
        .day {
            font-weight: 600;
            color: var(--text-color);
        }
        
        .time {
            color: var(--tertiary-color);
            font-weight: 500;
        }
        
        .cart-icon {
            position: relative;
        }
        
        .cart-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background-color: var(--secondary-color);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .user-welcome {
            display: none;
        }
        
        .user-welcome.logged-in {
            display: block;
        }
        
        .footer {
            background-color: var(--primary-color);
            color: white;
            padding: 40px 0 20px;
            margin-top: 60px;
        }
        
        .footer-title {
            font-family: 'Playfair Display', serif;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }
        
        .footer-links {
            list-style: none;
            padding: 0;
        }
        
        .footer-links li {
            margin-bottom: 8px;
        }
        
        .footer-links a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .footer-links a:hover {
            color: white;
            padding-right: 5px;
        }
        
        .footer-bottom {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 20px;
            margin-top: 30px;
            text-align: center;
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
        }
        
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }
            
            .section-title {
                font-size: 2rem;
            }
            
            .contact-info, .contact-form {
                padding: 25px;
            }
            
            .contact-item {
                flex-direction: column;
                text-align: center;
            }
            
            .contact-icon {
                margin-left: 0;
                margin-bottom: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="index.html">VelaSweets</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.html">المنتجات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html">من نحن</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="contact.html">اتصل بنا</a>
                    </li>
                </ul>
                <div class="d-flex align-items-center">
                    <div class="user-welcome ms-3" id="userWelcome">
                        <span>مرحباً، <strong id="userName">زائر</strong></span>
                    </div>
                    <a href="cart.html" class="nav-link ms-3 cart-icon">
                        <i class="bi bi-cart3 fs-5"></i>
                        <span class="cart-badge" id="cartBadge">0</span>
                    </a>
                    <div id="authButtons">
                        <a href="login.html" class="btn btn-outline-primary ms-2">تسجيل الدخول</a>
                        <a href="register.html" class="btn btn-primary">إنشاء حساب</a>
                    </div>
                    <div id="userDropdown" style="display:none;">
                        <div class="dropdown">
                            <button class="btn btn-outline-primary dropdown-toggle" type="button" id="userMenuButton" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle me-1"></i>
                                حسابي
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="profile.html">الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="orders.html">طلباتي</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" id="logoutBtn">تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <h1 class="hero-title">اتصل بنا</h1>
            <p class="hero-subtitle">نحن هنا للإجابة على جميع استفساراتكم وخدمتكم</p>
        </div>
    </section>
    
    <!-- Contact Section -->
    <section class="contact-section">
        <div class="container">
            <h2 class="section-title">تواصل معنا</h2>
            <div class="row">
                <div class="col-lg-6">
                    <div class="contact-info">
                        <div class="contact-item">
                            <div class="contact-icon" style="background-color: var(--primary-color);">
                                <i class="bi bi-geo-alt-fill"></i>
                            </div>
                            <div class="contact-details">
                                <h4>العنوان</h4>
                                <p>شارع الرشيد، منطقة الكرادة<br>بغداد، العراق</p>
                            </div>
                        </div>
                        
                        <div class="contact-item">
                            <div class="contact-icon" style="background-color: var(--secondary-color);">
                                <i class="bi bi-telephone-fill"></i>
                            </div>
                            <div class="contact-details">
                                <h4>الهاتف</h4>
                                <p>+964 123 456 789<br>+964 987 654 321</p>
                            </div>
                        </div>
                        
                        <div class="contact-item">
                            <div class="contact-icon" style="background-color: var(--tertiary-color);">
                                <i class="bi bi-envelope-fill"></i>
                            </div>
                            <div class="contact-details">
                                <h4>البريد الإلكتروني</h4>
                                <p><EMAIL><br><EMAIL></p>
                            </div>
                        </div>
                        
                        <div class="contact-item">
                            <div class="contact-icon" style="background-color: var(--primary-color);">
                                <i class="bi bi-clock-fill"></i>
                            </div>
                            <div class="contact-details">
                                <h4>ساعات العمل</h4>
                                <p>السبت - الخميس: 9:00 ص - 10:00 م<br>الجمعة: 2:00 م - 10:00 م</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <div class="contact-form">
                        <h3 class="form-title">أرسل لنا رسالة</h3>
                        <form id="contactForm">
                            <div class="form-group">
                                <label for="name" class="form-label">الاسم الكامل *</label>
                                <input type="text" class="form-control" id="name" name="name" required placeholder="أدخل اسمك الكامل">
                            </div>
                            
                            <div class="form-group">
                                <label for="email" class="form-label">البريد الإلكتروني *</label>
                                <input type="email" class="form-control" id="email" name="email" required placeholder="أدخل بريدك الإلكتروني">
                            </div>
                            
                            <div class="form-group">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone" placeholder="أدخل رقم هاتفك">
                            </div>
                            
                            <div class="form-group">
                                <label for="subject" class="form-label">الموضوع *</label>
                                <select class="form-select" id="subject" name="subject" required>
                                    <option value="">اختر الموضوع</option>
                                    <option value="general">استفسار عام</option>
                                    <option value="order">استفسار عن طلب</option>
                                    <option value="complaint">شكوى</option>
                                    <option value="suggestion">اقتراح</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="message" class="form-label">الرسالة *</label>
                                <textarea class="form-control" id="message" name="message" rows="5" required placeholder="اكتب رسالتك هنا..."></textarea>
                            </div>
                            
                            <button type="submit" class="btn-submit">
                                <i class="bi bi-send me-2"></i>
                                إرسال الرسالة
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Map Section -->
    <section class="map-section">
        <div class="container">
            <h2 class="section-title">موقعنا</h2>
            <div class="map-container">
                <div class="text-center">
                    <i class="bi bi-geo-alt-fill" style="font-size: 3rem; color: var(--primary-color); margin-bottom: 15px;"></i>
                    <p>خريطة الموقع ستكون متاحة قريباً</p>
                    <p class="text-muted">شارع الرشيد، منطقة الكرادة، بغداد، العراق</p>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Hours Section -->
    <section class="hours-section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-6">
                    <div class="hours-card">
                        <h3 class="hours-title">ساعات العمل</h3>
                        <ul class="hours-list">
                            <li>
                                <span class="day">السبت</span>
                                <span class="time">9:00 ص - 10:00 م</span>
                            </li>
                            <li>
                                <span class="day">الأحد</span>
                                <span class="time">9:00 ص - 10:00 م</span>
                            </li>
                            <li>
                                <span class="day">الاثنين</span>
                                <span class="time">9:00 ص - 10:00 م</span>
                            </li>
                            <li>
                                <span class="day">الثلاثاء</span>
                                <span class="time">9:00 ص - 10:00 م</span>
                            </li>
                            <li>
                                <span class="day">الأربعاء</span>
                                <span class="time">9:00 ص - 10:00 م</span>
                            </li>
                            <li>
                                <span class="day">الخميس</span>
                                <span class="time">9:00 ص - 10:00 م</span>
                            </li>
                            <li>
                                <span class="day">الجمعة</span>
                                <span class="time">2:00 م - 10:00 م</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h3 class="footer-title">VelaSweets</h3>
                    <p>متجر متخصص في تقديم أشهى الحلويات الشرقية والغربية المصنوعة من أجود الخامات.</p>
                </div>
                <div class="col-md-3">
                    <h4 class="footer-title">روابط سريعة</h4>
                    <ul class="footer-links">
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="products.html">المنتجات</a></li>
                        <li><a href="about.html">من نحن</a></li>
                        <li><a href="contact.html">اتصل بنا</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h4 class="footer-title">خدمة العملاء</h4>
                    <ul class="footer-links">
                        <li><a href="#">الأسئلة الشائعة</a></li>
                        <li><a href="#">سياسة الخصوصية</a></li>
                        <li><a href="#">شروط الاستخدام</a></li>
                        <li><a href="#">سياسة الإرجاع</a></li>
                    </ul>
                </div>
                <div class="col-md-2">
                    <h4 class="footer-title">تواصل معنا</h4>
                    <ul class="footer-links">
                        <li><i class="bi bi-geo-alt me-2"></i> بغداد، العراق</li>
                        <li><i class="bi bi-telephone me-2"></i> +964 123 456 789</li>
                        <li><i class="bi bi-envelope me-2"></i> <EMAIL></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>جميع الحقوق محفوظة &copy; 2025 VelaSweets</p>
            </div>
        </div>
    </footer>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="scripts/auth.js"></script>
    <script>
        let cart = JSON.parse(localStorage.getItem('cart')) || [];

        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من حالة تسجيل الدخول
            checkLoggedIn();

            // تحديث عداد السلة
            updateCartBadge();

            // إعداد مستمع حدث تسجيل الخروج
            const logoutBtn = document.getElementById('logoutBtn');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    localStorage.removeItem('customer_token');
                    localStorage.removeItem('customer_data');
                    window.location.reload();
                });
            }

            // إعداد نموذج الاتصال
            setupContactForm();

            // ملء البيانات إذا كان المستخدم مسجل الدخول
            fillUserData();
        });

        function checkLoggedIn() {
            const token = localStorage.getItem('customer_token');
            const authButtons = document.getElementById('authButtons');
            const userDropdown = document.getElementById('userDropdown');
            const userWelcome = document.getElementById('userWelcome');
            const userName = document.getElementById('userName');

            if (!token) {
                if (authButtons) authButtons.style.display = 'block';
                if (userDropdown) userDropdown.style.display = 'none';
                if (userWelcome) userWelcome.classList.remove('logged-in');
                return false;
            }

            try {
                const payload = parseJwt(token);
                const currentTime = Math.floor(Date.now() / 1000);

                if (!payload || payload.exp < currentTime) {
                    localStorage.removeItem('customer_token');
                    localStorage.removeItem('customer_data');

                    if (authButtons) authButtons.style.display = 'block';
                    if (userDropdown) userDropdown.style.display = 'none';
                    if (userWelcome) userWelcome.classList.remove('logged-in');
                    return false;
                }

                if (authButtons) authButtons.style.display = 'none';
                if (userDropdown) userDropdown.style.display = 'block';

                const userData = JSON.parse(localStorage.getItem('customer_data') || '{}');
                if (userName) {
                    userName.textContent = userData.fullName || userData.name || 'العميل';
                    if (userWelcome) userWelcome.classList.add('logged-in');
                }

                return true;

            } catch (error) {
                localStorage.removeItem('customer_token');
                localStorage.removeItem('customer_data');

                if (authButtons) authButtons.style.display = 'block';
                if (userDropdown) userDropdown.style.display = 'none';
                if (userWelcome) userWelcome.classList.remove('logged-in');
                return false;
            }
        }

        function parseJwt(token) {
            try {
                if (!token || typeof token !== 'string') {
                    return null;
                }

                const parts = token.split('.');
                if (parts.length !== 3) {
                    return null;
                }

                const base64Payload = parts[1];
                let payload;
                try {
                    payload = atob(base64Payload);
                } catch (e) {
                    payload = decodeURIComponent(atob(base64Payload));
                }

                return JSON.parse(payload);
            } catch (error) {
                console.error('خطأ في تحليل التوكن:', error);
                return null;
            }
        }

        function updateCartBadge() {
            const badge = document.getElementById('cartBadge');
            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
            if (badge) {
                badge.textContent = totalItems;
            }
        }

        function fillUserData() {
            if (checkLoggedIn()) {
                const userData = JSON.parse(localStorage.getItem('customer_data') || '{}');
                const nameField = document.getElementById('name');
                const emailField = document.getElementById('email');
                const phoneField = document.getElementById('phone');

                if (nameField && userData.fullName) {
                    nameField.value = userData.fullName;
                }

                if (emailField && userData.email) {
                    emailField.value = userData.email;
                }

                if (phoneField && userData.phone) {
                    phoneField.value = userData.phone;
                }
            }
        }

        function setupContactForm() {
            const form = document.getElementById('contactForm');
            if (!form) return;

            form.addEventListener('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(form);
                const data = {
                    name: formData.get('name'),
                    email: formData.get('email'),
                    phone: formData.get('phone'),
                    subject: formData.get('subject'),
                    message: formData.get('message'),
                    timestamp: new Date().toISOString()
                };

                // التحقق من صحة البيانات
                if (!validateForm(data)) {
                    return;
                }

                // حفظ الرسالة محلياً
                saveMessage(data);

                // إظهار رسالة نجاح
                showSuccessMessage();

                // إعادة تعيين النموذج
                form.reset();

                // إعادة ملء بيانات المستخدم إذا كان مسجل الدخول
                setTimeout(fillUserData, 100);
            });
        }

        function validateForm(data) {
            const errors = [];

            if (!data.name || data.name.trim().length < 2) {
                errors.push('يجب أن يكون الاسم أكثر من حرفين');
            }

            if (!data.email || !isValidEmail(data.email)) {
                errors.push('يجب إدخال بريد إلكتروني صحيح');
            }

            if (!data.subject) {
                errors.push('يجب اختيار موضوع الرسالة');
            }

            if (!data.message || data.message.trim().length < 10) {
                errors.push('يجب أن تكون الرسالة أكثر من 10 أحرف');
            }

            if (errors.length > 0) {
                showToast(errors.join('\n'), 'error');
                return false;
            }

            return true;
        }

        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        function saveMessage(data) {
            const messages = JSON.parse(localStorage.getItem('contact_messages') || '[]');
            messages.push({
                id: Date.now(),
                ...data,
                status: 'new'
            });
            localStorage.setItem('contact_messages', JSON.stringify(messages));
        }

        function showSuccessMessage() {
            const form = document.getElementById('contactForm');
            const successDiv = document.createElement('div');
            successDiv.className = 'alert alert-success mt-3';
            successDiv.innerHTML = `
                <i class="bi bi-check-circle me-2"></i>
                <strong>تم إرسال رسالتك بنجاح!</strong><br>
                سنقوم بالرد عليك في أقرب وقت ممكن.
            `;

            form.appendChild(successDiv);

            // إزالة الرسالة بعد 5 ثوان
            setTimeout(() => {
                if (successDiv.parentNode) {
                    successDiv.parentNode.removeChild(successDiv);
                }
            }, 5000);

            // التمرير إلى الرسالة
            successDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }

        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = 'toast-notification';
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? 'var(--success-color)' : '#dc3545'};
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 9999;
                transform: translateX(100%);
                transition: transform 0.3s ease;
                max-width: 300px;
                white-space: pre-line;
            `;
            toast.textContent = message;

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 100);

            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, type === 'error' ? 5000 : 3000);
        }
    </script>
