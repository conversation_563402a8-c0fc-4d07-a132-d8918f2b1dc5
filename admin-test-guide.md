# 🔧 دليل نظام الاختبار الإداري - VelaSweets

## نظرة عامة
نظام اختبار شامل ومتقدم مصمم خصيصاً لمراقبة وفحص جميع مكونات متجر VelaSweets الإلكتروني. يوفر واجهة إدارية احترافية لضمان عمل النظام بأعلى مستويات الجودة والأمان.

## 🎯 الهدف من النظام
- **مراقبة مستمرة**: فحص دوري لجميع مكونات النظام
- **اكتشاف المشاكل مبكراً**: تحديد الأخطاء قبل تأثيرها على المستخدمين
- **ضمان الجودة**: التأكد من عمل جميع الوظائف بشكل صحيح
- **تقارير مفصلة**: معلومات واضحة عن حالة النظام

## 📋 الاختبارات المشمولة

### 1. 🔐 نظام المصادقة
- **تحميل ملفات المصادقة**: التحقق من وجود جميع دوال المصادقة المطلوبة
- **تسجيل حساب جديد**: اختبار عملية إنشاء حسابات جديدة
- **تسجيل الدخول**: فحص آلية تسجيل الدخول والتحقق من البيانات
- **التحقق من رقم الهاتف العراقي**: ضمان قبول الأرقام بتنسيق 07xxxxxxxxx فقط
- **منع الحسابات المكررة**: التأكد من عدم قبول نفس البريد أو الهاتف مرتين
- **أمان كلمات المرور**: فحص قوة وحماية كلمات المرور

### 2. 💰 نظام العملة
- **تحميل نظام العملة**: التحقق من وجود دوال العملة والأسعار
- **تثبيت الدينار العراقي**: ضمان استخدام IQD كعملة وحيدة
- **تنسيق عرض الأسعار**: فحص عرض الأسعار بتنسيق صحيح مع رمز د.ع
- **حساب رسوم الشحن للبصرة**: التأكد من رسوم 3,000 د.ع للبصرة
- **حساب رسوم الشحن للمحافظات الأخرى**: التأكد من رسوم 5,000 د.ع لباقي المحافظات
- **تحويل العملات القديمة**: فحص تحديث الأسعار من الدولار للدينار

### 3. 🌐 نظام اللغات
- **تحميل نظام اللغات**: التحقق من وجود دوال الترجمة
- **دعم اللغة العربية (RTL)**: فحص الترجمة والاتجاه للعربية
- **دعم اللغة الكردية (RTL)**: فحص الترجمة والاتجاه للكردية
- **دعم اللغة الإنجليزية (LTR)**: فحص الترجمة والاتجاه للإنجليزية
- **تغيير الاتجاه تلقائياً**: ضمان تبديل RTL/LTR حسب اللغة
- **حفظ اللغة المختارة**: التأكد من حفظ اللغة في localStorage

### 4. 📦 نظام المنتجات
- **تحميل قاعدة بيانات المنتجات**: فحص وجود وتحميل المنتجات
- **صحة أسعار المنتجات**: التأكد من أن الأسعار بالدينار العراقي
- **تصنيف المنتجات**: فحص وجود فئات متنوعة للمنتجات
- **صور المنتجات**: التحقق من وجود روابط صور صحيحة
- **توفر المنتجات**: فحص حالة التوفر والمخزون
- **بيانات المنتجات المكتملة**: ضمان وجود جميع البيانات المطلوبة

### 5. 🛒 السلة والطلبات
- **إضافة منتج للسلة**: اختبار عملية إضافة المنتجات
- **حساب مجموع السلة**: فحص حساب المجموع الصحيح
- **تطبيق رسوم الشحن**: ضمان إضافة رسوم الشحن المناسبة
- **حفظ الطلبات**: اختبار حفظ الطلبات في قاعدة البيانات
- **تحديث كمية المنتجات**: فحص تعديل الكميات في السلة
- **إزالة منتجات من السلة**: اختبار حذف المنتجات

### 6. 🖥️ الواجهة والمظهر
- **تجاوب التصميم**: فحص عمل التصميم على أحجام مختلفة
- **عرض خيارات المستخدم**: ضمان ظهور الملف الشخصي والإعدادات
- **تحميل الخطوط والأيقونات**: التحقق من تحميل الموارد البصرية
- **عمل الروابط والأزرار**: فحص وظائف التنقل والتفاعل
- **عرض الرسائل والتنبيهات**: اختبار نظام الإشعارات
- **سلاسة الانتقالات**: فحص الحركات والتأثيرات البصرية

### 7. 🔒 الأمان والبنية
- **هيكل الملفات**: التحقق من تحميل جميع الملفات المطلوبة
- **ترابط السكربتات**: فحص الاتصال بين مكونات النظام
- **عدم وجود أخطاء في الكونسول**: مراقبة الأخطاء البرمجية
- **حماية البيانات الحساسة**: ضمان أمان المعلومات الشخصية
- **التحقق من الجلسات**: فحص آلية التحقق من تسجيل الدخول
- **منع الوصول غير المصرح**: ضمان حماية البيانات المحمية

## 🎮 كيفية الاستخدام

### الوصول للنظام
1. افتح الملف `admin-system-test.html` في المتصفح
2. ستظهر لوحة التحكم الإدارية مع جميع الاختبارات

### أنواع الفحص

#### 🚀 فحص شامل
- **الزر**: "تشغيل فحص شامل"
- **الوصف**: يقوم بتشغيل جميع الاختبارات (42 اختبار)
- **المدة**: 2-3 دقائق
- **الاستخدام**: للفحص الدوري الشامل

#### ⚡ فحص سريع
- **الزر**: "فحص سريع"
- **الوصف**: يقوم بفحص الوظائف الأساسية فقط
- **المدة**: 30 ثانية
- **الاستخدام**: للفحص السريع اليومي

### أدوات الصيانة

#### 🔄 إعادة تهيئة
- **الوظيفة**: مسح جميع البيانات وإعادة تحميل البيانات الافتراضية
- **الاستخدام**: عند وجود مشاكل في البيانات
- **تحذير**: سيتم حذف جميع الحسابات والطلبات

#### 🗑️ مسح الكاش
- **الوظيفة**: مسح ذاكرة التخزين المؤقت
- **الاستخدام**: لحل مشاكل التحميل أو التحديث
- **النتيجة**: تحسين أداء النظام

## 📊 فهم النتائج

### رموز الحالة
- **✅ نجح**: الاختبار نجح بالكامل
- **❌ فشل**: الاختبار فشل ويحتاج إصلاح
- **⚠️ تحذير**: الاختبار نجح مع ملاحظات
- **🔄 جاري الاختبار**: الاختبار قيد التنفيذ

### ألوان الأقسام
- **🟢 أخضر**: جميع اختبارات القسم نجحت
- **🔴 أحمر**: يوجد اختبارات فاشلة في القسم
- **🟡 أصفر**: يوجد تحذيرات في القسم

### ملخص النتائج
- **إجمالي الاختبارات**: العدد الكلي للاختبارات المنفذة
- **نجح**: عدد الاختبارات الناجحة
- **فشل**: عدد الاختبارات الفاشلة
- **معدل النجاح**: النسبة المئوية للنجاح

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. فشل تحميل الملفات
**الأعراض**: رسائل "الملف غير محمل"
**الحل**: 
- تأكد من وجود جميع الملفات في المجلدات الصحيحة
- تحقق من أسماء الملفات والمسارات

#### 2. مشاكل في العملة
**الأعراض**: أسعار بالدولار أو رسوم شحن خاطئة
**الحل**:
- شغل "إعادة تهيئة" لتحديث الأسعار
- تحقق من ملف `scripts/currency.js`

#### 3. مشاكل في اللغات
**الأعراض**: عدم تغيير الاتجاه أو الترجمة
**الحل**:
- امسح الكاش وأعد تحميل الصفحة
- تحقق من ملف `scripts/language.js`

#### 4. مشاكل في المصادقة
**الأعراض**: فشل تسجيل الدخول أو إنشاء الحسابات
**الحل**:
- تحقق من تنسيق أرقام الهواتف (07xxxxxxxxx)
- امسح البيانات القديمة وأعد التهيئة

## 📅 جدولة الفحص المقترحة

### يومياً
- فحص سريع في بداية اليوم
- مراقبة رسائل الخطأ

### أسبوعياً  
- فحص شامل مرة في الأسبوع
- مراجعة تقارير الأداء

### شهرياً
- إعادة تهيئة كاملة للنظام
- تحديث البيانات الافتراضية
- مراجعة الأمان والحماية

## 🆘 الدعم والمساعدة

### في حالة وجود مشاكل:
1. شغل الفحص الشامل أولاً
2. راجع تفاصيل الأخطاء في كل قسم
3. جرب إعادة التهيئة إذا لزم الأمر
4. تحقق من console المتصفح للأخطاء التقنية

### معلومات تقنية:
- **المتصفحات المدعومة**: Chrome, Firefox, Safari, Edge
- **متطلبات النظام**: JavaScript مفعل، localStorage متاح
- **حجم البيانات**: يستخدم حوالي 2-5 MB من localStorage

---

**نظام الاختبار الإداري - VelaSweets**  
**إصدار 2.0 - نظام متقدم لضمان الجودة والأمان**
