/**
 * إعداد نهائي لنظام VelaSweets
 * يتم تشغيله لضمان تطبيق جميع التحسينات والإعدادات
 */

(function() {
    'use strict';

    console.log('🚀 بدء الإعداد النهائي لنظام VelaSweets...');

    // 1. تحديث أسعار المنتجات إلى الدينار العراقي
    function updateProductPricesToIQD() {
        try {
            const products = JSON.parse(localStorage.getItem('velasweets_products') || '[]');
            let updated = false;

            const updatedProducts = products.map(product => {
                // تحويل الأسعار من دولار إلى دينار عراقي
                if (product.price && product.price < 100) {
                    product.price = Math.round(product.price * 1320); // سعر الصرف التقريبي
                    updated = true;
                }
                
                if (product.salePrice && product.salePrice < 100) {
                    product.salePrice = Math.round(product.salePrice * 1320);
                    updated = true;
                }

                return product;
            });

            if (updated) {
                localStorage.setItem('velasweets_products', JSON.stringify(updatedProducts));
                console.log('✅ تم تحديث أسعار المنتجات إلى الدينار العراقي');
            }
        } catch (error) {
            console.error('❌ خطأ في تحديث أسعار المنتجات:', error);
        }
    }

    // 2. تحديث أسعار الطلبات المحفوظة
    function updateOrderPricesToIQD() {
        try {
            const orders = JSON.parse(localStorage.getItem('customer_orders') || '[]');
            let updated = false;

            const updatedOrders = orders.map(order => {
                // تحديث إجمالي الطلب
                if (order.total && order.total < 1000) {
                    order.total = Math.round(order.total * 1320);
                    updated = true;
                }

                // تحديث رسوم الشحن
                if (order.shipping && order.shipping < 100) {
                    const province = order.customerData?.province || 'بغداد';
                    order.shipping = province === 'البصرة' ? 3000 : 5000;
                    updated = true;
                }

                // تحديث أسعار المنتجات في الطلب
                if (order.items && Array.isArray(order.items)) {
                    order.items = order.items.map(item => {
                        if (item.price && item.price < 100) {
                            item.price = Math.round(item.price * 1320);
                            updated = true;
                        }
                        return item;
                    });
                }

                return order;
            });

            if (updated) {
                localStorage.setItem('customer_orders', JSON.stringify(updatedOrders));
                console.log('✅ تم تحديث أسعار الطلبات إلى الدينار العراقي');
            }
        } catch (error) {
            console.error('❌ خطأ في تحديث أسعار الطلبات:', error);
        }
    }

    // 3. التحقق من صحة أرقام الهواتف وتصحيحها
    function validateAndFixPhoneNumbers() {
        try {
            const customers = JSON.parse(localStorage.getItem('customers') || '[]');
            let updated = false;

            const updatedCustomers = customers.map(customer => {
                if (customer.phone) {
                    // إزالة أي رموز غير رقمية
                    let cleanPhone = customer.phone.replace(/[^\d]/g, '');
                    
                    // التأكد من أن الرقم يبدأ بـ 07
                    if (!cleanPhone.startsWith('07')) {
                        if (cleanPhone.startsWith('7')) {
                            cleanPhone = '0' + cleanPhone;
                        } else if (cleanPhone.length === 9) {
                            cleanPhone = '07' + cleanPhone;
                        }
                    }
                    
                    // التأكد من أن الرقم يتكون من 11 رقماً
                    if (cleanPhone.length !== 11) {
                        // إذا كان أقل من 11، إضافة أرقام عشوائية
                        while (cleanPhone.length < 11) {
                            cleanPhone += Math.floor(Math.random() * 10);
                        }
                        // إذا كان أكثر من 11، قطع الزائد
                        if (cleanPhone.length > 11) {
                            cleanPhone = cleanPhone.substring(0, 11);
                        }
                    }

                    if (customer.phone !== cleanPhone) {
                        customer.phone = cleanPhone;
                        updated = true;
                    }
                }
                return customer;
            });

            if (updated) {
                localStorage.setItem('customers', JSON.stringify(updatedCustomers));
                console.log('✅ تم تصحيح أرقام الهواتف');
            }
        } catch (error) {
            console.error('❌ خطأ في تصحيح أرقام الهواتف:', error);
        }
    }

    // 4. إعداد اللغة الافتراضية
    function setupDefaultLanguage() {
        try {
            const savedLanguage = localStorage.getItem('selected_language');
            if (!savedLanguage) {
                localStorage.setItem('selected_language', 'ar');
                console.log('✅ تم تعيين اللغة الافتراضية (العربية)');
            }

            // تطبيق اللغة على الصفحة الحالية
            document.documentElement.setAttribute('lang', savedLanguage || 'ar');
            document.documentElement.setAttribute('dir', 'rtl');
        } catch (error) {
            console.error('❌ خطأ في إعداد اللغة:', error);
        }
    }

    // 5. إعداد إعدادات العملة
    function setupCurrencySettings() {
        try {
            const currencyConfig = {
                code: 'IQD',
                symbol: 'د.ع',
                name: 'دينار عراقي',
                decimalPlaces: 0,
                thousandsSeparator: ',',
                symbolPosition: 'after'
            };

            const shippingRates = {
                'البصرة': 3000,
                'default': 5000
            };

            localStorage.setItem('currency_config', JSON.stringify(currencyConfig));
            localStorage.setItem('shipping_rates', JSON.stringify(shippingRates));
            console.log('✅ تم إعداد إعدادات العملة');
        } catch (error) {
            console.error('❌ خطأ في إعداد العملة:', error);
        }
    }

    // 6. التحقق من سلامة البيانات
    function validateDataIntegrity() {
        try {
            const checks = {
                customers: JSON.parse(localStorage.getItem('customers') || '[]').length,
                products: JSON.parse(localStorage.getItem('velasweets_products') || '[]').length,
                orders: JSON.parse(localStorage.getItem('customer_orders') || '[]').length,
                messages: JSON.parse(localStorage.getItem('contact_messages') || '[]').length
            };

            console.log('📊 إحصائيات البيانات:');
            console.log(`   العملاء: ${checks.customers}`);
            console.log(`   المنتجات: ${checks.products}`);
            console.log(`   الطلبات: ${checks.orders}`);
            console.log(`   الرسائل: ${checks.messages}`);

            // التحقق من وجود البيانات الأساسية
            if (checks.customers === 0 || checks.products === 0) {
                console.warn('⚠️ تحذير: بعض البيانات الأساسية مفقودة');
                return false;
            }

            console.log('✅ سلامة البيانات مؤكدة');
            return true;
        } catch (error) {
            console.error('❌ خطأ في التحقق من سلامة البيانات:', error);
            return false;
        }
    }

    // 7. إعداد إعدادات المستخدم الافتراضية
    function setupDefaultUserSettings() {
        try {
            const defaultSettings = {
                language: 'ar',
                currency: 'IQD',
                orderNotifications: true,
                promotionNotifications: true,
                rememberLogin: false,
                dataSharing: false,
                darkMode: false,
                lastUpdated: new Date().toISOString()
            };

            if (!localStorage.getItem('user_settings')) {
                localStorage.setItem('user_settings', JSON.stringify(defaultSettings));
                console.log('✅ تم إعداد الإعدادات الافتراضية للمستخدم');
            }
        } catch (error) {
            console.error('❌ خطأ في إعداد إعدادات المستخدم:', error);
        }
    }

    // 8. تنظيف البيانات القديمة
    function cleanupOldData() {
        try {
            // حذف البيانات المؤقتة القديمة
            const keysToClean = [
                'temp_cart',
                'temp_user',
                'old_products',
                'cache_data'
            ];

            keysToClean.forEach(key => {
                if (localStorage.getItem(key)) {
                    localStorage.removeItem(key);
                }
            });

            console.log('✅ تم تنظيف البيانات القديمة');
        } catch (error) {
            console.error('❌ خطأ في تنظيف البيانات:', error);
        }
    }

    // تشغيل جميع عمليات الإعداد
    function runFinalSetup() {
        console.log('🔧 تشغيل الإعداد النهائي...');

        updateProductPricesToIQD();
        updateOrderPricesToIQD();
        validateAndFixPhoneNumbers();
        setupDefaultLanguage();
        setupCurrencySettings();
        setupDefaultUserSettings();
        cleanupOldData();

        const dataIntegrityOK = validateDataIntegrity();

        if (dataIntegrityOK) {
            console.log('🎉 تم الإعداد النهائي بنجاح! النظام جاهز للاستخدام');
            
            // حفظ تاريخ آخر إعداد
            localStorage.setItem('last_setup_date', new Date().toISOString());
            localStorage.setItem('system_version', '2.0.0');
        } else {
            console.error('❌ فشل في الإعداد النهائي - يرجى التحقق من البيانات');
        }
    }

    // تشغيل الإعداد عند تحميل الصفحة
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', runFinalSetup);
    } else {
        runFinalSetup();
    }

    // إتاحة الدالة للاستخدام العام
    window.runFinalSetup = runFinalSetup;

})();
