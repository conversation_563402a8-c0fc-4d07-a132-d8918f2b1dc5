<!DOCTYPE html>
<html lang="ar" dir="rtl" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - متجر VelaSweets</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap');
        
        :root {
            --primary-color: #5a287d;
            --secondary-color: #f8a5c2;
            --tertiary-color: #b07d28;
            --text-color: #333;
            --text-light: #fff;
            --bg-color: #f8f9fa;
            --card-bg: #fff;
            --border-color: #dee2e6;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }
        
        [data-theme="dark"] {
            --primary-color: #7e3ca3;
            --secondary-color: #ff9aba;
            --tertiary-color: #d9a440;
            --text-color: #e0e0e0;
            --text-light: #fff;
            --bg-color: #121212;
            --card-bg: #1e1e1e;
            --border-color: #2d2d2d;
            --shadow-color: rgba(0, 0, 0, 0.5);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            max-width: 500px;
            width: 90%;
            background-color: var(--card-bg);
            border-radius: 15px;
            box-shadow: 0 10px 30px var(--shadow-color);
            overflow: hidden;
            padding: 30px;
        }
        
        .login-logo {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-logo h1 {
            font-family: 'Playfair Display', serif;
            color: var(--primary-color);
            font-size: 2.5rem;
            margin-bottom: 5px;
        }
        
        .login-logo p {
            color: var(--text-color);
            opacity: 0.8;
        }
        
        .login-form h3 {
            color: var(--primary-color);
            margin-bottom: 20px;
            font-weight: 600;
            text-align: center;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: var(--bg-color);
            color: var(--text-color);
            transition: all 0.3s;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(90, 40, 125, 0.25);
            outline: none;
        }
        
        .password-field {
            position: relative;
        }
        
        .password-toggle {
            position: absolute;
            top: 50%;
            left: 15px;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--text-color);
            cursor: pointer;
            z-index: 5;
        }
        
        .form-check {
            margin: 15px 0;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            color: white;
            padding: 12px;
            border-radius: 8px;
            width: 100%;
            font-weight: 600;
            transition: all 0.3s;
            margin-top: 10px;
        }
        
        .btn-primary:hover {
            opacity: 0.9;
            transform: translateY(-2px);
        }
        
        .login-footer {
            text-align: center;
            margin-top: 20px;
            font-size: 0.9rem;
        }
        
        .theme-toggle {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
            cursor: pointer;
        }
        
        .theme-toggle i {
            margin-left: 8px;
        }
        
        .register-link {
            text-align: center;
            margin-top: 15px;
        }
        
        .register-link a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }
        
        .register-link a:hover {
            text-decoration: underline;
        }
        
        .alert {
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 20px;
        }
        
        .alert-danger {
            background-color: rgba(220, 53, 69, 0.1);
            color: #dc3545;
            border: 1px solid rgba(220, 53, 69, 0.2);
        }
        
        .alert-success {
            background-color: rgba(40, 167, 69, 0.1);
            color: #28a745;
            border: 1px solid rgba(40, 167, 69, 0.2);
        }
        
        .spinner-border {
            width: 1rem;
            height: 1rem;
            border-width: 0.15em;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-logo">
            <h1>VelaSweets</h1>
            <p>متجر الحلويات العراقية</p>
        </div>
        
        <div class="login-form">
            <h3>تسجيل الدخول</h3>
            
            <div id="loginError" class="alert alert-danger d-none"></div>
            <div id="loginSuccess" class="alert alert-success d-none"></div>
            
            <form id="loginForm">
                <div class="form-group">
                    <label for="email">البريد الإلكتروني</label>
                    <input type="email" class="form-control" id="email" placeholder="<EMAIL>" required>
                </div>
                
                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <div class="password-field">
                        <input type="password" class="form-control" id="password" placeholder="●●●●●●●●" required>
                        <button type="button" id="togglePassword" class="password-toggle">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="rememberMe">
                    <label class="form-check-label" for="rememberMe">
                        تذكرني
                    </label>
                </div>
                
                <button type="submit" class="btn btn-primary" id="loginBtn">
                    <span id="loginSpinner" class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                    <span>تسجيل الدخول</span>
                </button>
            </form>
            
            <div class="register-link">
                ليس لديك حساب؟ <a href="register.html">إنشاء حساب جديد</a>
            </div>
            
            <div class="theme-toggle" id="themeToggle">
                <i class="bi bi-moon"></i>
                <span>تبديل الوضع الداكن</span>
            </div>
        </div>
        
        <div class="login-footer">
            جميع الحقوق محفوظة &copy; 2025 VelaSweets
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="scripts/customers.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // مستمع الحدث لنموذج تسجيل الدخول
            const loginForm = document.getElementById('loginForm');
            if (loginForm) {
                loginForm.addEventListener('submit', handleLogin);
            }
            
            // مستمع الحدث لزر إظهار/إخفاء كلمة المرور
            const togglePassword = document.getElementById('togglePassword');
            if (togglePassword) {
                togglePassword.addEventListener('click', function() {
                    const passwordInput = document.getElementById('password');
                    const icon = this.querySelector('i');
                    
                    if (passwordInput.type === 'password') {
                        passwordInput.type = 'text';
                        icon.classList.remove('fa-eye');
                        icon.classList.add('fa-eye-slash');
                    } else {
                        passwordInput.type = 'password';
                        icon.classList.remove('fa-eye-slash');
                        icon.classList.add('fa-eye');
                    }
                });
            }
            
            // مستمع الحدث لتبديل الوضع الداكن
            const themeToggle = document.getElementById('themeToggle');
            if (themeToggle) {
                themeToggle.addEventListener('click', function() {
                    const htmlElement = document.documentElement;
                    const currentTheme = htmlElement.getAttribute('data-theme');
                    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                    
                    htmlElement.setAttribute('data-theme', newTheme);
                    localStorage.setItem('theme', newTheme);
                    
                    // تحديث أيقونة التبديل
                    const icon = this.querySelector('i');
                    const text = this.querySelector('span');
                    
                    if (newTheme === 'dark') {
                        icon.classList.remove('bi-moon');
                        icon.classList.add('bi-sun');
                        text.textContent = 'تبديل الوضع الفاتح';
                    } else {
                        icon.classList.remove('bi-sun');
                        icon.classList.add('bi-moon');
                        text.textContent = 'تبديل الوضع الداكن';
                    }
                });
            }
            
            // تحقق من الوضع المحفوظ
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme) {
                document.documentElement.setAttribute('data-theme', savedTheme);
                
                if (savedTheme === 'dark' && themeToggle) {
                    const icon = themeToggle.querySelector('i');
                    const text = themeToggle.querySelector('span');
                    
                    icon.classList.remove('bi-moon');
                    icon.classList.add('bi-sun');
                    text.textContent = 'تبديل الوضع الفاتح';
                }
            }
            
            // التحقق من وجود توكن تسجيل دخول سابق
            checkLoggedIn();
            
            // التحقق من تحويل من صفحة التسجيل
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.has('registered')) {
                const successDiv = document.getElementById('loginSuccess');
                successDiv.textContent = 'تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول.';
                successDiv.classList.remove('d-none');
            }
        });
        
        // التحقق مما إذا كان المستخدم مسجل دخوله بالفعل
        function checkLoggedIn() {
            const token = localStorage.getItem('customer_token');
            
            // إذا لم يكن هناك توكن، فلا داعي للتحقق أكثر
            if (!token) return;
            
            // التحقق من صلاحية التوكن
            try {
                const payload = parseJwt(token);
                const currentTime = Math.floor(Date.now() / 1000);
                
                // التحقق من انتهاء صلاحية التوكن
                if (payload.exp < currentTime) {
                    // حذف التوكن المنتهي
                    localStorage.removeItem('customer_token');
                    localStorage.removeItem('customer_data');
                    return;
                }
                
                // توجيه المستخدم إلى الصفحة الرئيسية
                window.location.href = 'index.html';
            } catch (error) {
                localStorage.removeItem('customer_token');
                localStorage.removeItem('customer_data');
            }
        }
        
        // تحليل توكن JWT
        function parseJwt(token) {
            try {
                if (!token || typeof token !== 'string') {
                    return null;
                }

                const parts = token.split('.');
                if (parts.length !== 3) {
                    return null;
                }

                const base64Payload = parts[1];
                // فك ترميز Base64 ثم فك ترميز URI إذا لزم الأمر
                let payload;
                try {
                    payload = atob(base64Payload);
                } catch (e) {
                    // محاولة فك ترميز URI أولاً ثم Base64
                    payload = decodeURIComponent(atob(base64Payload));
                }

                return JSON.parse(payload);
            } catch (error) {
                console.error('خطأ في تحليل التوكن:', error);
                return null;
            }
        }
        
        // معالجة تسجيل الدخول
        function handleLogin(event) {
            event.preventDefault();
            
            // الحصول على قيم الحقول
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;
            
            const errorDiv = document.getElementById('loginError');
            const successDiv = document.getElementById('loginSuccess');
            const loginBtn = document.getElementById('loginBtn');
            const loginSpinner = document.getElementById('loginSpinner');
            
            // إظهار مؤشر التحميل
            loginSpinner.classList.remove('d-none');
            loginBtn.setAttribute('disabled', 'disabled');
            
            // إخفاء رسائل الخطأ والنجاح السابقة
            errorDiv.classList.add('d-none');
            successDiv.classList.add('d-none');
            
            // التحقق من ملء الحقول
            if (!email || !password) {
                errorDiv.textContent = 'يرجى ملء جميع الحقول المطلوبة';
                errorDiv.classList.remove('d-none');
                // إخفاء مؤشر التحميل
                loginSpinner.classList.add('d-none');
                loginBtn.removeAttribute('disabled');
                return;
            }
            
            // إرسال طلب تسجيل الدخول إلى الخادم (محاكاة)
            setTimeout(() => {
                try {
                    // التحقق من وجود دالة المصادقة
                    if (typeof authenticateCustomer === 'function') {
                        // استدعاء دالة تسجيل الدخول من ملف customers.js
                        const customer = authenticateCustomer(email, password);
                        
                        if (customer) {
                            // إنشاء توكن JWT بسيط
                            const header = {
                                alg: 'HS256',
                                typ: 'JWT'
                            };
                            
                            const payload = {
                                sub: customer.id,
                                name: customer.fullName,
                                email: customer.email,
                                iat: Math.floor(Date.now() / 1000),
                                exp: Math.floor(Date.now() / 1000) + (rememberMe ? 30 * 24 * 60 * 60 : 24 * 60 * 60)
                            };
                            
                            // تحويل الرأس والحمولة إلى سلاسل مشفرة بـ Base64
                            const encodedHeader = btoa(encodeURIComponent(JSON.stringify(header)));
                            const encodedPayload = btoa(encodeURIComponent(JSON.stringify(payload)));
                            
                            // التوقيع
                            const signature = btoa(encodeURIComponent(`${encodedHeader}.${encodedPayload}`));
                            
                            // إنشاء التوكن الكامل
                            const token = `${encodedHeader}.${encodedPayload}.${signature}`;
                            
                            // حفظ توكن المصادقة وبيانات العميل
                            localStorage.setItem('customer_token', token);
                            localStorage.setItem('customer_data', JSON.stringify(customer));
                            
                            // عرض رسالة النجاح
                            successDiv.textContent = 'تم تسجيل الدخول بنجاح! جاري التحويل...';
                            successDiv.classList.remove('d-none');
                            
                            // توجيه المستخدم إلى الصفحة الرئيسية بعد 2 ثانية
                            setTimeout(() => {
                                window.location.href = 'index.html';
                            }, 2000);
                        } else {
                            // عرض رسالة الخطأ
                            errorDiv.textContent = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
                            errorDiv.classList.remove('d-none');
                            // إخفاء مؤشر التحميل
                            loginSpinner.classList.add('d-none');
                            loginBtn.removeAttribute('disabled');
                        }
                    } else {
                        // البحث في قاعدة البيانات المحلية
                        const customers = JSON.parse(localStorage.getItem('customers') || '[]');
                        const customer = customers.find(c =>
                            c.email.toLowerCase() === email.toLowerCase() &&
                            c.password === password
                        );

                        if (customer) {
                            // إنشاء توكن JWT بسيط
                            const token = btoa(encodeURIComponent(JSON.stringify({
                                userId: customer.id,
                                name: customer.fullName,
                                email: customer.email,
                                role: customer.role || 'customer',
                                exp: Math.floor(Date.now() / 1000) + (rememberMe ? 30 * 24 * 60 * 60 : 24 * 60 * 60)
                            })));

                            // حفظ توكن المصادقة وبيانات العميل
                            localStorage.setItem('customer_token', token);
                            localStorage.setItem('customer_data', JSON.stringify({
                                id: customer.id,
                                fullName: customer.fullName,
                                email: customer.email,
                                phone: customer.phone,
                                province: customer.province,
                                address: customer.address,
                                role: customer.role || 'customer'
                            }));

                            // عرض رسالة النجاح
                            successDiv.textContent = 'تم تسجيل الدخول بنجاح! جاري التحويل...';
                            successDiv.classList.remove('d-none');

                            // توجيه المستخدم
                            setTimeout(() => {
                                const urlParams = new URLSearchParams(window.location.search);
                                const redirectUrl = urlParams.get('redirect') || 'index.html';
                                window.location.href = redirectUrl;
                            }, 2000);
                        } else {
                            // عرض رسالة الخطأ
                            errorDiv.textContent = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
                            errorDiv.classList.remove('d-none');
                            // إخفاء مؤشر التحميل
                            loginSpinner.classList.add('d-none');
                            loginBtn.removeAttribute('disabled');
                        }
                    }
                } catch (error) {
                    console.error('خطأ في عملية تسجيل الدخول:', error);
                    errorDiv.textContent = 'حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.';
                    errorDiv.classList.remove('d-none');
                    // إخفاء مؤشر التحميل
                    loginSpinner.classList.add('d-none');
                    loginBtn.removeAttribute('disabled');
                }
            }, 1000);
        }
    </script>
</body>
</html> 