<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلباتي - متجر VelaSweets</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap');
        
        :root {
            --primary-color: #5a287d;
            --secondary-color: #f8a5c2;
            --tertiary-color: #b07d28;
            --text-color: #333;
            --bg-color: #f8f9fa;
            --card-bg: #fff;
            --border-color: #dee2e6;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
        }
        
        .navbar-custom {
            background-color: white;
            box-shadow: 0 2px 10px var(--shadow-color);
            padding: 15px 0;
        }
        
        .navbar-brand {
            font-family: 'Playfair Display', serif;
            color: var(--primary-color);
            font-size: 1.8rem;
            font-weight: 600;
        }
        
        .nav-link {
            color: var(--text-color);
            font-weight: 500;
            margin: 0 10px;
            transition: all 0.3s;
        }
        
        .nav-link:hover, .nav-link.active {
            color: var(--primary-color);
        }
        
        .orders-section {
            padding: 60px 0;
            min-height: 70vh;
        }
        
        .page-title {
            font-family: 'Playfair Display', serif;
            color: var(--primary-color);
            margin-bottom: 40px;
            text-align: center;
        }
        
        .order-card {
            background-color: var(--card-bg);
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px var(--shadow-color);
            transition: all 0.3s;
        }
        
        .order-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px var(--shadow-color);
        }
        
        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .order-id {
            font-weight: 700;
            color: var(--primary-color);
            font-size: 1.1rem;
        }
        
        .order-date {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .order-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-pending {
            background-color: rgba(255, 193, 7, 0.1);
            color: var(--warning-color);
            border: 1px solid rgba(255, 193, 7, 0.3);
        }
        
        .status-processing {
            background-color: rgba(23, 162, 184, 0.1);
            color: var(--info-color);
            border: 1px solid rgba(23, 162, 184, 0.3);
        }
        
        .status-completed {
            background-color: rgba(40, 167, 69, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(40, 167, 69, 0.3);
        }
        
        .status-cancelled {
            background-color: rgba(220, 53, 69, 0.1);
            color: var(--danger-color);
            border: 1px solid rgba(220, 53, 69, 0.3);
        }
        
        .order-items {
            margin-bottom: 20px;
        }
        
        .order-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f8f9fa;
        }
        
        .order-item:last-child {
            border-bottom: none;
        }
        
        .item-image {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            object-fit: cover;
            margin-left: 15px;
        }
        
        .item-details {
            flex: 1;
        }
        
        .item-name {
            font-weight: 600;
            margin-bottom: 2px;
        }
        
        .item-quantity {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .item-price {
            font-weight: 700;
            color: var(--tertiary-color);
        }
        
        .order-total {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 15px;
            border-top: 1px solid var(--border-color);
        }
        
        .total-label {
            font-weight: 600;
            color: var(--text-color);
        }
        
        .total-amount {
            font-weight: 700;
            font-size: 1.2rem;
            color: var(--primary-color);
        }
        
        .order-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .btn-reorder {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            font-size: 0.9rem;
            transition: all 0.3s;
        }
        
        .btn-reorder:hover {
            background-color: var(--tertiary-color);
            transform: translateY(-1px);
        }
        
        .btn-cancel {
            background-color: var(--danger-color);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            font-size: 0.9rem;
            transition: all 0.3s;
        }
        
        .btn-cancel:hover {
            background-color: #c82333;
            transform: translateY(-1px);
        }
        
        .empty-orders {
            text-align: center;
            padding: 60px 20px;
        }
        
        .empty-orders-icon {
            font-size: 4rem;
            color: var(--border-color);
            margin-bottom: 20px;
        }
        
        .empty-orders h3 {
            color: var(--text-color);
            margin-bottom: 15px;
        }
        
        .empty-orders p {
            color: #6c757d;
            margin-bottom: 30px;
        }
        
        .start-shopping {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .start-shopping:hover {
            background-color: var(--tertiary-color);
            color: white;
            transform: translateY(-2px);
        }
        
        .cart-icon {
            position: relative;
        }
        
        .cart-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background-color: var(--secondary-color);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .user-welcome {
            display: none;
        }
        
        .user-welcome.logged-in {
            display: block;
        }
        
        .footer {
            background-color: var(--primary-color);
            color: white;
            padding: 40px 0 20px;
            margin-top: 60px;
        }
        
        .footer-title {
            font-family: 'Playfair Display', serif;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }
        
        .footer-links {
            list-style: none;
            padding: 0;
        }
        
        .footer-links li {
            margin-bottom: 8px;
        }
        
        .footer-links a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .footer-links a:hover {
            color: white;
            padding-right: 5px;
        }
        
        .footer-bottom {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 20px;
            margin-top: 30px;
            text-align: center;
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
        }
        
        @media (max-width: 768px) {
            .order-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .order-actions {
                flex-direction: column;
            }
            
            .order-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .item-image {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="index.html">VelaSweets</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.html">المنتجات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html">من نحن</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.html">اتصل بنا</a>
                    </li>
                </ul>
                <div class="d-flex align-items-center">
                    <div class="user-welcome ms-3" id="userWelcome">
                        <span>مرحباً، <strong id="userName">زائر</strong></span>
                    </div>
                    <a href="cart.html" class="nav-link ms-3 cart-icon">
                        <i class="bi bi-cart3 fs-5"></i>
                        <span class="cart-badge" id="cartBadge">0</span>
                    </a>
                    <div id="authButtons">
                        <a href="login.html" class="btn btn-outline-primary ms-2">تسجيل الدخول</a>
                        <a href="register.html" class="btn btn-primary">إنشاء حساب</a>
                    </div>
                    <div id="userDropdown" style="display:none;">
                        <div class="dropdown">
                            <button class="btn btn-outline-primary dropdown-toggle" type="button" id="userMenuButton" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle me-1"></i>
                                حسابي
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="profile.html">الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="orders.html">طلباتي</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" id="logoutBtn">تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- Orders Section -->
    <section class="orders-section">
        <div class="container">
            <h1 class="page-title">طلباتي</h1>
            
            <div id="ordersContent">
                <!-- Orders content will be loaded here -->
            </div>
        </div>
    </section>
    
    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h3 class="footer-title">VelaSweets</h3>
                    <p>متجر متخصص في تقديم أشهى الحلويات الشرقية والغربية المصنوعة من أجود الخامات.</p>
                </div>
                <div class="col-md-3">
                    <h4 class="footer-title">روابط سريعة</h4>
                    <ul class="footer-links">
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="products.html">المنتجات</a></li>
                        <li><a href="about.html">من نحن</a></li>
                        <li><a href="contact.html">اتصل بنا</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h4 class="footer-title">خدمة العملاء</h4>
                    <ul class="footer-links">
                        <li><a href="#">الأسئلة الشائعة</a></li>
                        <li><a href="#">سياسة الخصوصية</a></li>
                        <li><a href="#">شروط الاستخدام</a></li>
                        <li><a href="#">سياسة الإرجاع</a></li>
                    </ul>
                </div>
                <div class="col-md-2">
                    <h4 class="footer-title">تواصل معنا</h4>
                    <ul class="footer-links">
                        <li><i class="bi bi-geo-alt me-2"></i> بغداد، العراق</li>
                        <li><i class="bi bi-telephone me-2"></i> +964 123 456 789</li>
                        <li><i class="bi bi-envelope me-2"></i> <EMAIL></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>جميع الحقوق محفوظة &copy; 2025 VelaSweets</p>
            </div>
        </div>
    </footer>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="scripts/auth.js"></script>
    <script>
        let orders = [];
        let cart = JSON.parse(localStorage.getItem('cart')) || [];

        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من حالة تسجيل الدخول
            if (!checkLoggedIn()) {
                window.location.href = 'login.html?redirect=orders.html';
                return;
            }

            // تحميل الطلبات
            loadOrders();

            // تحديث عداد السلة
            updateCartBadge();

            // إعداد مستمع حدث تسجيل الخروج
            const logoutBtn = document.getElementById('logoutBtn');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    localStorage.removeItem('customer_token');
                    localStorage.removeItem('customer_data');
                    window.location.href = 'login.html';
                });
            }
        });

        function checkLoggedIn() {
            const token = localStorage.getItem('customer_token');
            const authButtons = document.getElementById('authButtons');
            const userDropdown = document.getElementById('userDropdown');
            const userWelcome = document.getElementById('userWelcome');
            const userName = document.getElementById('userName');

            if (!token) {
                if (authButtons) authButtons.style.display = 'block';
                if (userDropdown) userDropdown.style.display = 'none';
                if (userWelcome) userWelcome.classList.remove('logged-in');
                return false;
            }

            try {
                const payload = parseJwt(token);
                const currentTime = Math.floor(Date.now() / 1000);

                if (!payload || payload.exp < currentTime) {
                    localStorage.removeItem('customer_token');
                    localStorage.removeItem('customer_data');

                    if (authButtons) authButtons.style.display = 'block';
                    if (userDropdown) userDropdown.style.display = 'none';
                    if (userWelcome) userWelcome.classList.remove('logged-in');
                    return false;
                }

                if (authButtons) authButtons.style.display = 'none';
                if (userDropdown) userDropdown.style.display = 'block';

                const userData = JSON.parse(localStorage.getItem('customer_data') || '{}');
                if (userName) {
                    userName.textContent = userData.fullName || userData.name || 'العميل';
                    if (userWelcome) userWelcome.classList.add('logged-in');
                }

                return true;

            } catch (error) {
                localStorage.removeItem('customer_token');
                localStorage.removeItem('customer_data');

                if (authButtons) authButtons.style.display = 'block';
                if (userDropdown) userDropdown.style.display = 'none';
                if (userWelcome) userWelcome.classList.remove('logged-in');
                return false;
            }
        }

        function parseJwt(token) {
            try {
                if (!token || typeof token !== 'string') {
                    return null;
                }

                const parts = token.split('.');
                if (parts.length !== 3) {
                    return null;
                }

                const base64Payload = parts[1];
                let payload;
                try {
                    payload = atob(base64Payload);
                } catch (e) {
                    payload = decodeURIComponent(atob(base64Payload));
                }

                return JSON.parse(payload);
            } catch (error) {
                console.error('خطأ في تحليل التوكن:', error);
                return null;
            }
        }

        function loadOrders() {
            orders = JSON.parse(localStorage.getItem('customer_orders') || '[]');

            // ترتيب الطلبات حسب التاريخ (الأحدث أولاً)
            orders.sort((a, b) => new Date(b.date) - new Date(a.date));

            displayOrders();
        }

        function displayOrders() {
            const ordersContent = document.getElementById('ordersContent');

            if (orders.length === 0) {
                ordersContent.innerHTML = `
                    <div class="empty-orders">
                        <i class="bi bi-bag-x empty-orders-icon"></i>
                        <h3>لا توجد طلبات</h3>
                        <p>لم تقم بإجراء أي طلبات بعد</p>
                        <a href="products.html" class="start-shopping">
                            <i class="bi bi-arrow-right me-2"></i>
                            ابدأ التسوق الآن
                        </a>
                    </div>
                `;
                return;
            }

            ordersContent.innerHTML = orders.map(order => createOrderHTML(order)).join('');
        }

        function createOrderHTML(order) {
            const orderDate = new Date(order.date);
            const formattedDate = orderDate.toLocaleDateString('ar-EG', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });

            const statusText = getStatusText(order.status);
            const statusClass = `status-${order.status}`;
            const total = (order.total + (order.shipping || 0)).toFixed(2);

            return `
                <div class="order-card">
                    <div class="order-header">
                        <div>
                            <div class="order-id">طلب رقم: #${order.id}</div>
                            <div class="order-date">${formattedDate}</div>
                        </div>
                        <div class="order-status ${statusClass}">
                            ${statusText}
                        </div>
                    </div>

                    <div class="order-items">
                        ${order.items.map(item => `
                            <div class="order-item">
                                <img src="${item.image}" alt="${item.name}" class="item-image">
                                <div class="item-details">
                                    <div class="item-name">${item.name}</div>
                                    <div class="item-quantity">الكمية: ${item.quantity}</div>
                                </div>
                                <div class="item-price">$${(item.price * item.quantity).toFixed(2)}</div>
                            </div>
                        `).join('')}
                    </div>

                    <div class="order-total">
                        <span class="total-label">المجموع الكلي:</span>
                        <span class="total-amount">$${total}</span>
                    </div>

                    <div class="order-actions">
                        <button class="btn-reorder" onclick="reorderItems(${order.id})">
                            <i class="bi bi-arrow-repeat me-1"></i>
                            إعادة الطلب
                        </button>
                        ${order.status === 'pending' ? `
                            <button class="btn-cancel" onclick="cancelOrder(${order.id})">
                                <i class="bi bi-x-circle me-1"></i>
                                إلغاء الطلب
                            </button>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        function getStatusText(status) {
            const statusMap = {
                'pending': 'قيد المراجعة',
                'processing': 'قيد التحضير',
                'completed': 'مكتمل',
                'cancelled': 'ملغي'
            };
            return statusMap[status] || status;
        }

        function reorderItems(orderId) {
            const order = orders.find(o => o.id === orderId);
            if (!order) return;

            // إضافة جميع عناصر الطلب إلى السلة
            order.items.forEach(item => {
                const existingItem = cart.find(cartItem => cartItem.id === item.id);
                if (existingItem) {
                    existingItem.quantity += item.quantity;
                } else {
                    cart.push({
                        id: item.id,
                        name: item.name,
                        price: item.price,
                        image: item.image,
                        quantity: item.quantity
                    });
                }
            });

            localStorage.setItem('cart', JSON.stringify(cart));
            updateCartBadge();
            showToast('تم إضافة جميع عناصر الطلب إلى السلة بنجاح!');
        }

        function cancelOrder(orderId) {
            if (!confirm('هل أنت متأكد من إلغاء هذا الطلب؟')) {
                return;
            }

            const orderIndex = orders.findIndex(o => o.id === orderId);
            if (orderIndex !== -1) {
                orders[orderIndex].status = 'cancelled';
                localStorage.setItem('customer_orders', JSON.stringify(orders));
                displayOrders();
                showToast('تم إلغاء الطلب بنجاح');
            }
        }

        function updateCartBadge() {
            const badge = document.getElementById('cartBadge');
            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
            if (badge) {
                badge.textContent = totalItems;
            }
        }

        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = 'toast-notification';
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? 'var(--success-color)' : 'var(--danger-color)'};
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 9999;
                transform: translateX(100%);
                transition: transform 0.3s ease;
                max-width: 300px;
            `;
            toast.textContent = message;

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 100);

            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }
    </script>
