<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 لوحة اختبار النظام الإدارية - VelaSweets</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap');
        
        :root {
            --primary-color: #5a287d;
            --secondary-color: #f8a5c2;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --dark-color: #343a40;
            --light-color: #f8f9fa;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            padding: 20px;
            color: var(--dark-color);
        }
        
        .admin-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            overflow: hidden;
        }
        
        .admin-header {
            background: linear-gradient(135deg, var(--primary-color), #6a3093);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        
        .admin-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        
        .admin-header h1 {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }
        
        .admin-header p {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        
        .control-panel {
            background: var(--light-color);
            padding: 20px 30px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .system-status {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 15px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .status-indicator.online {
            background: rgba(40, 167, 69, 0.1);
            color: var(--success-color);
            border: 2px solid rgba(40, 167, 69, 0.3);
        }
        
        .status-indicator.testing {
            background: rgba(255, 193, 7, 0.1);
            color: var(--warning-color);
            border: 2px solid rgba(255, 193, 7, 0.3);
            animation: pulse 2s infinite;
        }
        
        .status-indicator.error {
            background: rgba(220, 53, 69, 0.1);
            color: var(--danger-color);
            border: 2px solid rgba(220, 53, 69, 0.3);
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        .control-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn-admin {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-admin.primary {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-admin.primary:hover {
            background: #4a1f6b;
            transform: translateY(-2px);
        }
        
        .btn-admin.success {
            background: var(--success-color);
            color: white;
        }
        
        .btn-admin.success:hover {
            background: #218838;
            transform: translateY(-2px);
        }
        
        .btn-admin.warning {
            background: var(--warning-color);
            color: var(--dark-color);
        }
        
        .btn-admin.warning:hover {
            background: #e0a800;
            transform: translateY(-2px);
        }
        
        .btn-admin.danger {
            background: var(--danger-color);
            color: white;
        }
        
        .btn-admin.danger:hover {
            background: #c82333;
            transform: translateY(-2px);
        }
        
        .test-content {
            padding: 30px;
        }
        
        .test-section {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            margin-bottom: 25px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .test-section:hover {
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .test-section.success {
            border-color: var(--success-color);
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.05), rgba(40, 167, 69, 0.02));
        }
        
        .test-section.error {
            border-color: var(--danger-color);
            background: linear-gradient(135deg, rgba(220, 53, 69, 0.05), rgba(220, 53, 69, 0.02));
        }
        
        .test-section.warning {
            border-color: var(--warning-color);
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.05), rgba(255, 193, 7, 0.02));
        }
        
        .section-header {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 20px 25px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .section-status {
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 600;
        }
        
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-badge.success {
            background: var(--success-color);
            color: white;
        }
        
        .status-badge.error {
            background: var(--danger-color);
            color: white;
        }
        
        .status-badge.warning {
            background: var(--warning-color);
            color: var(--dark-color);
        }
        
        .status-badge.testing {
            background: var(--info-color);
            color: white;
            animation: pulse 1.5s infinite;
        }
        
        .test-items {
            padding: 25px;
        }
        
        .test-item {
            background: white;
            border: 2px solid #f0f0f0;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .test-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .test-item.success {
            border-color: var(--success-color);
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.05), rgba(40, 167, 69, 0.02));
        }

        .test-item.error {
            border-color: var(--danger-color);
            background: linear-gradient(135deg, rgba(220, 53, 69, 0.05), rgba(220, 53, 69, 0.02));
        }

        .test-item.warning {
            border-color: var(--warning-color);
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.05), rgba(255, 193, 7, 0.02));
        }
        
        .test-description {
            flex: 1;
            font-weight: 500;
            color: var(--dark-color);
        }
        
        .test-result {
            display: flex;
            align-items: center;
            gap: 10px;
            min-width: 120px;
            justify-content: flex-end;
        }
        
        .result-icon {
            font-size: 1.2rem;
            font-weight: bold;
        }
        
        .result-icon.success {
            color: var(--success-color);
        }
        
        .result-icon.error {
            color: var(--danger-color);
        }
        
        .result-icon.warning {
            color: var(--warning-color);
        }
        
        .result-icon.testing {
            color: var(--info-color);
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .result-text {
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .error-details {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid rgba(220, 53, 69, 0.3);
            border-radius: 8px;
            padding: 12px;
            margin-top: 10px;
            font-size: 0.85rem;
            color: var(--danger-color);
            display: none;
        }
        
        .error-details.show {
            display: block;
            animation: slideDown 0.3s ease;
        }
        
        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .summary-panel {
            background: linear-gradient(135deg, var(--primary-color), #6a3093);
            color: white;
            padding: 30px;
            margin-top: 30px;
            border-radius: 15px;
            text-align: center;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }
        
        .stat-card {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 12px;
            backdrop-filter: blur(10px);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .progress-ring {
            width: 120px;
            height: 120px;
            margin: 0 auto 20px;
        }
        
        .progress-ring circle {
            fill: none;
            stroke-width: 8;
            stroke-linecap: round;
            transform: rotate(-90deg);
            transform-origin: 50% 50%;
        }
        
        .progress-ring .background {
            stroke: rgba(255,255,255,0.2);
        }
        
        .progress-ring .progress {
            stroke: white;
            stroke-dasharray: 0 377;
            transition: stroke-dasharray 1s ease;
        }
        
        .responsive-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
        }
        
        @media (max-width: 768px) {
            .admin-container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .admin-header {
                padding: 20px;
            }
            
            .admin-header h1 {
                font-size: 2rem;
            }
            
            .control-panel {
                padding: 15px 20px;
                flex-direction: column;
                align-items: stretch;
            }
            
            .system-status {
                justify-content: center;
                margin-bottom: 15px;
            }
            
            .control-buttons {
                justify-content: center;
            }
            
            .test-content {
                padding: 20px;
            }
            
            .responsive-grid {
                grid-template-columns: 1fr;
            }
            
            .summary-stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .loading-content {
            background: white;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            max-width: 500px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .progress-bar-container {
            width: 100%;
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 4px;
        }

        .current-test {
            font-size: 0.9rem;
            color: var(--primary-color);
            margin-top: 10px;
            font-weight: 500;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        .test-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .test-title {
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--dark-color);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .test-status-badge {
            padding: 8px 15px;
            border-radius: 25px;
            font-size: 0.8rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .test-status-badge.success {
            background: linear-gradient(135deg, var(--success-color), #20c997);
            color: white;
            box-shadow: 0 3px 10px rgba(40, 167, 69, 0.3);
            border-color: rgba(40, 167, 69, 0.2);
        }

        .test-status-badge.error {
            background: linear-gradient(135deg, var(--danger-color), #e74c3c);
            color: white;
            box-shadow: 0 3px 10px rgba(220, 53, 69, 0.3);
            border-color: rgba(220, 53, 69, 0.2);
        }

        .test-status-badge.warning {
            background: linear-gradient(135deg, var(--warning-color), #f39c12);
            color: var(--dark-color);
            box-shadow: 0 3px 10px rgba(255, 193, 7, 0.3);
            border-color: rgba(255, 193, 7, 0.2);
        }

        .test-status-badge.testing {
            background: linear-gradient(135deg, var(--info-color), #3498db);
            color: white;
            animation: pulse 1.5s infinite;
            box-shadow: 0 3px 10px rgba(23, 162, 184, 0.3);
            border-color: rgba(23, 162, 184, 0.2);
        }

        .test-details {
            margin-top: 15px;
        }

        .test-detail-item {
            margin-bottom: 12px;
            padding: 10px;
            border-radius: 8px;
            font-size: 0.9rem;
        }

        .test-detail-item.technical {
            background: rgba(23, 162, 184, 0.1);
            border-left: 4px solid var(--info-color);
            color: #0c5460;
        }

        .test-detail-item.purpose {
            background: rgba(111, 66, 193, 0.1);
            border-left: 4px solid #6f42c1;
            color: #4a2c7a;
        }

        .test-detail-item.impact {
            background: rgba(220, 53, 69, 0.1);
            border-left: 4px solid var(--danger-color);
            color: #721c24;
        }

        .test-detail-item.solution {
            background: rgba(255, 193, 7, 0.1);
            border-left: 4px solid var(--warning-color);
            color: #856404;
        }

        .detail-label {
            font-weight: 600;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .detail-content {
            line-height: 1.5;
        }

        .problems-summary {
            background: linear-gradient(135deg, #fff5f5, #fed7d7);
            border: 2px solid var(--danger-color);
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
            display: none;
        }

        .problems-summary.show {
            display: block;
            animation: slideDown 0.5s ease;
        }

        .problem-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .problem-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .problem-title {
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--danger-color);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .problem-severity {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .severity-critical {
            background: var(--danger-color);
            color: white;
        }

        .severity-high {
            background: #fd7e14;
            color: white;
        }

        .severity-medium {
            background: var(--warning-color);
            color: var(--dark-color);
        }

        .severity-low {
            background: var(--info-color);
            color: white;
        }

        .copy-solutions-btn {
            background: linear-gradient(135deg, var(--success-color), #20c997);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .copy-solutions-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        /* Smart Analysis Panel Styles */
        .smart-analysis-panel {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 30px;
            margin-top: 30px;
            color: white;
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .smart-analysis-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="brain-pattern" width="50" height="50" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="2" fill="rgba(255,255,255,0.1)"/><path d="M10,10 Q25,5 40,10 Q35,25 40,40 Q25,35 10,40 Q15,25 10,10" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23brain-pattern)"/></svg>');
            opacity: 0.3;
        }

        .smart-analysis-panel h3 {
            font-size: 2rem;
            font-weight: 800;
            margin-bottom: 25px;
            position: relative;
            z-index: 1;
            text-align: center;
        }

        .analysis-summary {
            margin-bottom: 30px;
            position: relative;
            z-index: 1;
        }

        .analysis-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .analysis-stat-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .analysis-stat-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.25);
        }

        .analysis-stat-card.critical {
            border-left: 4px solid #ff4757;
        }

        .analysis-stat-card.warning {
            border-left: 4px solid #ffa502;
        }

        .analysis-stat-card.info {
            border-left: 4px solid #3742fa;
        }

        .analysis-stat-card.success {
            border-left: 4px solid #2ed573;
        }

        .analysis-stat-card .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 10px;
            opacity: 0.8;
        }

        .analysis-stat-card .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 5px;
        }

        .analysis-stat-card .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .category-breakdown,
        .root-causes-analysis,
        .priority-fix-plan {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            position: relative;
            z-index: 1;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .category-breakdown h4,
        .root-causes-analysis h4,
        .priority-fix-plan h4 {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 20px;
            color: rgba(255, 255, 255, 0.95);
        }

        .category-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .category-item:last-child {
            border-bottom: none;
        }

        .category-name {
            font-weight: 600;
        }

        .category-count {
            background: rgba(255, 255, 255, 0.2);
            padding: 5px 12px;
            border-radius: 20px;
            font-weight: 700;
        }

        .root-cause-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #ffa502;
        }

        .root-cause-title {
            font-weight: 700;
            margin-bottom: 8px;
            color: #ffa502;
        }

        .root-cause-description {
            opacity: 0.9;
            line-height: 1.5;
        }

        .fix-step {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #2ed573;
            position: relative;
        }

        .fix-step-number {
            position: absolute;
            top: -10px;
            left: 15px;
            background: #2ed573;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 0.9rem;
        }

        .fix-step-title {
            font-weight: 700;
            margin-bottom: 10px;
            margin-top: 10px;
            color: #2ed573;
        }

        .fix-step-description {
            opacity: 0.9;
            line-height: 1.5;
            margin-bottom: 10px;
        }

        .fix-step-action {
            background: rgba(46, 213, 115, 0.2);
            border-radius: 8px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            border: 1px solid rgba(46, 213, 115, 0.3);
        }

        .analysis-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            position: relative;
            z-index: 1;
        }
    </style>
</head>
<body>
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h4 id="loadingTitle">جاري تشغيل الفحص الذكي...</h4>
            <div class="progress-bar-container">
                <div class="progress-bar" id="progressBar"></div>
            </div>
            <p id="loadingMessage">يرجى الانتظار حتى اكتمال فحص النظام (42 اختبار)</p>
            <div class="current-test" id="currentTest">التحضير للفحص...</div>
        </div>
    </div>

    <div class="admin-container">
        <!-- Header -->
        <div class="admin-header">
            <h1><i class="fas fa-microscope me-3"></i>نظام الفحص الذكي والتشخيص المتقدم</h1>
            <p>أداة إدارية احترافية لفحص وتشخيص وإصلاح جميع مكونات نظام VelaSweets</p>
        </div>

        <!-- Control Panel -->
        <div class="control-panel">
            <div class="system-status">
                <div class="status-indicator online" id="systemStatus">
                    <i class="fas fa-circle"></i>
                    <span>النظام متصل</span>
                </div>
                <div id="lastTestTime">
                    آخر فحص: لم يتم بعد
                </div>
            </div>
            
            <div class="control-buttons">
                <button class="btn-admin primary" onclick="runFullSystemTest()">
                    <i class="fas fa-microscope"></i>
                    تشغيل فحص شامل ذكي
                </button>
                <button class="btn-admin warning" onclick="resetSystemData()">
                    <i class="fas fa-refresh"></i>
                    إعادة تهيئة النظام
                </button>
                <button class="btn-admin danger" onclick="clearAllCache()">
                    <i class="fas fa-trash"></i>
                    مسح الكاش
                </button>
                <button class="btn-admin success" onclick="exportSolutions()" style="display: none;" id="exportBtn">
                    <i class="fas fa-download"></i>
                    نسخ الحلول المقترحة
                </button>
            </div>
        </div>

        <!-- Test Content -->
        <div class="test-content">
            <div class="responsive-grid" id="testSections">
                <!-- Test sections will be dynamically generated here -->
            </div>

            <!-- Summary Panel -->
            <div class="summary-panel" id="summaryPanel" style="display: none;">
                <h3><i class="fas fa-chart-pie me-3"></i>ملخص نتائج الفحص</h3>
                
                <div class="summary-stats">
                    <div class="stat-card">
                        <div class="stat-number" id="totalTests">0</div>
                        <div class="stat-label">إجمالي الاختبارات</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="passedTests">0</div>
                        <div class="stat-label">نجح</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="failedTests">0</div>
                        <div class="stat-label">فشل</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="successRate">0%</div>
                        <div class="stat-label">معدل النجاح</div>
                    </div>
                </div>

                <svg class="progress-ring" id="progressRing">
                    <circle class="background" cx="60" cy="60" r="54"></circle>
                    <circle class="progress" cx="60" cy="60" r="54" id="progressCircle"></circle>
                </svg>
                
                <h4 id="overallStatus">جاري الفحص...</h4>
                <p id="statusMessage">يرجى الانتظار حتى اكتمال جميع الاختبارات</p>
            </div>

            <!-- Smart Analysis Panel -->
            <div class="smart-analysis-panel" id="smartAnalysisPanel" style="display: none;">
                <h3><i class="fas fa-brain me-3"></i>التحليل الذكي والتشخيص المتقدم</h3>

                <!-- Analysis Summary -->
                <div class="analysis-summary">
                    <div class="analysis-stats">
                        <div class="analysis-stat-card critical">
                            <div class="stat-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="criticalIssuesCount">0</div>
                                <div class="stat-label">مشاكل حرجة</div>
                            </div>
                        </div>
                        <div class="analysis-stat-card warning">
                            <div class="stat-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="rootCausesCount">0</div>
                                <div class="stat-label">أسباب جذرية</div>
                            </div>
                        </div>
                        <div class="analysis-stat-card info">
                            <div class="stat-icon">
                                <i class="fas fa-tools"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="fixStepsCount">0</div>
                                <div class="stat-label">خطوات إصلاح</div>
                            </div>
                        </div>
                        <div class="analysis-stat-card success">
                            <div class="stat-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="systemHealthScore">0%</div>
                                <div class="stat-label">صحة النظام</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Category Breakdown -->
                <div class="category-breakdown">
                    <h4><i class="fas fa-chart-pie me-2"></i>توزيع المشاكل حسب الفئة</h4>
                    <div id="categoryBreakdownContent"></div>
                </div>

                <!-- Root Causes -->
                <div class="root-causes-analysis">
                    <h4><i class="fas fa-microscope me-2"></i>الأسباب الجذرية المكتشفة</h4>
                    <div id="rootCausesContent"></div>
                </div>

                <!-- Priority Fix Plan -->
                <div class="priority-fix-plan">
                    <h4><i class="fas fa-list-ol me-2"></i>خطة الإصلاح المرتبة بالأولوية</h4>
                    <div id="priorityFixPlanContent"></div>
                </div>

                <!-- Action Buttons -->
                <div class="analysis-actions">
                    <button class="btn-admin success" onclick="copyAnalysisToClipboard()">
                        <i class="fas fa-copy"></i>
                        نسخ التحليل للذكاء الاصطناعي
                    </button>
                    <button class="btn-admin primary" onclick="generateDetailedReport()">
                        <i class="fas fa-file-alt"></i>
                        تقرير مفصل
                    </button>
                    <button class="btn-admin warning" onclick="exportFixPlan()">
                        <i class="fas fa-download"></i>
                        تصدير خطة الإصلاح
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="scripts/customer-auth.js"></script>
    <script src="scripts/currency.js"></script>
    <script src="scripts/language.js"></script>
    <script src="database/products.js"></script>
    <script src="init-data.js"></script>
    <script src="final-setup.js"></script>

    <script>
        // نظام اختبار شامل ومتقدم لـ VelaSweets
        class SystemTester {
            constructor() {
                this.testResults = {
                    total: 0,
                    passed: 0,
                    failed: 0,
                    warnings: 0
                };

                this.detailedResults = [];
                this.problemsFound = [];

                this.testSections = [
                    {
                        id: 'auth',
                        title: 'نظام المصادقة والحماية',
                        icon: 'fas fa-shield-alt',
                        tests: [
                            {
                                name: 'تحميل ملفات المصادقة',
                                method: 'testAuthFilesLoaded',
                                technical: 'فحص وجود دوال registerCustomer, authenticateCustomer, validateInput في النطاق العام',
                                purpose: 'ضمان تحميل جميع مكونات نظام المصادقة بشكل صحيح',
                                impact: 'عدم عمل تسجيل الدخول أو إنشاء الحسابات',
                                solution: 'تحقق من تحميل ملف scripts/customer-auth.js وترتيب تحميل السكربتات'
                            },
                            {
                                name: 'تسجيل حساب جديد',
                                method: 'testUserRegistration',
                                technical: 'اختبار إنشاء حساب جديد ببيانات صحيحة وحفظه في localStorage',
                                purpose: 'ضمان عمل عملية التسجيل للعملاء الجدد',
                                impact: 'عدم قدرة العملاء على إنشاء حسابات جديدة',
                                solution: 'راجع دالة registerCustomer وتأكد من صحة التحقق من البيانات'
                            },
                            {
                                name: 'تسجيل الدخول',
                                method: 'testUserLogin',
                                technical: 'فحص آلية التحقق من البيانات وإنشاء JWT token صحيح',
                                purpose: 'ضمان قدرة العملاء على الوصول لحساباتهم',
                                impact: 'عدم قدرة العملاء على تسجيل الدخول',
                                solution: 'تحقق من دالة authenticateCustomer وآلية إنشاء التوكن'
                            },
                            {
                                name: 'التحقق من رقم الهاتف العراقي',
                                method: 'testPhoneValidation',
                                technical: 'فحص regex pattern /^07\\d{9}$/ لضمان تنسيق الأرقام العراقية',
                                purpose: 'ضمان صحة أرقام الهواتف وتوافقها مع النظام العراقي',
                                impact: 'قبول أرقام خاطئة أو رفض أرقام صحيحة',
                                solution: 'تحديث دالة validateInput للهواتف واستخدام regex صحيح'
                            },
                            {
                                name: 'منع الحسابات المكررة',
                                method: 'testDuplicatePrevention',
                                technical: 'فحص دالة checkDuplicateCustomer للتحقق من البريد والهاتف',
                                purpose: 'منع إنشاء حسابات متعددة بنفس البيانات',
                                impact: 'إمكانية إنشاء حسابات مكررة وتضارب البيانات',
                                solution: 'تحسين آلية فحص التكرار وإضافة فهرسة للبيانات'
                            },
                            {
                                name: 'أمان كلمات المرور',
                                method: 'testPasswordSecurity',
                                technical: 'فحص قوة كلمات المرور وآلية التشفير المستخدمة',
                                purpose: 'ضمان حماية حسابات العملاء من الاختراق',
                                impact: 'ضعف أمان الحسابات وسهولة اختراقها',
                                solution: 'استخدام bcrypt أو Argon2 لتشفير كلمات المرور مع salt عشوائي'
                            }
                        ]
                    },
                    {
                        id: 'currency',
                        title: 'نظام العملة والأسعار',
                        icon: 'fas fa-coins',
                        tests: [
                            {
                                name: 'تحميل نظام العملة',
                                method: 'testCurrencySystemLoaded',
                                technical: 'فحص وجود دوال formatCurrency, getShippingCost وكائنات CURRENCY_CONFIG, SHIPPING_RATES',
                                purpose: 'ضمان عمل نظام العملة والأسعار بشكل صحيح',
                                impact: 'عدم عرض الأسعار أو حساب رسوم الشحن',
                                solution: 'تحقق من تحميل ملف scripts/currency.js وتعريف المتغيرات العامة'
                            },
                            {
                                name: 'تثبيت الدينار العراقي',
                                method: 'testIQDCurrency',
                                technical: 'فحص CURRENCY_CONFIG.code === "IQD" و CURRENCY_CONFIG.symbol === "د.ع"',
                                purpose: 'ضمان استخدام العملة العراقية الرسمية فقط',
                                impact: 'عرض أسعار بعملات أخرى مما يربك العملاء',
                                solution: 'تحديث CURRENCY_CONFIG لاستخدام IQD كعملة افتراضية وحيدة'
                            },
                            {
                                name: 'تنسيق عرض الأسعار',
                                method: 'testPriceFormatting',
                                technical: 'اختبار دالة formatCurrency لعرض الأسعار بتنسيق عربي مع فواصل',
                                purpose: 'عرض الأسعار بشكل واضح ومفهوم للعملاء العراقيين',
                                impact: 'أسعار غير واضحة أو بتنسيق خاطئ',
                                solution: 'استخدم Intl.NumberFormat مع locale عربي وإضافة رمز العملة'
                            },
                            {
                                name: 'حساب رسوم الشحن للبصرة',
                                method: 'testBasraShipping',
                                technical: 'فحص getShippingCost("البصرة") === 3000',
                                purpose: 'تطبيق رسوم شحن مخفضة لمحافظة البصرة',
                                impact: 'رسوم شحن خاطئة للبصرة',
                                solution: 'تحديث SHIPPING_RATES لتحديد 3000 د.ع للبصرة'
                            },
                            {
                                name: 'حساب رسوم الشحن للمحافظات الأخرى',
                                method: 'testOtherProvincesShipping',
                                technical: 'فحص getShippingCost لمحافظات مختلفة === 5000',
                                purpose: 'تطبيق رسوم شحن موحدة لباقي المحافظات',
                                impact: 'رسوم شحن غير متسقة أو خاطئة',
                                solution: 'تحديث SHIPPING_RATES لتحديد 5000 د.ع كقيمة افتراضية'
                            },
                            {
                                name: 'تحويل العملات القديمة',
                                method: 'testCurrencyConversion',
                                technical: 'فحص أن أسعار المنتجات أكبر من 1000 (بالدينار وليس بالدولار)',
                                purpose: 'ضمان تحديث جميع الأسعار القديمة للعملة الجديدة',
                                impact: 'أسعار خاطئة أو بعملات قديمة',
                                solution: 'تشغيل سكربت تحويل لضرب الأسعار القديمة × 1320'
                            }
                        ]
                    },
                    {
                        id: 'language',
                        title: 'نظام اللغات المتعددة',
                        icon: 'fas fa-globe',
                        tests: [
                            {
                                name: 'تحميل نظام اللغات',
                                method: 'testLanguageSystemLoaded',
                                technical: 'فحص وجود دوال changeLanguage, t, getCurrentLanguage وكائن SUPPORTED_LANGUAGES',
                                purpose: 'ضمان عمل نظام الترجمة والتبديل بين اللغات',
                                impact: 'عدم عمل تغيير اللغة أو الترجمة',
                                solution: 'تحقق من تحميل ملف scripts/language.js وتعريف الدوال العامة'
                            },
                            {
                                name: 'دعم اللغة العربية (RTL)',
                                method: 'testArabicLanguage',
                                technical: 'اختبار تغيير اللغة للعربية وفحص dir="rtl" والترجمات',
                                purpose: 'ضمان عمل اللغة العربية كلغة افتراضية مع الاتجاه الصحيح',
                                impact: 'مشاكل في عرض النصوص العربية أو اتجاه الصفحة',
                                solution: 'تحديث ملفات الترجمة العربية وضبط CSS للاتجاه RTL'
                            },
                            {
                                name: 'دعم اللغة الكردية (RTL)',
                                method: 'testKurdishLanguage',
                                technical: 'اختبار تغيير اللغة للكردية وفحص dir="rtl" والترجمات',
                                purpose: 'دعم العملاء الأكراد بلغتهم الأم',
                                impact: 'عدم فهم العملاء الأكراد للواجهة',
                                solution: 'إكمال ترجمة جميع النصوص للكردية وضبط الخطوط المناسبة'
                            },
                            {
                                name: 'دعم اللغة الإنجليزية (LTR)',
                                method: 'testEnglishLanguage',
                                technical: 'اختبار تغيير اللغة للإنجليزية وفحص dir="ltr" والترجمات',
                                purpose: 'دعم العملاء الأجانب والمتحدثين بالإنجليزية',
                                impact: 'فقدان عملاء محتملين من المتحدثين بالإنجليزية',
                                solution: 'مراجعة الترجمات الإنجليزية وضبط التصميم للاتجاه LTR'
                            },
                            {
                                name: 'تغيير الاتجاه تلقائياً',
                                method: 'testDirectionSwitching',
                                technical: 'فحص تبديل document.documentElement.dir بين RTL و LTR',
                                purpose: 'ضمان عرض صحيح للنصوص حسب اللغة المختارة',
                                impact: 'نصوص معكوسة أو غير مقروءة',
                                solution: 'تحسين دالة changeLanguage لتحديث الاتجاه تلقائياً'
                            },
                            {
                                name: 'حفظ اللغة المختارة',
                                method: 'testLanguagePersistence',
                                technical: 'فحص حفظ اللغة في localStorage واستعادتها عند إعادة التحميل',
                                purpose: 'تذكر تفضيلات المستخدم اللغوية',
                                impact: 'إعادة تعيين اللغة في كل زيارة',
                                solution: 'تحسين آلية حفظ واستعادة اللغة من localStorage'
                            }
                        ]
                    },
                    {
                        id: 'products',
                        title: 'نظام المنتجات والكتالوج',
                        icon: 'fas fa-box',
                        tests: [
                            {
                                name: 'تحميل قاعدة بيانات المنتجات',
                                method: 'testProductsLoaded',
                                technical: 'فحص وجود مصفوفة window.products وأنها تحتوي على بيانات',
                                purpose: 'ضمان عرض المنتجات للعملاء',
                                impact: 'عدم ظهور أي منتجات في المتجر',
                                solution: 'تحقق من تحميل ملف database/products.js وصحة بنية البيانات'
                            },
                            {
                                name: 'صحة أسعار المنتجات',
                                method: 'testProductPrices',
                                technical: 'فحص أن جميع أسعار المنتجات أكبر من 1000 (بالدينار العراقي)',
                                purpose: 'ضمان عرض أسعار صحيحة بالعملة المحلية',
                                impact: 'أسعار خاطئة أو بعملات أجنبية',
                                solution: 'تحديث أسعار المنتجات وضربها في سعر الصرف (1320)'
                            },
                            {
                                name: 'تصنيف المنتجات',
                                method: 'testProductCategories',
                                technical: 'فحص وجود فئات متنوعة للمنتجات وأن كل منتج له فئة',
                                purpose: 'تنظيم المنتجات وسهولة التصفح للعملاء',
                                impact: 'صعوبة في العثور على المنتجات المطلوبة',
                                solution: 'إضافة فئات واضحة لكل منتج وتحسين نظام التصنيف'
                            },
                            {
                                name: 'صور المنتجات',
                                method: 'testProductImages',
                                technical: 'فحص وجود روابط صور صحيحة لجميع المنتجات',
                                purpose: 'عرض بصري جذاب للمنتجات',
                                impact: 'منتجات بدون صور تقلل من جاذبية المتجر',
                                solution: 'رفع صور عالية الجودة وضغطها بتنسيق WebP لسرعة التحميل'
                            },
                            {
                                name: 'توفر المنتجات',
                                method: 'testProductAvailability',
                                technical: 'فحص حالة isAvailable و stock للمنتجات',
                                purpose: 'إظهار المنتجات المتاحة فقط للعملاء',
                                impact: 'عرض منتجات غير متاحة يسبب إحباط العملاء',
                                solution: 'تحديث حالة المخزون بانتظام وإخفاء المنتجات غير المتاحة'
                            },
                            {
                                name: 'بيانات المنتجات المكتملة',
                                method: 'testProductDataIntegrity',
                                technical: 'فحص وجود جميع الحقول المطلوبة: id, name, price, category, imageUrl',
                                purpose: 'ضمان عرض معلومات كاملة عن كل منتج',
                                impact: 'معلومات ناقصة تؤثر على قرار الشراء',
                                solution: 'مراجعة بيانات المنتجات وإكمال الحقول المفقودة'
                            }
                        ]
                    },
                    {
                        id: 'cart',
                        title: 'السلة والطلبات',
                        icon: 'fas fa-shopping-cart',
                        tests: [
                            {
                                name: 'إضافة منتج للسلة',
                                method: 'testAddToCart',
                                technical: 'اختبار إضافة منتج لمصفوفة السلة في localStorage',
                                purpose: 'السماح للعملاء بجمع المنتجات قبل الشراء',
                                impact: 'عدم قدرة العملاء على إضافة منتجات للسلة',
                                solution: 'تحسين دالة addToCart وضمان حفظ البيانات في localStorage'
                            },
                            {
                                name: 'حساب مجموع السلة',
                                method: 'testCartTotal',
                                technical: 'فحص حساب المجموع الصحيح لجميع المنتجات والكميات',
                                purpose: 'عرض المبلغ الإجمالي الصحيح للعميل',
                                impact: 'مبالغ خاطئة تؤدي لفقدان ثقة العملاء',
                                solution: 'مراجعة خوارزمية حساب المجموع وضمان دقة العمليات الحسابية'
                            },
                            {
                                name: 'تطبيق رسوم الشحن',
                                method: 'testShippingApplication',
                                technical: 'فحص إضافة رسوم الشحن الصحيحة حسب المحافظة',
                                purpose: 'حساب التكلفة الإجمالية الصحيحة للطلب',
                                impact: 'رسوم شحن خاطئة تؤثر على الربحية',
                                solution: 'ربط حساب الشحن بنظام العملة وقاعدة بيانات المحافظات'
                            },
                            {
                                name: 'حفظ الطلبات',
                                method: 'testOrderSaving',
                                technical: 'اختبار حفظ الطلب الكامل مع بيانات العميل في localStorage',
                                purpose: 'تسجيل الطلبات لمتابعتها ومعالجتها',
                                impact: 'فقدان الطلبات وعدم قدرة المتجر على تنفيذها',
                                solution: 'تحسين آلية حفظ الطلبات وإضافة نسخ احتياطية'
                            },
                            {
                                name: 'تحديث كمية المنتجات',
                                method: 'testQuantityUpdate',
                                technical: 'فحص تعديل كميات المنتجات في السلة',
                                purpose: 'مرونة في تعديل الطلب قبل الإتمام',
                                impact: 'عدم قدرة العملاء على تعديل طلباتهم',
                                solution: 'إضافة واجهة سهلة لتعديل الكميات مع تحديث فوري للمجموع'
                            },
                            {
                                name: 'إزالة منتجات من السلة',
                                method: 'testRemoveFromCart',
                                technical: 'اختبار حذف منتجات من السلة وتحديث البيانات',
                                purpose: 'السماح للعملاء بإزالة منتجات غير مرغوبة',
                                impact: 'سلة مليئة بمنتجات غير مرغوبة',
                                solution: 'إضافة أزرار حذف واضحة مع تأكيد قبل الحذف'
                            }
                        ]
                    },
                    {
                        id: 'ui',
                        title: 'الواجهة وتجربة المستخدم',
                        icon: 'fas fa-desktop',
                        tests: [
                            {
                                name: 'تجاوب التصميم',
                                method: 'testResponsiveDesign',
                                technical: 'فحص وجود Bootstrap وviewport meta tag للتصميم المتجاوب',
                                purpose: 'ضمان عمل الموقع على جميع الأجهزة والشاشات',
                                impact: 'تجربة سيئة على الهواتف والأجهزة اللوحية',
                                solution: 'استخدام CSS Media Queries وتحسين التصميم للشاشات الصغيرة'
                            },
                            {
                                name: 'عرض خيارات المستخدم',
                                method: 'testUserOptionsDisplay',
                                technical: 'فحص ظهور الملف الشخصي والإعدادات بعد تسجيل الدخول',
                                purpose: 'توفير وصول سهل لإعدادات الحساب',
                                impact: 'صعوبة في إدارة الحساب الشخصي',
                                solution: 'تحسين واجهة المستخدم وإضافة قائمة منسدلة للخيارات'
                            },
                            {
                                name: 'تحميل الخطوط والأيقونات',
                                method: 'testFontsAndIcons',
                                technical: 'فحص تحميل خط Cairo والأيقونات من Bootstrap Icons و Font Awesome',
                                purpose: 'مظهر احترافي وقراءة واضحة للنصوص العربية',
                                impact: 'نصوص غير واضحة وأيقونات مفقودة',
                                solution: 'تحسين تحميل الخطوط وإضافة خطوط احتياطية'
                            },
                            {
                                name: 'عمل الروابط والأزرار',
                                method: 'testLinksAndButtons',
                                technical: 'فحص وجود أزرار وروابط قابلة للنقر في الصفحة',
                                purpose: 'تنقل سهل وتفاعل فعال مع الموقع',
                                impact: 'عدم قدرة المستخدمين على التنقل أو التفاعل',
                                solution: 'مراجعة جميع الروابط وإضافة JavaScript للأزرار التفاعلية'
                            },
                            {
                                name: 'عرض الرسائل والتنبيهات',
                                method: 'testMessagesDisplay',
                                technical: 'اختبار إنشاء وعرض رسائل التنبيه والنجاح والخطأ',
                                purpose: 'إعلام المستخدم بحالة العمليات المختلفة',
                                impact: 'عدم وضوح نتائج العمليات للمستخدم',
                                solution: 'تطوير نظام إشعارات موحد مع تصميم واضح'
                            },
                            {
                                name: 'سلاسة الانتقالات',
                                method: 'testAnimations',
                                technical: 'فحص دعم CSS animations والحركات البصرية',
                                purpose: 'تجربة مستخدم سلسة وجذابة',
                                impact: 'واجهة جامدة وأقل جاذبية',
                                solution: 'إضافة انتقالات CSS ناعمة وحركات تفاعلية'
                            }
                        ]
                    },
                    {
                        id: 'security',
                        title: 'الأمان والبنية التقنية',
                        icon: 'fas fa-lock',
                        tests: [
                            {
                                name: 'هيكل الملفات',
                                method: 'testFileStructure',
                                technical: 'فحص تحميل جميع الملفات المطلوبة: customer-auth.js, currency.js, language.js, products.js',
                                purpose: 'ضمان تحميل جميع مكونات النظام بشكل صحيح',
                                impact: 'عطل في وظائف أساسية بالموقع',
                                solution: 'تنظيم الملفات في مجلدات واضحة وفحص مسارات التحميل'
                            },
                            {
                                name: 'ترابط السكربتات',
                                method: 'testScriptConnections',
                                technical: 'فحص الاتصال بين السكربتات المختلفة والدوال المشتركة',
                                purpose: 'ضمان عمل النظام كوحدة متكاملة',
                                impact: 'أخطاء في التشغيل وعدم تزامن البيانات',
                                solution: 'تحسين ترتيب تحميل السكربتات وإدارة التبعيات'
                            },
                            {
                                name: 'عدم وجود أخطاء في الكونسول',
                                method: 'testConsoleErrors',
                                technical: 'مراقبة console.error للتحقق من عدم وجود أخطاء JavaScript',
                                purpose: 'ضمان عمل الموقع بدون أخطاء برمجية',
                                impact: 'مشاكل في الأداء وتجربة مستخدم سيئة',
                                solution: 'إصلاح جميع أخطاء JavaScript وتحسين معالجة الأخطاء'
                            },
                            {
                                name: 'حماية البيانات الحساسة',
                                method: 'testDataProtection',
                                technical: 'فحص تشفير كلمات المرور وحماية البيانات الشخصية',
                                purpose: 'حماية خصوصية وأمان بيانات العملاء',
                                impact: 'تسريب بيانات العملاء ومشاكل قانونية',
                                solution: 'استخدام bcrypt لتشفير كلمات المرور وتطبيق HTTPS'
                            },
                            {
                                name: 'التحقق من الجلسات',
                                method: 'testSessionValidation',
                                technical: 'فحص صحة JWT tokens وآلية انتهاء الصلاحية',
                                purpose: 'ضمان أمان جلسات تسجيل الدخول',
                                impact: 'إمكانية اختراق الحسابات أو جلسات مفتوحة إلى الأبد',
                                solution: 'تحسين آلية JWT وإضافة انتهاء صلاحية قصير'
                            },
                            {
                                name: 'منع الوصول غير المصرح',
                                method: 'testUnauthorizedAccess',
                                technical: 'فحص عدم إمكانية الوصول للبيانات المحمية بدون تسجيل دخول',
                                purpose: 'حماية البيانات من الوصول غير المشروع',
                                impact: 'تسريب معلومات حساسة أو تلاعب بالبيانات',
                                solution: 'تطبيق نظام صلاحيات صارم وفحص الهوية في كل طلب'
                            }
                        ]
                    }
                ];

                this.init();
            }

            init() {
                this.renderTestSections();
                this.updateSystemStatus('online');
                console.log('🔧 نظام اختبار VelaSweets جاهز للعمل');
            }

            renderTestSections() {
                const container = document.getElementById('testSections');
                container.innerHTML = '';

                this.testSections.forEach(section => {
                    const sectionElement = this.createTestSection(section);
                    container.appendChild(sectionElement);
                });
            }

            createTestSection(section) {
                const sectionDiv = document.createElement('div');
                sectionDiv.className = 'test-section';
                sectionDiv.id = `section-${section.id}`;

                sectionDiv.innerHTML = `
                    <div class="section-header">
                        <div class="section-title">
                            <i class="${section.icon}"></i>
                            ${section.title}
                        </div>
                        <div class="section-status">
                            <span class="status-badge testing" id="badge-${section.id}">
                                في الانتظار
                            </span>
                        </div>
                    </div>
                    <div class="test-items" id="items-${section.id}">
                        ${section.tests.map(test => `
                            <div class="test-item" id="test-${section.id}-${test.method}">
                                <div class="test-header">
                                    <div class="test-title">
                                        <i class="fas fa-cog"></i>
                                        ${test.name}
                                    </div>
                                    <span class="test-status-badge testing" id="status-${section.id}-${test.method}">
                                        <i class="fas fa-clock"></i> في الانتظار
                                    </span>
                                </div>
                                <div class="test-details" id="details-${section.id}-${test.method}" style="display: none;">
                                    <div class="test-detail-item technical">
                                        <div class="detail-label">
                                            <i class="fas fa-code"></i>
                                            الشرح التقني
                                        </div>
                                        <div class="detail-content">${test.technical}</div>
                                    </div>
                                    <div class="test-detail-item purpose">
                                        <div class="detail-label">
                                            <i class="fas fa-bullseye"></i>
                                            الهدف من الفحص
                                        </div>
                                        <div class="detail-content">${test.purpose}</div>
                                    </div>
                                    <div class="test-detail-item impact" style="display: none;" id="impact-${section.id}-${test.method}">
                                        <div class="detail-label">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            أثر المشكلة
                                        </div>
                                        <div class="detail-content">${test.impact}</div>
                                    </div>
                                    <div class="test-detail-item solution" style="display: none;" id="solution-${section.id}-${test.method}">
                                        <div class="detail-label">
                                            <i class="fas fa-tools"></i>
                                            الحل المقترح
                                        </div>
                                        <div class="detail-content">${test.solution}</div>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;

                return sectionDiv;
            }

            updateSystemStatus(status) {
                const statusElement = document.getElementById('systemStatus');
                const statusClasses = {
                    'online': { class: 'online', icon: 'fa-circle', text: 'النظام متصل' },
                    'testing': { class: 'testing', icon: 'fa-cog', text: 'جاري الفحص' },
                    'error': { class: 'error', icon: 'fa-exclamation-triangle', text: 'خطأ في النظام' }
                };

                const config = statusClasses[status];
                statusElement.className = `status-indicator ${config.class}`;
                statusElement.innerHTML = `
                    <i class="fas ${config.icon}"></i>
                    <span>${config.text}</span>
                `;
            }

            updateTestResult(sectionId, testMethod, result, errorMessage = '', testData = null) {
                // حماية من الأخطاء - التحقق من وجود العناصر أولاً
                const statusElement = document.getElementById(`status-${sectionId}-${testMethod}`);
                const detailsElement = document.getElementById(`details-${sectionId}-${testMethod}`);
                const testItemElement = document.getElementById(`test-${sectionId}-${testMethod}`);
                const impactElement = document.getElementById(`impact-${sectionId}-${testMethod}`);
                const solutionElement = document.getElementById(`solution-${sectionId}-${testMethod}`);

                // إذا لم توجد العناصر، تسجيل الخطأ والخروج
                if (!statusElement || !testItemElement) {
                    console.warn(`⚠️ عناصر الاختبار مفقودة: ${sectionId}-${testMethod}`);
                    this.testResults.total++;
                    this.testResults.failed++;
                    return;
                }

                this.testResults.total++;

                // إظهار التفاصيل دائماً
                if (detailsElement) detailsElement.style.display = 'block';

                if (result === 'success') {
                    this.testResults.passed++;
                    statusElement.className = 'test-status-badge success';
                    statusElement.innerHTML = '<i class="fas fa-check"></i> نجح';
                    testItemElement.classList.add('success');

                    // إخفاء أثر المشكلة والحل للاختبارات الناجحة
                    if (impactElement) impactElement.style.display = 'none';
                    if (solutionElement) solutionElement.style.display = 'none';

                } else if (result === 'error') {
                    this.testResults.failed++;
                    statusElement.className = 'test-status-badge error';
                    statusElement.innerHTML = '<i class="fas fa-times"></i> فشل';
                    testItemElement.classList.add('error');

                    // إظهار أثر المشكلة والحل للاختبارات الفاشلة
                    if (impactElement) impactElement.style.display = 'block';
                    if (solutionElement) solutionElement.style.display = 'block';

                    // إضافة المشكلة لقائمة المشاكل
                    this.addProblem(sectionId, testMethod, testData, errorMessage);

                    // إضافة المشكلة للتحليل الذكي
                    this.problemsFound.push({
                        category: sectionId,
                        testName: testData ? testData.name : testMethod,
                        error: errorMessage,
                        impact: testData ? testData.impact : '',
                        solution: testData ? testData.solution : '',
                        priority: this.determinePriority(errorMessage, sectionId)
                    });

                } else if (result === 'warning') {
                    this.testResults.warnings++;
                    statusElement.className = 'test-status-badge warning';
                    statusElement.innerHTML = '<i class="fas fa-exclamation-triangle"></i> تحذير';
                    testItemElement.classList.add('warning');

                    // إظهار أثر المشكلة والحل للتحذيرات
                    if (impactElement) impactElement.style.display = 'block';
                    if (solutionElement) solutionElement.style.display = 'block';
                }

                // تحديث مؤشر التقدم العام
                this.updateOverallProgress();
            }

        determinePriority(errorMessage, category) {
            // تحديد الأولوية بناءً على نوع الخطأ والفئة
            if (errorMessage.includes('is not a function') || errorMessage.includes('Cannot read properties of null')) {
                return 'critical';
            }

            if (category === 'auth' || category === 'security') {
                return 'high';
            }

            if (errorMessage.includes('انتهت مهلة') || errorMessage.includes('timeout')) {
                return 'medium';
            }

            return 'medium';
        }

            updateOverallProgress() {
                const progressText = `${this.testResults.total} من 42 اختبار`;
                const progressElement = document.querySelector('.system-status span');
                if (progressElement && this.testResults.total < 42) {
                    progressElement.textContent = `جاري الفحص... (${progressText})`;
                }
            }

            addProblem(sectionId, testMethod, testData, errorMessage) {
                if (!testData) return;

                const problem = {
                    id: `${sectionId}-${testMethod}`,
                    title: testData.name,
                    section: sectionId,
                    description: errorMessage,
                    impact: testData.impact,
                    solution: testData.solution,
                    severity: this.determineSeverity(sectionId, testMethod),
                    technical: testData.technical
                };

                this.problemsFound.push(problem);
            }

            determineSeverity(sectionId, testMethod) {
                // تحديد مستوى خطورة المشكلة
                const criticalTests = ['testAuthFilesLoaded', 'testCurrencySystemLoaded', 'testProductsLoaded'];
                const highTests = ['testUserRegistration', 'testUserLogin', 'testIQDCurrency'];

                if (criticalTests.includes(testMethod)) return 'critical';
                if (highTests.includes(testMethod)) return 'high';
                if (sectionId === 'security') return 'high';
                return 'medium';
            }

            updateSectionStatus(sectionId) {
                const section = this.testSections.find(s => s.id === sectionId);
                const badge = document.getElementById(`badge-${sectionId}`);
                const sectionElement = document.getElementById(`section-${sectionId}`);

                let passedCount = 0;
                let failedCount = 0;
                let warningCount = 0;

                section.tests.forEach(test => {
                    const iconElement = document.getElementById(`icon-${sectionId}-${test.method}`);
                    if (iconElement.classList.contains('success')) passedCount++;
                    else if (iconElement.classList.contains('error')) failedCount++;
                    else if (iconElement.classList.contains('warning')) warningCount++;
                });

                if (failedCount > 0) {
                    badge.className = 'status-badge error';
                    badge.textContent = `${failedCount} فشل`;
                    sectionElement.classList.add('error');
                } else if (warningCount > 0) {
                    badge.className = 'status-badge warning';
                    badge.textContent = `${warningCount} تحذير`;
                    sectionElement.classList.add('warning');
                } else if (passedCount === section.tests.length) {
                    badge.className = 'status-badge success';
                    badge.textContent = 'نجح بالكامل';
                    sectionElement.classList.add('success');
                }
            }
        }

        // إنشاء مثيل من نظام الاختبار
        let systemTester;

        document.addEventListener('DOMContentLoaded', function() {
            systemTester = new SystemTester();
        });

        // دالة عرض ملخص المشاكل
        function showProblemsummary() {
            if (systemTester.problemsFound.length === 0) return;

            const summaryPanel = document.getElementById('summaryPanel');

            const problemsHtml = `
                <div class="problems-summary show">
                    <h3 style="color: var(--danger-color); margin-bottom: 20px;">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        المشاكل المكتشفة (${systemTester.problemsFound.length})
                    </h3>
                    ${systemTester.problemsFound.map(problem => `
                        <div class="problem-card">
                            <div class="problem-header">
                                <div class="problem-title">
                                    <i class="fas fa-bug"></i>
                                    ${problem.title}
                                </div>
                                <span class="problem-severity severity-${problem.severity}">
                                    ${getSeverityText(problem.severity)}
                                </span>
                            </div>
                            <div class="test-detail-item technical">
                                <div class="detail-label">
                                    <i class="fas fa-code"></i>
                                    التفاصيل التقنية
                                </div>
                                <div class="detail-content">${problem.technical}</div>
                            </div>
                            <div class="test-detail-item impact">
                                <div class="detail-label">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    أثر المشكلة
                                </div>
                                <div class="detail-content">${problem.impact}</div>
                            </div>
                            <div class="test-detail-item solution">
                                <div class="detail-label">
                                    <i class="fas fa-tools"></i>
                                    الحل المقترح
                                </div>
                                <div class="detail-content">${problem.solution}</div>
                            </div>
                            ${problem.description ? `
                                <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 12px; margin-top: 10px;">
                                    <strong>تفاصيل الخطأ:</strong> ${problem.description}
                                </div>
                            ` : ''}
                        </div>
                    `).join('')}
                    <button class="copy-solutions-btn" onclick="exportSolutions()">
                        <i class="fas fa-copy me-2"></i>
                        نسخ جميع الحلول المقترحة
                    </button>
                </div>
            `;

            summaryPanel.insertAdjacentHTML('beforeend', problemsHtml);

            // إظهار زر التصدير
            document.getElementById('exportBtn').style.display = 'inline-flex';
        }

        function getSeverityText(severity) {
            const severityMap = {
                'critical': 'حرجة',
                'high': 'عالية',
                'medium': 'متوسطة',
                'low': 'منخفضة'
            };
            return severityMap[severity] || 'غير محدد';
        }

        // دالة تصدير الحلول
        function exportSolutions() {
            if (systemTester.problemsFound.length === 0) {
                alert('لا توجد مشاكل لتصدير حلولها');
                return;
            }

            let solutionsText = `# تقرير المشاكل والحلول المقترحة - VelaSweets\n`;
            solutionsText += `تاريخ الفحص: ${new Date().toLocaleString('ar-IQ')}\n`;
            solutionsText += `عدد المشاكل المكتشفة: ${systemTester.problemsFound.length}\n\n`;

            systemTester.problemsFound.forEach((problem, index) => {
                solutionsText += `## ${index + 1}. ${problem.title}\n`;
                solutionsText += `**مستوى الخطورة:** ${getSeverityText(problem.severity)}\n`;
                solutionsText += `**القسم:** ${problem.section}\n\n`;
                solutionsText += `**التفاصيل التقنية:**\n${problem.technical}\n\n`;
                solutionsText += `**أثر المشكلة:**\n${problem.impact}\n\n`;
                solutionsText += `**الحل المقترح:**\n${problem.solution}\n\n`;
                if (problem.description) {
                    solutionsText += `**تفاصيل الخطأ:**\n${problem.description}\n\n`;
                }
                solutionsText += `---\n\n`;
            });

            // نسخ النص للحافظة
            navigator.clipboard.writeText(solutionsText).then(() => {
                alert('تم نسخ جميع الحلول المقترحة إلى الحافظة بنجاح!');
            }).catch(() => {
                // إنشاء textarea مؤقت للنسخ
                const textarea = document.createElement('textarea');
                textarea.value = solutionsText;
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);
                alert('تم نسخ الحلول إلى الحافظة!');
            });
        }

        // دوال الاختبار الفعلية

        // === اختبارات نظام المصادقة ===
        function testAuthFilesLoaded() {
            try {
                const requiredFunctions = [
                    'registerCustomer',
                    'authenticateCustomer',
                    'validateInput',
                    'checkDuplicateCustomer',
                    'getCurrentCustomer'
                ];

                const missingFunctions = [];

                for (let func of requiredFunctions) {
                    if (typeof window[func] !== 'function') {
                        missingFunctions.push(func);
                    }
                }

                if (missingFunctions.length > 0) {
                    return {
                        success: false,
                        error: `الدوال التالية غير موجودة: ${missingFunctions.join(', ')} - تحقق من تحميل ملف scripts/customer-auth.js`
                    };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: `خطأ في فحص ملفات المصادقة: ${error.message}` };
            }
        }

        function testUserRegistration() {
            try {
                // التحقق من وجود دالة التسجيل أولاً
                if (typeof registerCustomer !== 'function') {
                    return {
                        success: false,
                        error: 'دالة registerCustomer غير موجودة - تحقق من تحميل ملف scripts/customer-auth.js'
                    };
                }

                const testData = {
                    fullName: 'Ahmed Test User', // استخدام أحرف إنجليزية لتجنب مشكلة regex
                    email: 'test' + Date.now() + '@example.com',
                    phone: '07' + Math.floor(Math.random() * 1000000000).toString().padStart(9, '0'),
                    province: 'بغداد',
                    address: 'عنوان تجريبي',
                    password: 'Test123456' // كلمة مرور أقوى
                };

                // محاولة التسجيل مع معالجة الأخطاء
                const result = registerCustomer(testData);

                // التحقق من صحة النتيجة
                if (!result || typeof result !== 'object') {
                    return {
                        success: false,
                        error: 'دالة registerCustomer لم ترجع نتيجة صحيحة'
                    };
                }

                if (result.success) {
                    return { success: true };
                } else {
                    return {
                        success: false,
                        error: result.message || 'فشل في تسجيل المستخدم لسبب غير معروف'
                    };
                }
            } catch (error) {
                return {
                    success: false,
                    error: `خطأ في اختبار تسجيل المستخدم: ${error.message}`
                };
            }
        }

        function testUserLogin() {
            try {
                // التحقق من وجود دالة تسجيل الدخول أولاً
                if (typeof authenticateCustomer !== 'function') {
                    return {
                        success: false,
                        error: 'دالة authenticateCustomer غير موجودة - تحقق من تحميل ملف scripts/customer-auth.js'
                    };
                }

                // محاولة الحصول على العملاء مع معالجة الأخطاء
                let customers = [];
                try {
                    customers = JSON.parse(localStorage.getItem('customers') || '[]');
                } catch (parseError) {
                    return {
                        success: false,
                        error: 'خطأ في قراءة بيانات العملاء من localStorage'
                    };
                }

                if (!Array.isArray(customers) || customers.length === 0) {
                    return {
                        success: false,
                        error: 'لا توجد حسابات للاختبار - قم بإنشاء حساب أولاً'
                    };
                }

                const testCustomer = customers[customers.length - 1];

                // التحقق من وجود البيانات المطلوبة
                if (!testCustomer.email || !testCustomer.password) {
                    return {
                        success: false,
                        error: 'بيانات العميل التجريبي غير مكتملة'
                    };
                }

                const result = authenticateCustomer(testCustomer.email, testCustomer.password);

                // التحقق من صحة النتيجة
                if (!result || typeof result !== 'object') {
                    return {
                        success: false,
                        error: 'دالة authenticateCustomer لم ترجع نتيجة صحيحة'
                    };
                }

                if (result.success) {
                    return { success: true };
                } else {
                    return {
                        success: false,
                        error: result.message || 'فشل في تسجيل الدخول لسبب غير معروف'
                    };
                }
            } catch (error) {
                return {
                    success: false,
                    error: `خطأ في اختبار تسجيل الدخول: ${error.message}`
                };
            }
        }

        function testPhoneValidation() {
            try {
                const validPhones = ['07123456789', '07987654321', '07555666777'];
                const invalidPhones = ['123456789', '7123456789', '071234567890', '07abc123456'];

                // اختبار الأرقام الصحيحة
                for (let phone of validPhones) {
                    const result = validateInput(phone, 'phone');
                    if (!result.valid) {
                        return { success: false, error: `الرقم ${phone} يجب أن يكون صحيحاً` };
                    }
                }

                // اختبار الأرقام الخاطئة
                for (let phone of invalidPhones) {
                    const result = validateInput(phone, 'phone');
                    if (result.valid) {
                        return { success: false, error: `الرقم ${phone} يجب أن يكون خاطئاً` };
                    }
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testDuplicatePrevention() {
            try {
                const customers = JSON.parse(localStorage.getItem('customers') || '[]');
                if (customers.length === 0) {
                    return { success: false, error: 'لا توجد حسابات للاختبار' };
                }

                const existingCustomer = customers[0];
                const duplicateCheck = checkDuplicateCustomer(existingCustomer.email, existingCustomer.phone);

                if (duplicateCheck.isDuplicate) {
                    return { success: true };
                } else {
                    return { success: false, error: 'لم يتم اكتشاف التكرار' };
                }
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testPasswordSecurity() {
            try {
                // التحقق من وجود دالة التشفير أولاً
                if (typeof hashPassword !== 'function') {
                    return {
                        success: false,
                        error: 'دالة تشفير كلمات المرور غير موجودة - تحقق من تحميل ملف scripts/customer-auth.js'
                    };
                }

                // اختبار التشفير
                const testPassword = 'Test123!';
                const hashedPassword = hashPassword(testPassword);

                // التحقق من أن كلمة المرور تم تشفيرها
                if (!hashedPassword || hashedPassword === testPassword) {
                    return {
                        success: false,
                        error: 'كلمات المرور غير محمية بشكل كافٍ (تخزينها بدون تشفير)'
                    };
                }

                // التحقق من أن التشفير يعطي نتائج مختلفة لكلمات مرور مختلفة
                const anotherPassword = 'Different123!';
                const anotherHashed = hashPassword(anotherPassword);

                if (hashedPassword === anotherHashed) {
                    return {
                        success: false,
                        error: 'خوارزمية التشفير لا تعطي نتائج مختلفة لكلمات مرور مختلفة'
                    };
                }

                // التحقق من دالة التحقق من كلمة المرور
                if (typeof verifyPassword === 'function') {
                    const isValid = verifyPassword(testPassword, hashedPassword);
                    if (!isValid) {
                        return {
                            success: false,
                            error: 'دالة التحقق من كلمة المرور لا تعمل بشكل صحيح'
                        };
                    }
                }

                return { success: true };

            } catch (error) {
                return {
                    success: false,
                    error: `خطأ في اختبار أمان كلمات المرور: ${error.message}`
                };
            }
        }

        // === اختبارات نظام العملة ===
        function testCurrencySystemLoaded() {
            try {
                const requiredFunctions = ['formatCurrency', 'getShippingCost'];
                const requiredObjects = ['CURRENCY_CONFIG', 'SHIPPING_RATES'];

                for (let func of requiredFunctions) {
                    if (typeof window[func] !== 'function') {
                        return { success: false, error: `الدالة ${func} غير موجودة` };
                    }
                }

                for (let obj of requiredObjects) {
                    if (typeof window[obj] === 'undefined') {
                        return { success: false, error: `الكائن ${obj} غير موجود` };
                    }
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testIQDCurrency() {
            try {
                if (CURRENCY_CONFIG.code !== 'IQD') {
                    return { success: false, error: `العملة الحالية ${CURRENCY_CONFIG.code} وليس IQD` };
                }

                if (CURRENCY_CONFIG.symbol !== 'د.ع') {
                    return { success: false, error: `رمز العملة ${CURRENCY_CONFIG.symbol} وليس د.ع` };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testPriceFormatting() {
            try {
                const testPrice = 15000;
                const formatted = formatCurrency(testPrice);

                if (!formatted.includes('د.ع')) {
                    return { success: false, error: 'رمز العملة غير موجود في التنسيق' };
                }

                if (!formatted.includes('15,000') && !formatted.includes('15000')) {
                    return { success: false, error: 'تنسيق الرقم غير صحيح' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testBasraShipping() {
            try {
                const basraShipping = getShippingCost('البصرة');
                if (basraShipping !== 3000) {
                    return { success: false, error: `رسوم شحن البصرة ${basraShipping} وليس 3000` };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testOtherProvincesShipping() {
            try {
                const provinces = ['بغداد', 'أربيل', 'النجف', 'كركوك'];

                for (let province of provinces) {
                    const shipping = getShippingCost(province);
                    if (shipping !== 5000) {
                        return { success: false, error: `رسوم شحن ${province} هي ${shipping} وليس 5000` };
                    }
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testCurrencyConversion() {
            try {
                // التحقق من أن أسعار المنتجات بالدينار العراقي
                if (typeof window.products !== 'undefined' && window.products.length > 0) {
                    const firstProduct = window.products[0];
                    if (firstProduct.price < 1000) {
                        return { success: false, error: 'أسعار المنتجات لا تزال بالدولار' };
                    }
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // === اختبارات نظام اللغات ===
        function testLanguageSystemLoaded() {
            try {
                const requiredFunctions = ['changeLanguage', 't', 'getCurrentLanguage'];
                const requiredObjects = ['SUPPORTED_LANGUAGES'];

                for (let func of requiredFunctions) {
                    if (typeof window[func] !== 'function') {
                        return { success: false, error: `الدالة ${func} غير موجودة` };
                    }
                }

                for (let obj of requiredObjects) {
                    if (typeof window[obj] === 'undefined') {
                        return { success: false, error: `الكائن ${obj} غير موجود` };
                    }
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testArabicLanguage() {
            try {
                changeLanguage('ar');

                if (getCurrentLanguage() !== 'ar') {
                    return { success: false, error: 'فشل في تغيير اللغة إلى العربية' };
                }

                if (document.documentElement.getAttribute('dir') !== 'rtl') {
                    return { success: false, error: 'اتجاه الصفحة ليس RTL للعربية' };
                }

                const testTranslation = t('nav.home');
                if (!testTranslation || testTranslation === 'nav.home') {
                    return { success: false, error: 'الترجمة العربية لا تعمل' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testKurdishLanguage() {
            try {
                changeLanguage('ku');

                if (getCurrentLanguage() !== 'ku') {
                    return { success: false, error: 'فشل في تغيير اللغة إلى الكردية' };
                }

                if (document.documentElement.getAttribute('dir') !== 'rtl') {
                    return { success: false, error: 'اتجاه الصفحة ليس RTL للكردية' };
                }

                const testTranslation = t('nav.home');
                if (!testTranslation || testTranslation === 'nav.home') {
                    return { success: false, error: 'الترجمة الكردية لا تعمل' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testEnglishLanguage() {
            try {
                changeLanguage('en');

                if (getCurrentLanguage() !== 'en') {
                    return { success: false, error: 'فشل في تغيير اللغة إلى الإنجليزية' };
                }

                if (document.documentElement.getAttribute('dir') !== 'ltr') {
                    return { success: false, error: 'اتجاه الصفحة ليس LTR للإنجليزية' };
                }

                const testTranslation = t('nav.home');
                if (!testTranslation || testTranslation === 'nav.home') {
                    return { success: false, error: 'الترجمة الإنجليزية لا تعمل' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testDirectionSwitching() {
            try {
                // اختبار التبديل بين RTL و LTR
                changeLanguage('ar');
                const rtlDirection = document.documentElement.getAttribute('dir');

                changeLanguage('en');
                const ltrDirection = document.documentElement.getAttribute('dir');

                if (rtlDirection !== 'rtl' || ltrDirection !== 'ltr') {
                    return { success: false, error: 'تبديل الاتجاه لا يعمل بشكل صحيح' };
                }

                // العودة للعربية
                changeLanguage('ar');

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testLanguagePersistence() {
            try {
                changeLanguage('en');
                const savedLanguage = localStorage.getItem('selected_language');

                if (savedLanguage !== 'en') {
                    return { success: false, error: 'اللغة لا تُحفظ في localStorage' };
                }

                // العودة للعربية
                changeLanguage('ar');

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // === اختبارات نظام المنتجات ===
        function testProductsLoaded() {
            try {
                if (typeof window.products === 'undefined') {
                    return { success: false, error: 'قاعدة بيانات المنتجات غير محملة' };
                }

                if (!Array.isArray(window.products)) {
                    return { success: false, error: 'المنتجات ليست في شكل مصفوفة' };
                }

                if (window.products.length === 0) {
                    return { success: false, error: 'لا توجد منتجات في قاعدة البيانات' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testProductPrices() {
            try {
                if (!window.products || window.products.length === 0) {
                    return { success: false, error: 'لا توجد منتجات للاختبار' };
                }

                for (let product of window.products) {
                    if (!product.price || product.price < 1000) {
                        return { success: false, error: `سعر المنتج ${product.name} غير صحيح: ${product.price}` };
                    }
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testProductCategories() {
            try {
                if (!window.products || window.products.length === 0) {
                    return { success: false, error: 'لا توجد منتجات للاختبار' };
                }

                const categories = new Set();
                for (let product of window.products) {
                    if (!product.category) {
                        return { success: false, error: `المنتج ${product.name} بدون فئة` };
                    }
                    categories.add(product.category);
                }

                if (categories.size < 2) {
                    return { success: false, error: 'عدد الفئات قليل جداً' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testProductImages() {
            try {
                if (!window.products || window.products.length === 0) {
                    return { success: false, error: 'لا توجد منتجات للاختبار' };
                }

                for (let product of window.products) {
                    if (!product.imageUrl) {
                        return { success: false, error: `المنتج ${product.name} بدون صورة` };
                    }

                    if (!product.imageUrl.startsWith('http')) {
                        return { success: false, error: `رابط صورة المنتج ${product.name} غير صحيح` };
                    }
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testProductAvailability() {
            try {
                if (!window.products || window.products.length === 0) {
                    return { success: false, error: 'لا توجد منتجات للاختبار' };
                }

                let availableCount = 0;
                for (let product of window.products) {
                    if (product.isAvailable && product.stock > 0) {
                        availableCount++;
                    }
                }

                if (availableCount === 0) {
                    return { success: false, error: 'لا توجد منتجات متاحة' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testProductDataIntegrity() {
            try {
                if (!window.products || window.products.length === 0) {
                    return { success: false, error: 'لا توجد منتجات للاختبار' };
                }

                const requiredFields = ['id', 'name', 'price', 'category', 'imageUrl'];

                for (let product of window.products) {
                    for (let field of requiredFields) {
                        if (!product[field]) {
                            return { success: false, error: `المنتج ${product.name || 'غير معروف'} يفتقد الحقل ${field}` };
                        }
                    }
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // === اختبارات السلة والطلبات ===
        function testAddToCart() {
            try {
                // مسح السلة أولاً
                localStorage.removeItem('cart');

                if (!window.products || window.products.length === 0) {
                    return { success: false, error: 'لا توجد منتجات للاختبار' };
                }

                const testProduct = window.products[0];
                const cart = [];

                cart.push({
                    id: testProduct.id,
                    name: testProduct.name,
                    price: testProduct.price,
                    quantity: 1,
                    image: testProduct.imageUrl
                });

                localStorage.setItem('cart', JSON.stringify(cart));

                const savedCart = JSON.parse(localStorage.getItem('cart') || '[]');
                if (savedCart.length === 0) {
                    return { success: false, error: 'فشل في حفظ المنتج في السلة' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testCartTotal() {
            try {
                const cart = JSON.parse(localStorage.getItem('cart') || '[]');
                if (cart.length === 0) {
                    return { success: false, error: 'السلة فارغة' };
                }

                let total = 0;
                for (let item of cart) {
                    total += item.price * item.quantity;
                }

                if (total <= 0) {
                    return { success: false, error: 'مجموع السلة غير صحيح' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testShippingApplication() {
            try {
                const basraShipping = getShippingCost('البصرة');
                const baghdadShipping = getShippingCost('بغداد');

                if (basraShipping !== 3000 || baghdadShipping !== 5000) {
                    return { success: false, error: 'رسوم الشحن غير صحيحة' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testOrderSaving() {
            try {
                const testOrder = {
                    id: 'test-' + Date.now(),
                    customerData: {
                        fullName: 'عميل تجريبي',
                        email: '<EMAIL>',
                        phone: '07123456789',
                        province: 'بغداد'
                    },
                    items: [{
                        id: 1,
                        name: 'منتج تجريبي',
                        price: 15000,
                        quantity: 1
                    }],
                    total: 20000,
                    shipping: 5000,
                    status: 'pending',
                    createdAt: new Date().toISOString()
                };

                const orders = JSON.parse(localStorage.getItem('customer_orders') || '[]');
                orders.push(testOrder);
                localStorage.setItem('customer_orders', JSON.stringify(orders));

                const savedOrders = JSON.parse(localStorage.getItem('customer_orders') || '[]');
                const savedOrder = savedOrders.find(o => o.id === testOrder.id);

                if (!savedOrder) {
                    return { success: false, error: 'فشل في حفظ الطلب' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testQuantityUpdate() {
            try {
                const cart = JSON.parse(localStorage.getItem('cart') || '[]');
                if (cart.length === 0) {
                    return { success: false, error: 'السلة فارغة' };
                }

                // تحديث الكمية
                cart[0].quantity = 3;
                localStorage.setItem('cart', JSON.stringify(cart));

                const updatedCart = JSON.parse(localStorage.getItem('cart') || '[]');
                if (updatedCart[0].quantity !== 3) {
                    return { success: false, error: 'فشل في تحديث الكمية' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testRemoveFromCart() {
            try {
                let cart = JSON.parse(localStorage.getItem('cart') || '[]');
                if (cart.length === 0) {
                    return { success: false, error: 'السلة فارغة' };
                }

                // إزالة المنتج الأول
                cart = cart.slice(1);
                localStorage.setItem('cart', JSON.stringify(cart));

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // === اختبارات الواجهة والمظهر ===
        function testResponsiveDesign() {
            try {
                // فحص وجود Bootstrap
                if (typeof window.bootstrap === 'undefined') {
                    return { success: false, error: 'Bootstrap غير محمل' };
                }

                // فحص وجود viewport meta tag
                const viewport = document.querySelector('meta[name="viewport"]');
                if (!viewport) {
                    return { success: false, error: 'viewport meta tag غير موجود' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testUserOptionsDisplay() {
            try {
                // هذا اختبار لصفحة الإدارة، لذا سنتحقق من وجود عناصر أساسية
                const basicElements = [
                    'body', 'head', 'title'
                ];

                for (let element of basicElements) {
                    if (!document.querySelector(element)) {
                        return { success: false, error: `العنصر ${element} غير موجود` };
                    }
                }

                // التحقق من وجود Bootstrap
                const bootstrapElements = document.querySelectorAll('.container, .row, .col, [class*="btn"]');
                if (bootstrapElements.length === 0) {
                    return { success: false, error: 'عناصر Bootstrap غير موجودة' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testFontsAndIcons() {
            try {
                // فحص تحميل الخطوط
                const computedStyle = window.getComputedStyle(document.body);
                const fontFamily = computedStyle.fontFamily;

                if (!fontFamily.includes('Cairo')) {
                    return { success: false, error: 'خط Cairo غير محمل' };
                }

                // فحص وجود أيقونات Bootstrap
                const iconElements = document.querySelectorAll('.bi, .fas, .fa');
                if (iconElements.length === 0) {
                    return { success: false, error: 'لا توجد أيقونات في الصفحة' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testLinksAndButtons() {
            try {
                const buttons = document.querySelectorAll('button, .btn');
                const links = document.querySelectorAll('a[href]');

                if (buttons.length === 0) {
                    return { success: false, error: 'لا توجد أزرار في الصفحة' };
                }

                if (links.length === 0) {
                    return { success: false, error: 'لا توجد روابط في الصفحة' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testMessagesDisplay() {
            try {
                // اختبار إنشاء رسالة تنبيه
                const testMessage = document.createElement('div');
                testMessage.className = 'alert alert-success';
                testMessage.textContent = 'رسالة اختبار';
                document.body.appendChild(testMessage);

                // التحقق من وجودها
                const messageExists = document.body.contains(testMessage);

                // إزالة الرسالة
                document.body.removeChild(testMessage);

                if (!messageExists) {
                    return { success: false, error: 'فشل في عرض الرسائل' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testAnimations() {
            try {
                // فحص دعم CSS animations
                const testElement = document.createElement('div');
                testElement.style.animation = 'test 1s ease';

                if (testElement.style.animation === '') {
                    return { success: false, error: 'CSS animations غير مدعومة' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // === اختبارات الأمان والبنية ===
        function testFileStructure() {
            try {
                const requiredScripts = [
                    'scripts/customer-auth.js',
                    'scripts/currency.js',
                    'scripts/language.js',
                    'database/products.js'
                ];

                // فحص تحميل السكربتات
                const scripts = Array.from(document.querySelectorAll('script[src]'));
                const loadedScripts = scripts.map(s => s.src);

                for (let script of requiredScripts) {
                    const isLoaded = loadedScripts.some(loaded => loaded.includes(script));
                    if (!isLoaded) {
                        return { success: false, error: `الملف ${script} غير محمل` };
                    }
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testScriptConnections() {
            try {
                // فحص الاتصال بين السكربتات
                const connections = [
                    { name: 'customer-auth', check: () => typeof registerCustomer === 'function' },
                    { name: 'currency', check: () => typeof formatCurrency === 'function' },
                    { name: 'language', check: () => typeof changeLanguage === 'function' },
                    { name: 'products', check: () => typeof window.products !== 'undefined' }
                ];

                for (let connection of connections) {
                    if (!connection.check()) {
                        return { success: false, error: `اتصال ${connection.name} فاشل` };
                    }
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testConsoleErrors() {
            try {
                // فحص الأخطاء في الكونسول (محدود)
                const originalError = console.error;
                let errorCount = 0;

                console.error = function(...args) {
                    errorCount++;
                    originalError.apply(console, args);
                };

                // استعادة console.error
                setTimeout(() => {
                    console.error = originalError;
                }, 100);

                if (errorCount > 0) {
                    return { success: false, error: `${errorCount} أخطاء في الكونسول` };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testDataProtection() {
            try {
                // فحص حماية البيانات الحساسة
                const customers = JSON.parse(localStorage.getItem('customers') || '[]');

                for (let customer of customers) {
                    // التحقق من عدم حفظ كلمات المرور بوضوح
                    if (customer.password && customer.password.length < 8) {
                        return { success: false, error: 'كلمات المرور غير محمية بشكل كافٍ' };
                    }
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testSessionValidation() {
            try {
                // اختبار التحقق من الجلسات
                const testToken = 'invalid.token.test';
                localStorage.setItem('customer_token', testToken);

                // محاولة التحقق من التوكن
                const currentCustomer = getCurrentCustomer();

                // تنظيف
                localStorage.removeItem('customer_token');

                // يجب أن يفشل التحقق من التوكن غير الصحيح
                if (currentCustomer) {
                    return { success: false, error: 'التحقق من الجلسة غير آمن' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testUnauthorizedAccess() {
            try {
                // فحص منع الوصول غير المصرح
                localStorage.removeItem('customer_token');
                localStorage.removeItem('customer_data');

                // محاولة الوصول لبيانات محمية
                const currentCustomer = getCurrentCustomer();

                if (currentCustomer) {
                    return { success: false, error: 'يمكن الوصول للبيانات بدون تسجيل دخول' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // === الدوال الرئيسية لتشغيل الاختبارات ===

        async function runFullSystemTest() {
            try {
                systemTester.updateSystemStatus('testing');
                document.getElementById('loadingOverlay').style.display = 'flex';

                // إعادة تعيين النتائج
                systemTester.testResults = { total: 0, passed: 0, failed: 0, warnings: 0 };
                systemTester.problemsFound = [];

                // مسح ملخص المشاكل السابق
                const existingProblems = document.querySelector('.problems-summary');
                if (existingProblems) {
                    existingProblems.remove();
                }

                console.log('🔬 بدء الفحص الشامل للنظام...');

                // تشغيل الاختبارات مع آلية Fail-Safe شاملة
                for (let i = 0; i < systemTester.testSections.length; i++) {
                    const section = systemTester.testSections[i];

                    try {
                        console.log(`📋 فحص قسم: ${section.title}`);
                        await runSectionTestsWithFailSafe(section);
                        systemTester.updateSectionStatus(section.id);

                        // تحديث مؤشر التقدم
                        const progress = Math.round(((i + 1) / systemTester.testSections.length) * 100);
                        updateProgressIndicator(progress, `تم فحص ${section.title}`);

                    } catch (sectionError) {
                        console.error(`❌ خطأ في فحص قسم ${section.title}:`, sectionError);
                        // تسجيل فشل جميع اختبارات هذا القسم
                        markSectionAsFailed(section, `خطأ في فحص القسم: ${sectionError.message}`);
                    }

                    // تأخير قصير لتحسين تجربة المستخدم
                    await new Promise(resolve => setTimeout(resolve, 200));
                }

                console.log('✅ انتهى الفحص الشامل');

            } catch (globalError) {
                console.error('❌ خطأ عام في نظام الفحص:', globalError);
                alert('حدث خطأ في نظام الفحص. يرجى إعادة تحميل الصفحة والمحاولة مرة أخرى.');
            } finally {
                // ضمان إنهاء العملية حتى لو حدث خطأ
                updateSummaryPanel();
                showProblemsummary();
                document.getElementById('loadingOverlay').style.display = 'none';
                systemTester.updateSystemStatus('online');
                updateLastTestTime();
            }
        }

        // دالة تشغيل اختبارات القسم مع Fail-Safe
        async function runSectionTestsWithFailSafe(section) {
            const SECTION_TIMEOUT = 30000; // 30 ثانية لكل قسم

            return new Promise(async (resolve) => {
                // مؤقت للقسم كاملاً
                const sectionTimeout = setTimeout(() => {
                    console.warn(`⚠️ انتهت مهلة قسم ${section.title}`);
                    markSectionAsFailed(section, 'انتهت مهلة فحص القسم (30 ثانية)');
                    resolve();
                }, SECTION_TIMEOUT);

                try {
                    await runSectionTests(section);
                    clearTimeout(sectionTimeout);
                    resolve();
                } catch (error) {
                    clearTimeout(sectionTimeout);
                    console.error(`خطأ في قسم ${section.title}:`, error);
                    markSectionAsFailed(section, error.message);
                    resolve();
                }
            });
        }

        // دالة تسجيل فشل قسم كامل
        function markSectionAsFailed(section, errorMessage) {
            section.tests.forEach(test => {
                systemTester.updateTestResult(
                    section.id,
                    test.method,
                    'error',
                    errorMessage,
                    test
                );
            });
        }

        // دالة تحديث مؤشر التقدم
        function updateProgressIndicator(progress, message) {
            // تحديث شريط التقدم
            const progressBar = document.getElementById('progressBar');
            if (progressBar) {
                progressBar.style.width = `${progress}%`;
            }

            // تحديث رسالة التقدم
            const currentTest = document.getElementById('currentTest');
            if (currentTest) {
                currentTest.textContent = `${message} (${progress}%)`;
            }

            // تحديث حالة النظام
            const statusElement = document.querySelector('.system-status span');
            if (statusElement) {
                statusElement.textContent = `جاري الفحص... (${progress}%)`;
            }
        }

        // دالة تحديث تقدم الاختبار الحالي
        function updateCurrentTestProgress(sectionTitle, testName, testIndex, totalTests) {
            const progress = Math.round((testIndex / totalTests) * 100);
            const currentTest = document.getElementById('currentTest');
            if (currentTest) {
                currentTest.textContent = `${sectionTitle}: ${testName} (${testIndex}/${totalTests})`;
            }

            const progressBar = document.getElementById('progressBar');
            if (progressBar) {
                progressBar.style.width = `${progress}%`;
            }
        }

        async function runSectionTests(section) {
            const totalTests = section.tests.length;

            for (let i = 0; i < section.tests.length; i++) {
                const test = section.tests[i];

                // تحديث مؤشر التقدم للاختبار الحالي
                updateCurrentTestProgress(section.title, test.name, i + 1, totalTests);

                // تشغيل الاختبار مع آلية Timeout وFail-Safe
                await runSingleTestWithTimeout(section, test);

                // تأخير قصير بين الاختبارات
                await new Promise(resolve => setTimeout(resolve, 200));
            }
        }

        // دالة تشغيل اختبار واحد مع آلية Timeout
        async function runSingleTestWithTimeout(section, test) {
            const TIMEOUT_DURATION = 5000; // 5 ثوانٍ

            try {
                // إنشاء Promise للاختبار مع Timeout
                const testPromise = new Promise((resolve, reject) => {
                    try {
                        const testFunction = window[test.method];

                        if (typeof testFunction !== 'function') {
                            resolve({
                                success: false,
                                error: `دالة الاختبار ${test.method} غير موجودة - تحقق من تحميل الملفات المطلوبة`
                            });
                            return;
                        }

                        // تنفيذ الاختبار
                        const result = testFunction();

                        // التحقق من أن النتيجة صحيحة
                        if (result && typeof result === 'object' && typeof result.success === 'boolean') {
                            resolve(result);
                        } else {
                            resolve({
                                success: false,
                                error: 'الاختبار لم يُرجع نتيجة صحيحة'
                            });
                        }

                    } catch (error) {
                        resolve({
                            success: false,
                            error: `خطأ في تنفيذ الاختبار: ${error.message}`
                        });
                    }
                });

                // إنشاء Promise للTimeout
                const timeoutPromise = new Promise((resolve) => {
                    setTimeout(() => {
                        let timeoutMessage = `انتهت مهلة الاختبار (${TIMEOUT_DURATION/1000} ثوانٍ)`;

                        // رسائل مخصصة حسب نوع الاختبار
                        if (test.method.includes('Auth')) {
                            timeoutMessage += ' - تحقق من الكود البرمجي الخاص بتسجيل الدخول والمصادقة';
                        } else if (test.method.includes('Currency')) {
                            timeoutMessage += ' - تحقق من ملف scripts/currency.js';
                        } else if (test.method.includes('Language')) {
                            timeoutMessage += ' - تحقق من ملف scripts/language.js';
                        } else if (test.method.includes('Product')) {
                            timeoutMessage += ' - تحقق من ملف database/products.js';
                        } else {
                            timeoutMessage += ' - قد يكون هناك حلقة لا نهائية أو مشكلة في الكود';
                        }

                        resolve({
                            success: false,
                            error: timeoutMessage
                        });
                    }, TIMEOUT_DURATION);
                });

                // تشغيل الاختبار مع Timeout
                const result = await Promise.race([testPromise, timeoutPromise]);

                // تحديث النتيجة
                if (result.success) {
                    systemTester.updateTestResult(section.id, test.method, 'success', '', test);
                } else {
                    systemTester.updateTestResult(section.id, test.method, 'error', result.error, test);
                }

            } catch (error) {
                // معالجة أي خطأ غير متوقع
                systemTester.updateTestResult(
                    section.id,
                    test.method,
                    'error',
                    `خطأ غير متوقع: ${error.message}`,
                    test
                );
            }
        }

        // تم حذف دالة الفحص السريع حسب المطلوب

        function updateSummaryPanel() {
            const summaryPanel = document.getElementById('summaryPanel');
            summaryPanel.style.display = 'block';

            const { total, passed, failed, warnings } = systemTester.testResults;
            const successRate = total > 0 ? Math.round((passed / total) * 100) : 0;

            document.getElementById('totalTests').textContent = total;
            document.getElementById('passedTests').textContent = passed;
            document.getElementById('failedTests').textContent = failed;
            document.getElementById('successRate').textContent = successRate + '%';

            // تحديث دائرة التقدم
            const progressCircle = document.getElementById('progressCircle');
            const circumference = 2 * Math.PI * 54;
            const offset = circumference - (successRate / 100) * circumference;
            progressCircle.style.strokeDasharray = `${circumference} ${circumference}`;
            progressCircle.style.strokeDashoffset = offset;

            // تحديث الحالة العامة
            const overallStatus = document.getElementById('overallStatus');
            const statusMessage = document.getElementById('statusMessage');

            if (failed === 0) {
                overallStatus.textContent = '🎉 النظام يعمل بشكل مثالي!';
                statusMessage.textContent = 'جميع الاختبارات نجحت بنجاح';
                overallStatus.style.color = 'var(--success-color)';
            } else if (failed < total / 2) {
                overallStatus.textContent = '⚠️ النظام يعمل مع بعض المشاكل';
                statusMessage.textContent = `${failed} اختبار فشل من أصل ${total}`;
                overallStatus.style.color = 'var(--warning-color)';
            } else {
                overallStatus.textContent = '❌ النظام يحتاج إصلاحات';
                statusMessage.textContent = `${failed} اختبار فشل - يرجى المراجعة`;
                overallStatus.style.color = 'var(--danger-color)';
            }
        }

        function resetSystemData() {
            if (confirm('هل أنت متأكد من إعادة تهيئة جميع بيانات النظام؟\nسيتم حذف جميع الحسابات والطلبات والإعدادات.')) {
                // حذف البيانات الأساسية
                const keysToRemove = [
                    'customers',
                    'customer_orders',
                    'contact_messages',
                    'cart',
                    'customer_token',
                    'customer_data',
                    'selected_language',
                    'user_settings'
                ];

                keysToRemove.forEach(key => {
                    localStorage.removeItem(key);
                });

                // إعادة تحميل البيانات الافتراضية
                if (typeof runFinalSetup === 'function') {
                    runFinalSetup();
                }

                alert('تم إعادة تهيئة النظام بنجاح!');
                location.reload();
            }
        }

        function clearAllCache() {
            if (confirm('هل أنت متأكد من مسح جميع بيانات الكاش؟')) {
                // مسح الكاش
                if ('caches' in window) {
                    caches.keys().then(names => {
                        names.forEach(name => {
                            caches.delete(name);
                        });
                    });
                }

                // مسح session storage
                sessionStorage.clear();

                alert('تم مسح الكاش بنجاح!');
                location.reload();
            }
        }

        function updateLastTestTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-IQ', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });

            document.getElementById('lastTestTime').textContent = `آخر فحص: ${timeString}`;
        }

        // === نظام التحليل الذكي والتشخيص المتقدم ===

        class SmartAnalyzer {
            constructor() {
                this.problemCategories = {
                    'auth': 'نظام المصادقة والحماية',
                    'currency': 'نظام العملة والأسعار',
                    'language': 'نظام اللغات المتعددة',
                    'products': 'نظام المنتجات والكتالوج',
                    'cart': 'السلة والطلبات',
                    'ui': 'الواجهة وتجربة المستخدم',
                    'security': 'الأمان والبنية التقنية'
                };

                this.rootCausePatterns = [
                    {
                        pattern: /Cannot read properties of null/,
                        cause: 'عناصر DOM غير موجودة',
                        description: 'محاولة الوصول لعناصر HTML غير موجودة في الصفحة',
                        priority: 'high'
                    },
                    {
                        pattern: /is not a function/,
                        cause: 'دوال غير محملة',
                        description: 'عدم تحميل الملفات المطلوبة أو ترتيب تحميل خاطئ',
                        priority: 'critical'
                    },
                    {
                        pattern: /undefined.*not.*function/,
                        cause: 'مراجع مفقودة',
                        description: 'مراجع لدوال أو متغيرات غير معرفة',
                        priority: 'high'
                    },
                    {
                        pattern: /localStorage.*null/,
                        cause: 'مشاكل التخزين المحلي',
                        description: 'عدم توفر أو مشاكل في localStorage',
                        priority: 'medium'
                    },
                    {
                        pattern: /انتهت مهلة/,
                        cause: 'بطء في الاستجابة',
                        description: 'عمليات تستغرق وقت أطول من المتوقع',
                        priority: 'medium'
                    }
                ];

                this.fixTemplates = {
                    'auth': {
                        title: 'إصلاح نظام المصادقة',
                        steps: [
                            'تحقق من تحميل ملف scripts/customer-auth.js',
                            'راجع دوال registerCustomer و authenticateCustomer',
                            'تأكد من صحة تشفير كلمات المرور',
                            'فحص آلية JWT وانتهاء الصلاحية'
                        ]
                    },
                    'currency': {
                        title: 'إصلاح نظام العملة',
                        steps: [
                            'تحقق من تحميل ملف scripts/currency.js',
                            'راجع إعدادات CURRENCY_CONFIG',
                            'تأكد من صحة رسوم الشحن',
                            'فحص دالة formatCurrency'
                        ]
                    },
                    'language': {
                        title: 'إصلاح نظام اللغات',
                        steps: [
                            'تحقق من تحميل ملف scripts/language.js',
                            'راجع ملفات الترجمة في مجلد locales/',
                            'تأكد من دالة changeLanguage',
                            'فحص تبديل الاتجاه RTL/LTR'
                        ]
                    },
                    'products': {
                        title: 'إصلاح نظام المنتجات',
                        steps: [
                            'تحقق من تحميل ملف database/products.js',
                            'راجع بنية بيانات المنتجات',
                            'تأكد من صحة الأسعار والصور',
                            'فحص حالة التوفر والمخزون'
                        ]
                    },
                    'cart': {
                        title: 'إصلاح نظام السلة',
                        steps: [
                            'راجع دالة addToCart',
                            'تحقق من حساب المجموع',
                            'فحص حفظ البيانات في localStorage',
                            'تأكد من تطبيق رسوم الشحن'
                        ]
                    },
                    'ui': {
                        title: 'إصلاح الواجهة',
                        steps: [
                            'تحقق من تحميل Bootstrap وCSS',
                            'راجع عناصر DOM المطلوبة',
                            'فحص الخطوط والأيقونات',
                            'تأكد من التجاوب على الأجهزة المختلفة'
                        ]
                    },
                    'security': {
                        title: 'إصلاح الأمان',
                        steps: [
                            'راجع ترتيب تحميل السكربتات',
                            'تحقق من تشفير البيانات الحساسة',
                            'فحص صحة JWT tokens',
                            'تأكد من حماية الوصول غير المصرح'
                        ]
                    }
                };
            }

            analyzeResults(testResults, problemsFound) {
                const analysis = {
                    summary: this.generateSummary(testResults, problemsFound),
                    categoryBreakdown: this.analyzeProblemsByCategory(problemsFound),
                    rootCauses: this.identifyRootCauses(problemsFound),
                    fixPlan: this.generateFixPlan(problemsFound),
                    healthScore: this.calculateHealthScore(testResults)
                };

                return analysis;
            }

            generateSummary(testResults, problemsFound) {
                const { total, passed, failed } = testResults;
                const criticalIssues = problemsFound.filter(p => p.priority === 'critical').length;
                const rootCauses = this.identifyRootCauses(problemsFound).length;
                const fixSteps = this.generateFixPlan(problemsFound).length;

                return {
                    totalTests: total,
                    passedTests: passed,
                    failedTests: failed,
                    criticalIssues,
                    rootCauses,
                    fixSteps,
                    successRate: total > 0 ? Math.round((passed / total) * 100) : 0
                };
            }

            analyzeProblemsByCategory(problemsFound) {
                const categoryCount = {};

                // تهيئة العدادات
                Object.keys(this.problemCategories).forEach(key => {
                    categoryCount[key] = 0;
                });

                // عد المشاكل حسب الفئة
                problemsFound.forEach(problem => {
                    if (problem.category && categoryCount.hasOwnProperty(problem.category)) {
                        categoryCount[problem.category]++;
                    }
                });

                // تحويل إلى مصفوفة مرتبة
                return Object.entries(categoryCount)
                    .map(([key, count]) => ({
                        category: key,
                        name: this.problemCategories[key],
                        count: count,
                        percentage: problemsFound.length > 0 ? Math.round((count / problemsFound.length) * 100) : 0
                    }))
                    .filter(item => item.count > 0)
                    .sort((a, b) => b.count - a.count);
            }

            identifyRootCauses(problemsFound) {
                const rootCauses = new Map();

                problemsFound.forEach(problem => {
                    const errorMessage = problem.error || '';

                    // البحث عن أنماط الأخطاء المعروفة
                    for (const pattern of this.rootCausePatterns) {
                        if (pattern.pattern.test(errorMessage)) {
                            const key = pattern.cause;
                            if (!rootCauses.has(key)) {
                                rootCauses.set(key, {
                                    cause: pattern.cause,
                                    description: pattern.description,
                                    priority: pattern.priority,
                                    count: 0,
                                    examples: []
                                });
                            }

                            const rootCause = rootCauses.get(key);
                            rootCause.count++;
                            if (rootCause.examples.length < 3) {
                                rootCause.examples.push(problem.testName || 'اختبار غير محدد');
                            }
                            break;
                        }
                    }
                });

                return Array.from(rootCauses.values())
                    .sort((a, b) => {
                        // ترتيب حسب الأولوية ثم العدد
                        const priorityOrder = { 'critical': 3, 'high': 2, 'medium': 1, 'low': 0 };
                        const aPriority = priorityOrder[a.priority] || 0;
                        const bPriority = priorityOrder[b.priority] || 0;

                        if (aPriority !== bPriority) {
                            return bPriority - aPriority;
                        }
                        return b.count - a.count;
                    });
            }

            generateFixPlan(problemsFound) {
                const categoryProblems = this.analyzeProblemsByCategory(problemsFound);
                const fixPlan = [];

                categoryProblems.forEach((categoryData, index) => {
                    const template = this.fixTemplates[categoryData.category];
                    if (template) {
                        fixPlan.push({
                            step: index + 1,
                            title: template.title,
                            category: categoryData.name,
                            problemCount: categoryData.count,
                            priority: categoryData.count > 2 ? 'high' : 'medium',
                            actions: template.steps,
                            estimatedTime: this.estimateFixTime(categoryData.count)
                        });
                    }
                });

                return fixPlan;
            }

            estimateFixTime(problemCount) {
                if (problemCount <= 1) return '15-30 دقيقة';
                if (problemCount <= 3) return '30-60 دقيقة';
                if (problemCount <= 5) return '1-2 ساعة';
                return '2-4 ساعات';
            }

            calculateHealthScore(testResults) {
                const { total, passed } = testResults;
                if (total === 0) return 0;

                const baseScore = Math.round((passed / total) * 100);

                // تعديل النتيجة حسب نوع المشاكل
                // يمكن إضافة منطق أكثر تعقيداً هنا

                return Math.max(0, Math.min(100, baseScore));
            }
        }

        // إنشاء مثيل من المحلل الذكي
        const smartAnalyzer = new SmartAnalyzer();

        // دالة عرض التحليل الذكي
        function displaySmartAnalysis() {
            const analysis = smartAnalyzer.analyzeResults(systemTester.testResults, systemTester.problemsFound);

            // عرض لوحة التحليل
            const analysisPanel = document.getElementById('smartAnalysisPanel');
            analysisPanel.style.display = 'block';

            // تحديث الإحصائيات
            document.getElementById('criticalIssuesCount').textContent = analysis.summary.criticalIssues;
            document.getElementById('rootCausesCount').textContent = analysis.summary.rootCauses;
            document.getElementById('fixStepsCount').textContent = analysis.summary.fixSteps;
            document.getElementById('systemHealthScore').textContent = analysis.summary.successRate + '%';

            // عرض توزيع المشاكل حسب الفئة
            displayCategoryBreakdown(analysis.categoryBreakdown);

            // عرض الأسباب الجذرية
            displayRootCauses(analysis.rootCauses);

            // عرض خطة الإصلاح
            displayFixPlan(analysis.fixPlan);
        }

        function displayCategoryBreakdown(categoryBreakdown) {
            const container = document.getElementById('categoryBreakdownContent');

            if (categoryBreakdown.length === 0) {
                container.innerHTML = '<div class="category-item"><span class="category-name">🎉 لا توجد مشاكل مكتشفة</span></div>';
                return;
            }

            container.innerHTML = categoryBreakdown.map(item => `
                <div class="category-item">
                    <span class="category-name">${item.name}</span>
                    <span class="category-count">${item.count} مشكلة (${item.percentage}%)</span>
                </div>
            `).join('');
        }

        function displayRootCauses(rootCauses) {
            const container = document.getElementById('rootCausesContent');

            if (rootCauses.length === 0) {
                container.innerHTML = '<div class="root-cause-item"><div class="root-cause-title">✅ لا توجد أسباب جذرية مكتشفة</div></div>';
                return;
            }

            container.innerHTML = rootCauses.map(cause => `
                <div class="root-cause-item">
                    <div class="root-cause-title">
                        ${getPriorityIcon(cause.priority)} ${cause.cause} (${cause.count} مرة)
                    </div>
                    <div class="root-cause-description">
                        ${cause.description}
                        ${cause.examples.length > 0 ? `<br><strong>أمثلة:</strong> ${cause.examples.join(', ')}` : ''}
                    </div>
                </div>
            `).join('');
        }

        function displayFixPlan(fixPlan) {
            const container = document.getElementById('priorityFixPlanContent');

            if (fixPlan.length === 0) {
                container.innerHTML = '<div class="fix-step"><div class="fix-step-title">🎉 لا توجد إصلاحات مطلوبة</div><div class="fix-step-description">النظام يعمل بشكل مثالي!</div></div>';
                return;
            }

            container.innerHTML = fixPlan.map(step => `
                <div class="fix-step">
                    <div class="fix-step-number">${step.step}</div>
                    <div class="fix-step-title">
                        ${getPriorityIcon(step.priority)} ${step.title}
                    </div>
                    <div class="fix-step-description">
                        <strong>الفئة:</strong> ${step.category} (${step.problemCount} مشكلة)<br>
                        <strong>الوقت المتوقع:</strong> ${step.estimatedTime}
                    </div>
                    <div class="fix-step-action">
                        ${step.actions.map((action, index) => `${index + 1}. ${action}`).join('<br>')}
                    </div>
                </div>
            `).join('');
        }

        function getPriorityIcon(priority) {
            const icons = {
                'critical': '🔴',
                'high': '🟠',
                'medium': '🟡',
                'low': '🟢'
            };
            return icons[priority] || '⚪';
        }

        // دالة نسخ التحليل للذكاء الاصطناعي
        function copyAnalysisToClipboard() {
            const analysis = smartAnalyzer.analyzeResults(systemTester.testResults, systemTester.problemsFound);

            const analysisText = `
# تقرير التحليل الذكي لنظام VelaSweets

## ملخص النتائج
- إجمالي الاختبارات: ${analysis.summary.totalTests}
- الاختبارات الناجحة: ${analysis.summary.passedTests}
- الاختبارات الفاشلة: ${analysis.summary.failedTests}
- معدل النجاح: ${analysis.summary.successRate}%
- المشاكل الحرجة: ${analysis.summary.criticalIssues}
- الأسباب الجذرية: ${analysis.summary.rootCauses}

## توزيع المشاكل حسب الفئة
${analysis.categoryBreakdown.map(item => `- ${item.name}: ${item.count} مشكلة (${item.percentage}%)`).join('\n')}

## الأسباب الجذرية المكتشفة
${analysis.rootCauses.map(cause => `
### ${cause.cause} (${cause.count} مرة)
${cause.description}
الأولوية: ${cause.priority}
أمثلة: ${cause.examples.join(', ')}
`).join('\n')}

## خطة الإصلاح المرتبة بالأولوية
${analysis.fixPlan.map(step => `
### ${step.step}. ${step.title}
الفئة: ${step.category} (${step.problemCount} مشكلة)
الوقت المتوقع: ${step.estimatedTime}
الأولوية: ${step.priority}

خطوات الإصلاح:
${step.actions.map((action, index) => `${index + 1}. ${action}`).join('\n')}
`).join('\n')}

---
تم إنشاء هذا التقرير بواسطة نظام التحليل الذكي لـ VelaSweets
التاريخ: ${new Date().toLocaleString('ar-IQ')}
            `.trim();

            navigator.clipboard.writeText(analysisText).then(() => {
                alert('✅ تم نسخ التحليل بنجاح!\nيمكنك الآن لصقه للذكاء الاصطناعي أو فريق التطوير.');
            }).catch(err => {
                console.error('خطأ في النسخ:', err);
                // عرض النص في نافذة منبثقة كبديل
                const popup = window.open('', '_blank', 'width=800,height=600');
                popup.document.write(`<pre style="font-family: Arial; padding: 20px; direction: rtl;">${analysisText}</pre>`);
            });
        }

        // دالة إنشاء تقرير مفصل
        function generateDetailedReport() {
            const analysis = smartAnalyzer.analyzeResults(systemTester.testResults, systemTester.problemsFound);

            // إنشاء تقرير HTML مفصل
            const reportHTML = `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير التحليل المفصل - VelaSweets</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
        h2 { color: #34495e; margin-top: 30px; }
        .metric { display: inline-block; margin: 10px; padding: 15px; background: #ecf0f1; border-radius: 8px; min-width: 120px; text-align: center; }
        .metric-value { font-size: 2em; font-weight: bold; color: #2980b9; }
        .metric-label { color: #7f8c8d; }
        .problem-item { margin: 15px 0; padding: 15px; border-left: 4px solid #e74c3c; background: #fdf2f2; border-radius: 5px; }
        .fix-item { margin: 15px 0; padding: 15px; border-left: 4px solid #27ae60; background: #f2fdf5; border-radius: 5px; }
        .priority-critical { border-left-color: #e74c3c; }
        .priority-high { border-left-color: #f39c12; }
        .priority-medium { border-left-color: #f1c40f; }
        .priority-low { border-left-color: #27ae60; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧠 تقرير التحليل الذكي المفصل - VelaSweets</h1>

        <div class="metrics">
            <div class="metric">
                <div class="metric-value">${analysis.summary.totalTests}</div>
                <div class="metric-label">إجمالي الاختبارات</div>
            </div>
            <div class="metric">
                <div class="metric-value">${analysis.summary.passedTests}</div>
                <div class="metric-label">نجح</div>
            </div>
            <div class="metric">
                <div class="metric-value">${analysis.summary.failedTests}</div>
                <div class="metric-label">فشل</div>
            </div>
            <div class="metric">
                <div class="metric-value">${analysis.summary.successRate}%</div>
                <div class="metric-label">معدل النجاح</div>
            </div>
        </div>

        <h2>📊 توزيع المشاكل حسب الفئة</h2>
        ${analysis.categoryBreakdown.map(item => `
            <div class="problem-item">
                <strong>${item.name}</strong>: ${item.count} مشكلة (${item.percentage}%)
            </div>
        `).join('')}

        <h2>🔍 الأسباب الجذرية</h2>
        ${analysis.rootCauses.map(cause => `
            <div class="problem-item priority-${cause.priority}">
                <h3>${cause.cause} (${cause.count} مرة)</h3>
                <p>${cause.description}</p>
                <p><strong>أمثلة:</strong> ${cause.examples.join(', ')}</p>
            </div>
        `).join('')}

        <h2>🛠️ خطة الإصلاح المرتبة</h2>
        ${analysis.fixPlan.map(step => `
            <div class="fix-item priority-${step.priority}">
                <h3>${step.step}. ${step.title}</h3>
                <p><strong>الفئة:</strong> ${step.category} (${step.problemCount} مشكلة)</p>
                <p><strong>الوقت المتوقع:</strong> ${step.estimatedTime}</p>
                <h4>خطوات الإصلاح:</h4>
                <ol>
                    ${step.actions.map(action => `<li>${action}</li>`).join('')}
                </ol>
            </div>
        `).join('')}

        <hr>
        <p style="text-align: center; color: #7f8c8d;">
            تم إنشاء هذا التقرير بواسطة نظام التحليل الذكي لـ VelaSweets<br>
            التاريخ: ${new Date().toLocaleString('ar-IQ')}
        </p>
    </div>
</body>
</html>
            `;

            // فتح التقرير في نافذة جديدة
            const reportWindow = window.open('', '_blank', 'width=1200,height=800');
            reportWindow.document.write(reportHTML);
            reportWindow.document.close();
        }

        // دالة تصدير خطة الإصلاح
        function exportFixPlan() {
            const analysis = smartAnalyzer.analyzeResults(systemTester.testResults, systemTester.problemsFound);

            const fixPlanText = `
خطة الإصلاح المرتبة بالأولوية - VelaSweets
=============================================

${analysis.fixPlan.map(step => `
${step.step}. ${step.title}
${'='.repeat(step.title.length + 3)}

الفئة: ${step.category}
عدد المشاكل: ${step.problemCount}
الأولوية: ${step.priority}
الوقت المتوقع: ${step.estimatedTime}

خطوات الإصلاح:
${step.actions.map((action, index) => `  ${index + 1}. ${action}`).join('\n')}

`).join('\n')}

---
تم إنشاء هذه الخطة في: ${new Date().toLocaleString('ar-IQ')}
            `.trim();

            // إنشاء ملف للتحميل
            const blob = new Blob([fixPlanText], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `خطة_الإصلاح_VelaSweets_${new Date().toISOString().split('T')[0]}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // تحديث دالة showProblemsummary لتشمل التحليل الذكي
        function showProblemsummary() {
            // الكود الموجود...

            // إضافة التحليل الذكي
            if (systemTester.testResults.failed > 0) {
                displaySmartAnalysis();
            }
        }
    </script>
</body>
</html>
