<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 لوحة اختبار النظام الإدارية - VelaSweets</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap');
        
        :root {
            --primary-color: #5a287d;
            --secondary-color: #f8a5c2;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --dark-color: #343a40;
            --light-color: #f8f9fa;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            padding: 20px;
            color: var(--dark-color);
        }
        
        .admin-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            overflow: hidden;
        }
        
        .admin-header {
            background: linear-gradient(135deg, var(--primary-color), #6a3093);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        
        .admin-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        
        .admin-header h1 {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }
        
        .admin-header p {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        
        .control-panel {
            background: var(--light-color);
            padding: 20px 30px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .system-status {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 15px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .status-indicator.online {
            background: rgba(40, 167, 69, 0.1);
            color: var(--success-color);
            border: 2px solid rgba(40, 167, 69, 0.3);
        }
        
        .status-indicator.testing {
            background: rgba(255, 193, 7, 0.1);
            color: var(--warning-color);
            border: 2px solid rgba(255, 193, 7, 0.3);
            animation: pulse 2s infinite;
        }
        
        .status-indicator.error {
            background: rgba(220, 53, 69, 0.1);
            color: var(--danger-color);
            border: 2px solid rgba(220, 53, 69, 0.3);
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        .control-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn-admin {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-admin.primary {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-admin.primary:hover {
            background: #4a1f6b;
            transform: translateY(-2px);
        }
        
        .btn-admin.success {
            background: var(--success-color);
            color: white;
        }
        
        .btn-admin.success:hover {
            background: #218838;
            transform: translateY(-2px);
        }
        
        .btn-admin.warning {
            background: var(--warning-color);
            color: var(--dark-color);
        }
        
        .btn-admin.warning:hover {
            background: #e0a800;
            transform: translateY(-2px);
        }
        
        .btn-admin.danger {
            background: var(--danger-color);
            color: white;
        }
        
        .btn-admin.danger:hover {
            background: #c82333;
            transform: translateY(-2px);
        }
        
        .test-content {
            padding: 30px;
        }
        
        .test-section {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            margin-bottom: 25px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .test-section:hover {
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .test-section.success {
            border-color: var(--success-color);
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.05), rgba(40, 167, 69, 0.02));
        }
        
        .test-section.error {
            border-color: var(--danger-color);
            background: linear-gradient(135deg, rgba(220, 53, 69, 0.05), rgba(220, 53, 69, 0.02));
        }
        
        .test-section.warning {
            border-color: var(--warning-color);
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.05), rgba(255, 193, 7, 0.02));
        }
        
        .section-header {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 20px 25px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .section-status {
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 600;
        }
        
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-badge.success {
            background: var(--success-color);
            color: white;
        }
        
        .status-badge.error {
            background: var(--danger-color);
            color: white;
        }
        
        .status-badge.warning {
            background: var(--warning-color);
            color: var(--dark-color);
        }
        
        .status-badge.testing {
            background: var(--info-color);
            color: white;
            animation: pulse 1.5s infinite;
        }
        
        .test-items {
            padding: 25px;
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        
        .test-item:last-child {
            border-bottom: none;
        }
        
        .test-item:hover {
            background: rgba(0,0,0,0.02);
            padding-left: 10px;
            border-radius: 8px;
        }
        
        .test-description {
            flex: 1;
            font-weight: 500;
            color: var(--dark-color);
        }
        
        .test-result {
            display: flex;
            align-items: center;
            gap: 10px;
            min-width: 120px;
            justify-content: flex-end;
        }
        
        .result-icon {
            font-size: 1.2rem;
            font-weight: bold;
        }
        
        .result-icon.success {
            color: var(--success-color);
        }
        
        .result-icon.error {
            color: var(--danger-color);
        }
        
        .result-icon.warning {
            color: var(--warning-color);
        }
        
        .result-icon.testing {
            color: var(--info-color);
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .result-text {
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .error-details {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid rgba(220, 53, 69, 0.3);
            border-radius: 8px;
            padding: 12px;
            margin-top: 10px;
            font-size: 0.85rem;
            color: var(--danger-color);
            display: none;
        }
        
        .error-details.show {
            display: block;
            animation: slideDown 0.3s ease;
        }
        
        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .summary-panel {
            background: linear-gradient(135deg, var(--primary-color), #6a3093);
            color: white;
            padding: 30px;
            margin-top: 30px;
            border-radius: 15px;
            text-align: center;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }
        
        .stat-card {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 12px;
            backdrop-filter: blur(10px);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .progress-ring {
            width: 120px;
            height: 120px;
            margin: 0 auto 20px;
        }
        
        .progress-ring circle {
            fill: none;
            stroke-width: 8;
            stroke-linecap: round;
            transform: rotate(-90deg);
            transform-origin: 50% 50%;
        }
        
        .progress-ring .background {
            stroke: rgba(255,255,255,0.2);
        }
        
        .progress-ring .progress {
            stroke: white;
            stroke-dasharray: 0 377;
            transition: stroke-dasharray 1s ease;
        }
        
        .responsive-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
        }
        
        @media (max-width: 768px) {
            .admin-container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .admin-header {
                padding: 20px;
            }
            
            .admin-header h1 {
                font-size: 2rem;
            }
            
            .control-panel {
                padding: 15px 20px;
                flex-direction: column;
                align-items: stretch;
            }
            
            .system-status {
                justify-content: center;
                margin-bottom: 15px;
            }
            
            .control-buttons {
                justify-content: center;
            }
            
            .test-content {
                padding: 20px;
            }
            
            .responsive-grid {
                grid-template-columns: 1fr;
            }
            
            .summary-stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .loading-content {
            background: white;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            max-width: 400px;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
    </style>
</head>
<body>
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h4>جاري تشغيل الاختبارات...</h4>
            <p>يرجى الانتظار حتى اكتمال فحص النظام</p>
        </div>
    </div>

    <div class="admin-container">
        <!-- Header -->
        <div class="admin-header">
            <h1><i class="fas fa-cogs me-3"></i>لوحة اختبار النظام الإدارية</h1>
            <p>فحص شامل ومتقدم لجميع مكونات نظام VelaSweets</p>
        </div>

        <!-- Control Panel -->
        <div class="control-panel">
            <div class="system-status">
                <div class="status-indicator online" id="systemStatus">
                    <i class="fas fa-circle"></i>
                    <span>النظام متصل</span>
                </div>
                <div id="lastTestTime">
                    آخر فحص: لم يتم بعد
                </div>
            </div>
            
            <div class="control-buttons">
                <button class="btn-admin primary" onclick="runFullSystemTest()">
                    <i class="fas fa-play"></i>
                    تشغيل فحص شامل
                </button>
                <button class="btn-admin success" onclick="runQuickTest()">
                    <i class="fas fa-bolt"></i>
                    فحص سريع
                </button>
                <button class="btn-admin warning" onclick="resetSystemData()">
                    <i class="fas fa-refresh"></i>
                    إعادة تهيئة
                </button>
                <button class="btn-admin danger" onclick="clearAllCache()">
                    <i class="fas fa-trash"></i>
                    مسح الكاش
                </button>
            </div>
        </div>

        <!-- Test Content -->
        <div class="test-content">
            <div class="responsive-grid" id="testSections">
                <!-- Test sections will be dynamically generated here -->
            </div>

            <!-- Summary Panel -->
            <div class="summary-panel" id="summaryPanel" style="display: none;">
                <h3><i class="fas fa-chart-pie me-3"></i>ملخص نتائج الفحص</h3>
                
                <div class="summary-stats">
                    <div class="stat-card">
                        <div class="stat-number" id="totalTests">0</div>
                        <div class="stat-label">إجمالي الاختبارات</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="passedTests">0</div>
                        <div class="stat-label">نجح</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="failedTests">0</div>
                        <div class="stat-label">فشل</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="successRate">0%</div>
                        <div class="stat-label">معدل النجاح</div>
                    </div>
                </div>

                <svg class="progress-ring" id="progressRing">
                    <circle class="background" cx="60" cy="60" r="54"></circle>
                    <circle class="progress" cx="60" cy="60" r="54" id="progressCircle"></circle>
                </svg>
                
                <h4 id="overallStatus">جاري الفحص...</h4>
                <p id="statusMessage">يرجى الانتظار حتى اكتمال جميع الاختبارات</p>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="scripts/customer-auth.js"></script>
    <script src="scripts/currency.js"></script>
    <script src="scripts/language.js"></script>
    <script src="database/products.js"></script>
    <script src="init-data.js"></script>
    <script src="final-setup.js"></script>

    <script>
        // نظام اختبار شامل ومتقدم لـ VelaSweets
        class SystemTester {
            constructor() {
                this.testResults = {
                    total: 0,
                    passed: 0,
                    failed: 0,
                    warnings: 0
                };

                this.testSections = [
                    {
                        id: 'auth',
                        title: 'نظام المصادقة',
                        icon: 'fas fa-shield-alt',
                        tests: [
                            { name: 'تحميل ملفات المصادقة', method: 'testAuthFilesLoaded' },
                            { name: 'تسجيل حساب جديد', method: 'testUserRegistration' },
                            { name: 'تسجيل الدخول', method: 'testUserLogin' },
                            { name: 'التحقق من رقم الهاتف العراقي', method: 'testPhoneValidation' },
                            { name: 'منع الحسابات المكررة', method: 'testDuplicatePrevention' },
                            { name: 'أمان كلمات المرور', method: 'testPasswordSecurity' }
                        ]
                    },
                    {
                        id: 'currency',
                        title: 'نظام العملة',
                        icon: 'fas fa-coins',
                        tests: [
                            { name: 'تحميل نظام العملة', method: 'testCurrencySystemLoaded' },
                            { name: 'تثبيت الدينار العراقي', method: 'testIQDCurrency' },
                            { name: 'تنسيق عرض الأسعار', method: 'testPriceFormatting' },
                            { name: 'حساب رسوم الشحن للبصرة', method: 'testBasraShipping' },
                            { name: 'حساب رسوم الشحن للمحافظات الأخرى', method: 'testOtherProvincesShipping' },
                            { name: 'تحويل العملات القديمة', method: 'testCurrencyConversion' }
                        ]
                    },
                    {
                        id: 'language',
                        title: 'نظام اللغات',
                        icon: 'fas fa-globe',
                        tests: [
                            { name: 'تحميل نظام اللغات', method: 'testLanguageSystemLoaded' },
                            { name: 'دعم اللغة العربية (RTL)', method: 'testArabicLanguage' },
                            { name: 'دعم اللغة الكردية (RTL)', method: 'testKurdishLanguage' },
                            { name: 'دعم اللغة الإنجليزية (LTR)', method: 'testEnglishLanguage' },
                            { name: 'تغيير الاتجاه تلقائياً', method: 'testDirectionSwitching' },
                            { name: 'حفظ اللغة المختارة', method: 'testLanguagePersistence' }
                        ]
                    },
                    {
                        id: 'products',
                        title: 'نظام المنتجات',
                        icon: 'fas fa-box',
                        tests: [
                            { name: 'تحميل قاعدة بيانات المنتجات', method: 'testProductsLoaded' },
                            { name: 'صحة أسعار المنتجات', method: 'testProductPrices' },
                            { name: 'تصنيف المنتجات', method: 'testProductCategories' },
                            { name: 'صور المنتجات', method: 'testProductImages' },
                            { name: 'توفر المنتجات', method: 'testProductAvailability' },
                            { name: 'بيانات المنتجات المكتملة', method: 'testProductDataIntegrity' }
                        ]
                    },
                    {
                        id: 'cart',
                        title: 'السلة والطلبات',
                        icon: 'fas fa-shopping-cart',
                        tests: [
                            { name: 'إضافة منتج للسلة', method: 'testAddToCart' },
                            { name: 'حساب مجموع السلة', method: 'testCartTotal' },
                            { name: 'تطبيق رسوم الشحن', method: 'testShippingApplication' },
                            { name: 'حفظ الطلبات', method: 'testOrderSaving' },
                            { name: 'تحديث كمية المنتجات', method: 'testQuantityUpdate' },
                            { name: 'إزالة منتجات من السلة', method: 'testRemoveFromCart' }
                        ]
                    },
                    {
                        id: 'ui',
                        title: 'الواجهة والمظهر',
                        icon: 'fas fa-desktop',
                        tests: [
                            { name: 'تجاوب التصميم', method: 'testResponsiveDesign' },
                            { name: 'عرض خيارات المستخدم', method: 'testUserOptionsDisplay' },
                            { name: 'تحميل الخطوط والأيقونات', method: 'testFontsAndIcons' },
                            { name: 'عمل الروابط والأزرار', method: 'testLinksAndButtons' },
                            { name: 'عرض الرسائل والتنبيهات', method: 'testMessagesDisplay' },
                            { name: 'سلاسة الانتقالات', method: 'testAnimations' }
                        ]
                    },
                    {
                        id: 'security',
                        title: 'الأمان والبنية',
                        icon: 'fas fa-lock',
                        tests: [
                            { name: 'هيكل الملفات', method: 'testFileStructure' },
                            { name: 'ترابط السكربتات', method: 'testScriptConnections' },
                            { name: 'عدم وجود أخطاء في الكونسول', method: 'testConsoleErrors' },
                            { name: 'حماية البيانات الحساسة', method: 'testDataProtection' },
                            { name: 'التحقق من الجلسات', method: 'testSessionValidation' },
                            { name: 'منع الوصول غير المصرح', method: 'testUnauthorizedAccess' }
                        ]
                    }
                ];

                this.init();
            }

            init() {
                this.renderTestSections();
                this.updateSystemStatus('online');
                console.log('🔧 نظام اختبار VelaSweets جاهز للعمل');
            }

            renderTestSections() {
                const container = document.getElementById('testSections');
                container.innerHTML = '';

                this.testSections.forEach(section => {
                    const sectionElement = this.createTestSection(section);
                    container.appendChild(sectionElement);
                });
            }

            createTestSection(section) {
                const sectionDiv = document.createElement('div');
                sectionDiv.className = 'test-section';
                sectionDiv.id = `section-${section.id}`;

                sectionDiv.innerHTML = `
                    <div class="section-header">
                        <div class="section-title">
                            <i class="${section.icon}"></i>
                            ${section.title}
                        </div>
                        <div class="section-status">
                            <span class="status-badge testing" id="badge-${section.id}">
                                في الانتظار
                            </span>
                        </div>
                    </div>
                    <div class="test-items" id="items-${section.id}">
                        ${section.tests.map(test => `
                            <div class="test-item" id="test-${section.id}-${test.method}">
                                <div class="test-description">${test.name}</div>
                                <div class="test-result">
                                    <span class="result-icon testing" id="icon-${section.id}-${test.method}">
                                        <i class="fas fa-clock"></i>
                                    </span>
                                    <span class="result-text" id="text-${section.id}-${test.method}">
                                        في الانتظار
                                    </span>
                                </div>
                                <div class="error-details" id="error-${section.id}-${test.method}"></div>
                            </div>
                        `).join('')}
                    </div>
                `;

                return sectionDiv;
            }

            updateSystemStatus(status) {
                const statusElement = document.getElementById('systemStatus');
                const statusClasses = {
                    'online': { class: 'online', icon: 'fa-circle', text: 'النظام متصل' },
                    'testing': { class: 'testing', icon: 'fa-cog', text: 'جاري الفحص' },
                    'error': { class: 'error', icon: 'fa-exclamation-triangle', text: 'خطأ في النظام' }
                };

                const config = statusClasses[status];
                statusElement.className = `status-indicator ${config.class}`;
                statusElement.innerHTML = `
                    <i class="fas ${config.icon}"></i>
                    <span>${config.text}</span>
                `;
            }

            updateTestResult(sectionId, testMethod, result, errorMessage = '') {
                const iconElement = document.getElementById(`icon-${sectionId}-${testMethod}`);
                const textElement = document.getElementById(`text-${sectionId}-${testMethod}`);
                const errorElement = document.getElementById(`error-${sectionId}-${testMethod}`);

                this.testResults.total++;

                if (result === 'success') {
                    this.testResults.passed++;
                    iconElement.className = 'result-icon success';
                    iconElement.innerHTML = '<i class="fas fa-check"></i>';
                    textElement.textContent = 'نجح';
                    textElement.style.color = 'var(--success-color)';
                } else if (result === 'error') {
                    this.testResults.failed++;
                    iconElement.className = 'result-icon error';
                    iconElement.innerHTML = '<i class="fas fa-times"></i>';
                    textElement.textContent = 'فشل';
                    textElement.style.color = 'var(--danger-color)';

                    if (errorMessage) {
                        errorElement.textContent = errorMessage;
                        errorElement.classList.add('show');
                    }
                } else if (result === 'warning') {
                    this.testResults.warnings++;
                    iconElement.className = 'result-icon warning';
                    iconElement.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
                    textElement.textContent = 'تحذير';
                    textElement.style.color = 'var(--warning-color)';

                    if (errorMessage) {
                        errorElement.textContent = errorMessage;
                        errorElement.classList.add('show');
                    }
                }
            }

            updateSectionStatus(sectionId) {
                const section = this.testSections.find(s => s.id === sectionId);
                const badge = document.getElementById(`badge-${sectionId}`);
                const sectionElement = document.getElementById(`section-${sectionId}`);

                let passedCount = 0;
                let failedCount = 0;
                let warningCount = 0;

                section.tests.forEach(test => {
                    const iconElement = document.getElementById(`icon-${sectionId}-${test.method}`);
                    if (iconElement.classList.contains('success')) passedCount++;
                    else if (iconElement.classList.contains('error')) failedCount++;
                    else if (iconElement.classList.contains('warning')) warningCount++;
                });

                if (failedCount > 0) {
                    badge.className = 'status-badge error';
                    badge.textContent = `${failedCount} فشل`;
                    sectionElement.classList.add('error');
                } else if (warningCount > 0) {
                    badge.className = 'status-badge warning';
                    badge.textContent = `${warningCount} تحذير`;
                    sectionElement.classList.add('warning');
                } else if (passedCount === section.tests.length) {
                    badge.className = 'status-badge success';
                    badge.textContent = 'نجح بالكامل';
                    sectionElement.classList.add('success');
                }
            }
        }

        // إنشاء مثيل من نظام الاختبار
        let systemTester;

        document.addEventListener('DOMContentLoaded', function() {
            systemTester = new SystemTester();
        });

        // دوال الاختبار الفعلية

        // === اختبارات نظام المصادقة ===
        function testAuthFilesLoaded() {
            try {
                const requiredFunctions = [
                    'registerCustomer',
                    'authenticateCustomer',
                    'validateInput',
                    'checkDuplicateCustomer',
                    'getCurrentCustomer'
                ];

                for (let func of requiredFunctions) {
                    if (typeof window[func] !== 'function') {
                        return { success: false, error: `الدالة ${func} غير موجودة` };
                    }
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testUserRegistration() {
            try {
                const testData = {
                    fullName: 'مستخدم تجريبي ' + Date.now(),
                    email: 'test' + Date.now() + '@example.com',
                    phone: '07' + Math.floor(Math.random() * 1000000000).toString().padStart(9, '0'),
                    province: 'بغداد',
                    address: 'عنوان تجريبي',
                    password: 'Test123!'
                };

                const result = registerCustomer(testData);

                if (result.success) {
                    return { success: true };
                } else {
                    return { success: false, error: result.message };
                }
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testUserLogin() {
            try {
                // الحصول على آخر عميل مسجل
                const customers = JSON.parse(localStorage.getItem('customers') || '[]');
                if (customers.length === 0) {
                    return { success: false, error: 'لا توجد حسابات للاختبار' };
                }

                const testCustomer = customers[customers.length - 1];
                const result = authenticateCustomer(testCustomer.email, testCustomer.password);

                if (result.success) {
                    return { success: true };
                } else {
                    return { success: false, error: result.message };
                }
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testPhoneValidation() {
            try {
                const validPhones = ['07123456789', '07987654321', '07555666777'];
                const invalidPhones = ['123456789', '7123456789', '071234567890', '07abc123456'];

                // اختبار الأرقام الصحيحة
                for (let phone of validPhones) {
                    const result = validateInput(phone, 'phone');
                    if (!result.valid) {
                        return { success: false, error: `الرقم ${phone} يجب أن يكون صحيحاً` };
                    }
                }

                // اختبار الأرقام الخاطئة
                for (let phone of invalidPhones) {
                    const result = validateInput(phone, 'phone');
                    if (result.valid) {
                        return { success: false, error: `الرقم ${phone} يجب أن يكون خاطئاً` };
                    }
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testDuplicatePrevention() {
            try {
                const customers = JSON.parse(localStorage.getItem('customers') || '[]');
                if (customers.length === 0) {
                    return { success: false, error: 'لا توجد حسابات للاختبار' };
                }

                const existingCustomer = customers[0];
                const duplicateCheck = checkDuplicateCustomer(existingCustomer.email, existingCustomer.phone);

                if (duplicateCheck.isDuplicate) {
                    return { success: true };
                } else {
                    return { success: false, error: 'لم يتم اكتشاف التكرار' };
                }
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testPasswordSecurity() {
            try {
                const weakPasswords = ['123', 'password', '123456'];
                const strongPasswords = ['Test123!', 'MyStr0ng@Pass', 'Secure#2024'];

                // يجب رفض كلمات المرور الضعيفة
                for (let password of weakPasswords) {
                    const result = validateInput(password, 'password');
                    if (result.valid) {
                        return { success: false, error: `كلمة المرور ${password} ضعيفة ولكن تم قبولها` };
                    }
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // === اختبارات نظام العملة ===
        function testCurrencySystemLoaded() {
            try {
                const requiredFunctions = ['formatCurrency', 'getShippingCost'];
                const requiredObjects = ['CURRENCY_CONFIG', 'SHIPPING_RATES'];

                for (let func of requiredFunctions) {
                    if (typeof window[func] !== 'function') {
                        return { success: false, error: `الدالة ${func} غير موجودة` };
                    }
                }

                for (let obj of requiredObjects) {
                    if (typeof window[obj] === 'undefined') {
                        return { success: false, error: `الكائن ${obj} غير موجود` };
                    }
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testIQDCurrency() {
            try {
                if (CURRENCY_CONFIG.code !== 'IQD') {
                    return { success: false, error: `العملة الحالية ${CURRENCY_CONFIG.code} وليس IQD` };
                }

                if (CURRENCY_CONFIG.symbol !== 'د.ع') {
                    return { success: false, error: `رمز العملة ${CURRENCY_CONFIG.symbol} وليس د.ع` };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testPriceFormatting() {
            try {
                const testPrice = 15000;
                const formatted = formatCurrency(testPrice);

                if (!formatted.includes('د.ع')) {
                    return { success: false, error: 'رمز العملة غير موجود في التنسيق' };
                }

                if (!formatted.includes('15,000') && !formatted.includes('15000')) {
                    return { success: false, error: 'تنسيق الرقم غير صحيح' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testBasraShipping() {
            try {
                const basraShipping = getShippingCost('البصرة');
                if (basraShipping !== 3000) {
                    return { success: false, error: `رسوم شحن البصرة ${basraShipping} وليس 3000` };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testOtherProvincesShipping() {
            try {
                const provinces = ['بغداد', 'أربيل', 'النجف', 'كركوك'];

                for (let province of provinces) {
                    const shipping = getShippingCost(province);
                    if (shipping !== 5000) {
                        return { success: false, error: `رسوم شحن ${province} هي ${shipping} وليس 5000` };
                    }
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testCurrencyConversion() {
            try {
                // التحقق من أن أسعار المنتجات بالدينار العراقي
                if (typeof window.products !== 'undefined' && window.products.length > 0) {
                    const firstProduct = window.products[0];
                    if (firstProduct.price < 1000) {
                        return { success: false, error: 'أسعار المنتجات لا تزال بالدولار' };
                    }
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // === اختبارات نظام اللغات ===
        function testLanguageSystemLoaded() {
            try {
                const requiredFunctions = ['changeLanguage', 't', 'getCurrentLanguage'];
                const requiredObjects = ['SUPPORTED_LANGUAGES'];

                for (let func of requiredFunctions) {
                    if (typeof window[func] !== 'function') {
                        return { success: false, error: `الدالة ${func} غير موجودة` };
                    }
                }

                for (let obj of requiredObjects) {
                    if (typeof window[obj] === 'undefined') {
                        return { success: false, error: `الكائن ${obj} غير موجود` };
                    }
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testArabicLanguage() {
            try {
                changeLanguage('ar');

                if (getCurrentLanguage() !== 'ar') {
                    return { success: false, error: 'فشل في تغيير اللغة إلى العربية' };
                }

                if (document.documentElement.getAttribute('dir') !== 'rtl') {
                    return { success: false, error: 'اتجاه الصفحة ليس RTL للعربية' };
                }

                const testTranslation = t('nav.home');
                if (!testTranslation || testTranslation === 'nav.home') {
                    return { success: false, error: 'الترجمة العربية لا تعمل' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testKurdishLanguage() {
            try {
                changeLanguage('ku');

                if (getCurrentLanguage() !== 'ku') {
                    return { success: false, error: 'فشل في تغيير اللغة إلى الكردية' };
                }

                if (document.documentElement.getAttribute('dir') !== 'rtl') {
                    return { success: false, error: 'اتجاه الصفحة ليس RTL للكردية' };
                }

                const testTranslation = t('nav.home');
                if (!testTranslation || testTranslation === 'nav.home') {
                    return { success: false, error: 'الترجمة الكردية لا تعمل' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testEnglishLanguage() {
            try {
                changeLanguage('en');

                if (getCurrentLanguage() !== 'en') {
                    return { success: false, error: 'فشل في تغيير اللغة إلى الإنجليزية' };
                }

                if (document.documentElement.getAttribute('dir') !== 'ltr') {
                    return { success: false, error: 'اتجاه الصفحة ليس LTR للإنجليزية' };
                }

                const testTranslation = t('nav.home');
                if (!testTranslation || testTranslation === 'nav.home') {
                    return { success: false, error: 'الترجمة الإنجليزية لا تعمل' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testDirectionSwitching() {
            try {
                // اختبار التبديل بين RTL و LTR
                changeLanguage('ar');
                const rtlDirection = document.documentElement.getAttribute('dir');

                changeLanguage('en');
                const ltrDirection = document.documentElement.getAttribute('dir');

                if (rtlDirection !== 'rtl' || ltrDirection !== 'ltr') {
                    return { success: false, error: 'تبديل الاتجاه لا يعمل بشكل صحيح' };
                }

                // العودة للعربية
                changeLanguage('ar');

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testLanguagePersistence() {
            try {
                changeLanguage('en');
                const savedLanguage = localStorage.getItem('selected_language');

                if (savedLanguage !== 'en') {
                    return { success: false, error: 'اللغة لا تُحفظ في localStorage' };
                }

                // العودة للعربية
                changeLanguage('ar');

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // === اختبارات نظام المنتجات ===
        function testProductsLoaded() {
            try {
                if (typeof window.products === 'undefined') {
                    return { success: false, error: 'قاعدة بيانات المنتجات غير محملة' };
                }

                if (!Array.isArray(window.products)) {
                    return { success: false, error: 'المنتجات ليست في شكل مصفوفة' };
                }

                if (window.products.length === 0) {
                    return { success: false, error: 'لا توجد منتجات في قاعدة البيانات' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testProductPrices() {
            try {
                if (!window.products || window.products.length === 0) {
                    return { success: false, error: 'لا توجد منتجات للاختبار' };
                }

                for (let product of window.products) {
                    if (!product.price || product.price < 1000) {
                        return { success: false, error: `سعر المنتج ${product.name} غير صحيح: ${product.price}` };
                    }
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testProductCategories() {
            try {
                if (!window.products || window.products.length === 0) {
                    return { success: false, error: 'لا توجد منتجات للاختبار' };
                }

                const categories = new Set();
                for (let product of window.products) {
                    if (!product.category) {
                        return { success: false, error: `المنتج ${product.name} بدون فئة` };
                    }
                    categories.add(product.category);
                }

                if (categories.size < 2) {
                    return { success: false, error: 'عدد الفئات قليل جداً' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testProductImages() {
            try {
                if (!window.products || window.products.length === 0) {
                    return { success: false, error: 'لا توجد منتجات للاختبار' };
                }

                for (let product of window.products) {
                    if (!product.imageUrl) {
                        return { success: false, error: `المنتج ${product.name} بدون صورة` };
                    }

                    if (!product.imageUrl.startsWith('http')) {
                        return { success: false, error: `رابط صورة المنتج ${product.name} غير صحيح` };
                    }
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testProductAvailability() {
            try {
                if (!window.products || window.products.length === 0) {
                    return { success: false, error: 'لا توجد منتجات للاختبار' };
                }

                let availableCount = 0;
                for (let product of window.products) {
                    if (product.isAvailable && product.stock > 0) {
                        availableCount++;
                    }
                }

                if (availableCount === 0) {
                    return { success: false, error: 'لا توجد منتجات متاحة' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testProductDataIntegrity() {
            try {
                if (!window.products || window.products.length === 0) {
                    return { success: false, error: 'لا توجد منتجات للاختبار' };
                }

                const requiredFields = ['id', 'name', 'price', 'category', 'imageUrl'];

                for (let product of window.products) {
                    for (let field of requiredFields) {
                        if (!product[field]) {
                            return { success: false, error: `المنتج ${product.name || 'غير معروف'} يفتقد الحقل ${field}` };
                        }
                    }
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // === اختبارات السلة والطلبات ===
        function testAddToCart() {
            try {
                // مسح السلة أولاً
                localStorage.removeItem('cart');

                if (!window.products || window.products.length === 0) {
                    return { success: false, error: 'لا توجد منتجات للاختبار' };
                }

                const testProduct = window.products[0];
                const cart = [];

                cart.push({
                    id: testProduct.id,
                    name: testProduct.name,
                    price: testProduct.price,
                    quantity: 1,
                    image: testProduct.imageUrl
                });

                localStorage.setItem('cart', JSON.stringify(cart));

                const savedCart = JSON.parse(localStorage.getItem('cart') || '[]');
                if (savedCart.length === 0) {
                    return { success: false, error: 'فشل في حفظ المنتج في السلة' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testCartTotal() {
            try {
                const cart = JSON.parse(localStorage.getItem('cart') || '[]');
                if (cart.length === 0) {
                    return { success: false, error: 'السلة فارغة' };
                }

                let total = 0;
                for (let item of cart) {
                    total += item.price * item.quantity;
                }

                if (total <= 0) {
                    return { success: false, error: 'مجموع السلة غير صحيح' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testShippingApplication() {
            try {
                const basraShipping = getShippingCost('البصرة');
                const baghdadShipping = getShippingCost('بغداد');

                if (basraShipping !== 3000 || baghdadShipping !== 5000) {
                    return { success: false, error: 'رسوم الشحن غير صحيحة' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testOrderSaving() {
            try {
                const testOrder = {
                    id: 'test-' + Date.now(),
                    customerData: {
                        fullName: 'عميل تجريبي',
                        email: '<EMAIL>',
                        phone: '07123456789',
                        province: 'بغداد'
                    },
                    items: [{
                        id: 1,
                        name: 'منتج تجريبي',
                        price: 15000,
                        quantity: 1
                    }],
                    total: 20000,
                    shipping: 5000,
                    status: 'pending',
                    createdAt: new Date().toISOString()
                };

                const orders = JSON.parse(localStorage.getItem('customer_orders') || '[]');
                orders.push(testOrder);
                localStorage.setItem('customer_orders', JSON.stringify(orders));

                const savedOrders = JSON.parse(localStorage.getItem('customer_orders') || '[]');
                const savedOrder = savedOrders.find(o => o.id === testOrder.id);

                if (!savedOrder) {
                    return { success: false, error: 'فشل في حفظ الطلب' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testQuantityUpdate() {
            try {
                const cart = JSON.parse(localStorage.getItem('cart') || '[]');
                if (cart.length === 0) {
                    return { success: false, error: 'السلة فارغة' };
                }

                // تحديث الكمية
                cart[0].quantity = 3;
                localStorage.setItem('cart', JSON.stringify(cart));

                const updatedCart = JSON.parse(localStorage.getItem('cart') || '[]');
                if (updatedCart[0].quantity !== 3) {
                    return { success: false, error: 'فشل في تحديث الكمية' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testRemoveFromCart() {
            try {
                let cart = JSON.parse(localStorage.getItem('cart') || '[]');
                if (cart.length === 0) {
                    return { success: false, error: 'السلة فارغة' };
                }

                // إزالة المنتج الأول
                cart = cart.slice(1);
                localStorage.setItem('cart', JSON.stringify(cart));

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // === اختبارات الواجهة والمظهر ===
        function testResponsiveDesign() {
            try {
                // فحص وجود Bootstrap
                if (typeof window.bootstrap === 'undefined') {
                    return { success: false, error: 'Bootstrap غير محمل' };
                }

                // فحص وجود viewport meta tag
                const viewport = document.querySelector('meta[name="viewport"]');
                if (!viewport) {
                    return { success: false, error: 'viewport meta tag غير موجود' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testUserOptionsDisplay() {
            try {
                // محاكاة تسجيل دخول
                const testToken = 'test.token.here';
                localStorage.setItem('customer_token', testToken);

                // فحص وجود عناصر واجهة المستخدم
                const authButtons = document.getElementById('authButtons');
                const userDropdown = document.getElementById('userDropdown');

                if (!authButtons && !userDropdown) {
                    return { success: false, error: 'عناصر واجهة المستخدم غير موجودة' };
                }

                // تنظيف
                localStorage.removeItem('customer_token');

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testFontsAndIcons() {
            try {
                // فحص تحميل الخطوط
                const computedStyle = window.getComputedStyle(document.body);
                const fontFamily = computedStyle.fontFamily;

                if (!fontFamily.includes('Cairo')) {
                    return { success: false, error: 'خط Cairo غير محمل' };
                }

                // فحص وجود أيقونات Bootstrap
                const iconElements = document.querySelectorAll('.bi, .fas, .fa');
                if (iconElements.length === 0) {
                    return { success: false, error: 'لا توجد أيقونات في الصفحة' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testLinksAndButtons() {
            try {
                const buttons = document.querySelectorAll('button, .btn');
                const links = document.querySelectorAll('a[href]');

                if (buttons.length === 0) {
                    return { success: false, error: 'لا توجد أزرار في الصفحة' };
                }

                if (links.length === 0) {
                    return { success: false, error: 'لا توجد روابط في الصفحة' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testMessagesDisplay() {
            try {
                // اختبار إنشاء رسالة تنبيه
                const testMessage = document.createElement('div');
                testMessage.className = 'alert alert-success';
                testMessage.textContent = 'رسالة اختبار';
                document.body.appendChild(testMessage);

                // التحقق من وجودها
                const messageExists = document.body.contains(testMessage);

                // إزالة الرسالة
                document.body.removeChild(testMessage);

                if (!messageExists) {
                    return { success: false, error: 'فشل في عرض الرسائل' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testAnimations() {
            try {
                // فحص دعم CSS animations
                const testElement = document.createElement('div');
                testElement.style.animation = 'test 1s ease';

                if (testElement.style.animation === '') {
                    return { success: false, error: 'CSS animations غير مدعومة' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // === اختبارات الأمان والبنية ===
        function testFileStructure() {
            try {
                const requiredScripts = [
                    'scripts/customer-auth.js',
                    'scripts/currency.js',
                    'scripts/language.js',
                    'database/products.js'
                ];

                // فحص تحميل السكربتات
                const scripts = Array.from(document.querySelectorAll('script[src]'));
                const loadedScripts = scripts.map(s => s.src);

                for (let script of requiredScripts) {
                    const isLoaded = loadedScripts.some(loaded => loaded.includes(script));
                    if (!isLoaded) {
                        return { success: false, error: `الملف ${script} غير محمل` };
                    }
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testScriptConnections() {
            try {
                // فحص الاتصال بين السكربتات
                const connections = [
                    { name: 'customer-auth', check: () => typeof registerCustomer === 'function' },
                    { name: 'currency', check: () => typeof formatCurrency === 'function' },
                    { name: 'language', check: () => typeof changeLanguage === 'function' },
                    { name: 'products', check: () => typeof window.products !== 'undefined' }
                ];

                for (let connection of connections) {
                    if (!connection.check()) {
                        return { success: false, error: `اتصال ${connection.name} فاشل` };
                    }
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testConsoleErrors() {
            try {
                // فحص الأخطاء في الكونسول (محدود)
                const originalError = console.error;
                let errorCount = 0;

                console.error = function(...args) {
                    errorCount++;
                    originalError.apply(console, args);
                };

                // استعادة console.error
                setTimeout(() => {
                    console.error = originalError;
                }, 100);

                if (errorCount > 0) {
                    return { success: false, error: `${errorCount} أخطاء في الكونسول` };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testDataProtection() {
            try {
                // فحص حماية البيانات الحساسة
                const customers = JSON.parse(localStorage.getItem('customers') || '[]');

                for (let customer of customers) {
                    // التحقق من عدم حفظ كلمات المرور بوضوح
                    if (customer.password && customer.password.length < 8) {
                        return { success: false, error: 'كلمات المرور غير محمية بشكل كافٍ' };
                    }
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testSessionValidation() {
            try {
                // اختبار التحقق من الجلسات
                const testToken = 'invalid.token.test';
                localStorage.setItem('customer_token', testToken);

                // محاولة التحقق من التوكن
                const currentCustomer = getCurrentCustomer();

                // تنظيف
                localStorage.removeItem('customer_token');

                // يجب أن يفشل التحقق من التوكن غير الصحيح
                if (currentCustomer) {
                    return { success: false, error: 'التحقق من الجلسة غير آمن' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function testUnauthorizedAccess() {
            try {
                // فحص منع الوصول غير المصرح
                localStorage.removeItem('customer_token');
                localStorage.removeItem('customer_data');

                // محاولة الوصول لبيانات محمية
                const currentCustomer = getCurrentCustomer();

                if (currentCustomer) {
                    return { success: false, error: 'يمكن الوصول للبيانات بدون تسجيل دخول' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // === الدوال الرئيسية لتشغيل الاختبارات ===

        async function runFullSystemTest() {
            systemTester.updateSystemStatus('testing');
            document.getElementById('loadingOverlay').style.display = 'flex';

            // إعادة تعيين النتائج
            systemTester.testResults = { total: 0, passed: 0, failed: 0, warnings: 0 };

            for (let section of systemTester.testSections) {
                await runSectionTests(section);
                systemTester.updateSectionStatus(section.id);

                // تأخير قصير لتحسين تجربة المستخدم
                await new Promise(resolve => setTimeout(resolve, 200));
            }

            updateSummaryPanel();
            document.getElementById('loadingOverlay').style.display = 'none';
            systemTester.updateSystemStatus('online');
            updateLastTestTime();
        }

        async function runSectionTests(section) {
            for (let test of section.tests) {
                try {
                    const testFunction = window[test.method];
                    if (typeof testFunction === 'function') {
                        const result = testFunction();

                        if (result.success) {
                            systemTester.updateTestResult(section.id, test.method, 'success');
                        } else {
                            systemTester.updateTestResult(section.id, test.method, 'error', result.error);
                        }
                    } else {
                        systemTester.updateTestResult(section.id, test.method, 'error', 'دالة الاختبار غير موجودة');
                    }
                } catch (error) {
                    systemTester.updateTestResult(section.id, test.method, 'error', error.message);
                }

                // تأخير قصير بين الاختبارات
                await new Promise(resolve => setTimeout(resolve, 50));
            }
        }

        async function runQuickTest() {
            systemTester.updateSystemStatus('testing');

            // اختبارات سريعة أساسية
            const quickTests = [
                { section: 'auth', method: 'testAuthFilesLoaded' },
                { section: 'currency', method: 'testCurrencySystemLoaded' },
                { section: 'language', method: 'testLanguageSystemLoaded' },
                { section: 'products', method: 'testProductsLoaded' }
            ];

            systemTester.testResults = { total: 0, passed: 0, failed: 0, warnings: 0 };

            for (let test of quickTests) {
                try {
                    const testFunction = window[test.method];
                    if (typeof testFunction === 'function') {
                        const result = testFunction();

                        if (result.success) {
                            systemTester.updateTestResult(test.section, test.method, 'success');
                        } else {
                            systemTester.updateTestResult(test.section, test.method, 'error', result.error);
                        }
                    }
                } catch (error) {
                    systemTester.updateTestResult(test.section, test.method, 'error', error.message);
                }

                await new Promise(resolve => setTimeout(resolve, 100));
            }

            updateSummaryPanel();
            systemTester.updateSystemStatus('online');
            updateLastTestTime();
        }

        function updateSummaryPanel() {
            const summaryPanel = document.getElementById('summaryPanel');
            summaryPanel.style.display = 'block';

            const { total, passed, failed, warnings } = systemTester.testResults;
            const successRate = total > 0 ? Math.round((passed / total) * 100) : 0;

            document.getElementById('totalTests').textContent = total;
            document.getElementById('passedTests').textContent = passed;
            document.getElementById('failedTests').textContent = failed;
            document.getElementById('successRate').textContent = successRate + '%';

            // تحديث دائرة التقدم
            const progressCircle = document.getElementById('progressCircle');
            const circumference = 2 * Math.PI * 54;
            const offset = circumference - (successRate / 100) * circumference;
            progressCircle.style.strokeDasharray = `${circumference} ${circumference}`;
            progressCircle.style.strokeDashoffset = offset;

            // تحديث الحالة العامة
            const overallStatus = document.getElementById('overallStatus');
            const statusMessage = document.getElementById('statusMessage');

            if (failed === 0) {
                overallStatus.textContent = '🎉 النظام يعمل بشكل مثالي!';
                statusMessage.textContent = 'جميع الاختبارات نجحت بنجاح';
                overallStatus.style.color = 'var(--success-color)';
            } else if (failed < total / 2) {
                overallStatus.textContent = '⚠️ النظام يعمل مع بعض المشاكل';
                statusMessage.textContent = `${failed} اختبار فشل من أصل ${total}`;
                overallStatus.style.color = 'var(--warning-color)';
            } else {
                overallStatus.textContent = '❌ النظام يحتاج إصلاحات';
                statusMessage.textContent = `${failed} اختبار فشل - يرجى المراجعة`;
                overallStatus.style.color = 'var(--danger-color)';
            }
        }

        function resetSystemData() {
            if (confirm('هل أنت متأكد من إعادة تهيئة جميع بيانات النظام؟\nسيتم حذف جميع الحسابات والطلبات والإعدادات.')) {
                // حذف البيانات الأساسية
                const keysToRemove = [
                    'customers',
                    'customer_orders',
                    'contact_messages',
                    'cart',
                    'customer_token',
                    'customer_data',
                    'selected_language',
                    'user_settings'
                ];

                keysToRemove.forEach(key => {
                    localStorage.removeItem(key);
                });

                // إعادة تحميل البيانات الافتراضية
                if (typeof runFinalSetup === 'function') {
                    runFinalSetup();
                }

                alert('تم إعادة تهيئة النظام بنجاح!');
                location.reload();
            }
        }

        function clearAllCache() {
            if (confirm('هل أنت متأكد من مسح جميع بيانات الكاش؟')) {
                // مسح الكاش
                if ('caches' in window) {
                    caches.keys().then(names => {
                        names.forEach(name => {
                            caches.delete(name);
                        });
                    });
                }

                // مسح session storage
                sessionStorage.clear();

                alert('تم مسح الكاش بنجاح!');
                location.reload();
            }
        }

        function updateLastTestTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-IQ', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });

            document.getElementById('lastTestTime').textContent = `آخر فحص: ${timeString}`;
        }
    </script>
</body>
</html>
    </script>
</body>
</html>
