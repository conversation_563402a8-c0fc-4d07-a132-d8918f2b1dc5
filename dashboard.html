<!DOCTYPE html>
<html lang="ar" dir="rtl" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - متجر VelaSweets</title>
    
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap');
        
        :root {
            --primary-color: #5a287d;
            --secondary-color: #f8a5c2;
            --tertiary-color: #b07d28;
            --text-color: #333;
            --text-light: #fff;
            --bg-color: #f8f9fa;
            --card-bg: #fff;
            --sidebar-bg: #fff;
            --sidebar-hover: #f1f1f1;
            --sidebar-active: #5a287d;
            --sidebar-active-text: #fff;
            --border-color: #dee2e6;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --transition-speed: 0.3s;
        }
        
        [data-theme="dark"] {
            --primary-color: #7e3ca3;
            --secondary-color: #ff9aba;
            --tertiary-color: #d9a440;
            --text-color: #e0e0e0;
            --text-light: #fff;
            --bg-color: #121212;
            --card-bg: #1e1e1e;
            --sidebar-bg: #1e1e1e;
            --sidebar-hover: #2d2d2d;
            --sidebar-active: #7e3ca3;
            --sidebar-active-text: #fff;
            --border-color: #2d2d2d;
            --shadow-color: rgba(0, 0, 0, 0.5);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            direction: rtl;
            min-height: 100vh;
        }
        
        .wrapper {
            display: flex;
            width: 100%;
            align-items: stretch;
        }
        
        #sidebar {
            min-width: 250px;
            max-width: 250px;
            background: var(--sidebar-bg);
            transition: all 0.3s;
            height: 100vh;
            position: fixed;
            right: 0;
            top: 0;
            z-index: 999;
            box-shadow: -2px 0 10px var(--shadow-color);
            overflow-y: auto;
        }
        
        #sidebar.active {
            margin-right: -250px;
        }
        
        #sidebar .sidebar-header {
            padding: 20px;
            background: var(--primary-color);
            text-align: center;
            color: white;
        }
        
        #sidebar .sidebar-header h3 {
            font-family: 'Playfair Display', serif;
            margin: 0;
            font-size: 1.8rem;
        }
        
        #sidebar ul.components {
            padding: 20px 0;
            border-bottom: 1px solid var(--border-color);
        }
        
        #sidebar ul p {
            padding: 10px;
            font-size: 1.1em;
            display: block;
            color: var(--text-color);
        }
        
        #sidebar ul li a {
            padding: 15px 20px;
            display: block;
            color: var(--text-color);
            text-decoration: none;
            transition: all var(--transition-speed);
            border-right: 3px solid transparent;
            display: flex;
            align-items: center;
        }
        
        #sidebar ul li a:hover {
            background: var(--sidebar-hover);
            border-right-color: var(--secondary-color);
        }
        
        #sidebar ul li.active > a {
            background: var(--sidebar-active);
            color: var(--sidebar-active-text);
            border-right-color: var(--tertiary-color);
        }
        
        #sidebar ul li a i {
            margin-left: 10px;
            width: 20px;
            text-align: center;
        }
        
        #sidebar .sidebar-footer {
            padding: 20px;
            border-top: 1px solid var(--border-color);
        }
        
        #sidebar .user-info {
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
        }
        
        #sidebar .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 10px;
        }
        
        #sidebar .user-details {
            flex: 1;
        }
        
        #sidebar .user-name {
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 2px;
        }
        
        #sidebar .user-role {
            font-size: 0.8rem;
            opacity: 0.8;
        }
        
        #content {
            width: 100%;
            padding: 20px;
            min-height: 100vh;
            transition: all 0.3s;
            margin-right: 250px;
        }
        
        #content.active {
            margin-right: 0;
        }
        
        .navbar {
            background-color: var(--card-bg);
            box-shadow: 0 2px 10px var(--shadow-color);
            padding: 15px 20px;
            margin-bottom: 30px;
            border-radius: 10px;
        }
        
        .navbar .navbar-btn {
            background: transparent;
            border: none;
            cursor: pointer;
            padding: 10px;
        }
        
        .navbar .navbar-btn:focus {
            outline: none;
        }
        
        .search-form {
            position: relative;
            margin-right: 20px;
            margin-left: auto;
        }
        
        .search-form input {
            padding: 10px 15px;
            padding-right: 40px;
            border-radius: 20px;
            border: 1px solid var(--border-color);
            background: var(--bg-color);
            width: 100%;
            transition: all var(--transition-speed);
        }
        
        .search-form input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(90, 40, 125, 0.25);
        }
        
        .search-form button {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: transparent;
            border: none;
            color: var(--text-color);
            cursor: pointer;
        }
        
        .notification-bell {
            position: relative;
            margin-right: 20px;
        }
        
        .notification-bell .badge {
            position: absolute;
            top: -5px;
            left: -5px;
            background: var(--danger-color);
            color: white;
            border-radius: 50%;
            padding: 3px 6px;
            font-size: 10px;
        }
        
        .stats-card {
            background-color: var(--card-bg);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px var(--shadow-color);
            transition: all var(--transition-speed);
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px var(--shadow-color);
        }
        
        .stats-card .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            font-size: 24px;
            color: white;
        }
        
        .stats-card .stats-title {
            font-size: 0.9rem;
            margin-bottom: 10px;
            color: var(--text-color);
            opacity: 0.8;
        }
        
        .stats-card .stats-value {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .stats-card .stats-desc {
            font-size: 0.8rem;
            color: var(--text-color);
            opacity: 0.7;
        }
        
        .stats-card .trend-up {
            color: var(--success-color);
        }
        
        .stats-card .trend-down {
            color: var(--danger-color);
        }
        
        .bg-primary-custom {
            background-color: var(--primary-color);
        }
        
        .bg-secondary-custom {
            background-color: var(--secondary-color);
        }
        
        .bg-tertiary-custom {
            background-color: var(--tertiary-color);
        }
        
        .bg-info-custom {
            background-color: var(--info-color);
        }
        
        .chart-container {
            background-color: var(--card-bg);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px var(--shadow-color);
        }
        
        .chart-container h4 {
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
            font-size: 1.2rem;
        }
        
        .recent-orders {
            background-color: var(--card-bg);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px var(--shadow-color);
        }
        
        .recent-orders h4 {
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
            font-size: 1.2rem;
        }
        
        .recent-orders .table th {
            border-top: none;
            font-weight: 600;
        }
        
        .recent-orders .table td, .recent-orders .table th {
            padding: 12px 15px;
            vertical-align: middle;
        }
        
        .badge-status {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .badge-completed {
            background-color: rgba(40, 167, 69, 0.1);
            color: var(--success-color);
        }
        
        .badge-pending {
            background-color: rgba(255, 193, 7, 0.1);
            color: var(--warning-color);
        }
        
        .badge-cancelled {
            background-color: rgba(220, 53, 69, 0.1);
            color: var(--danger-color);
        }
        
        .theme-switch {
            display: flex;
            align-items: center;
            margin-top: 5px;
        }
        
        .theme-switch input {
            display: none;
        }
        
        .theme-switch label {
            cursor: pointer;
            padding: 0;
            width: 50px;
            height: 25px;
            background: var(--border-color);
            display: block;
            border-radius: 100px;
            position: relative;
            margin: 0 10px;
        }
        
        .theme-switch label:after {
            content: '';
            position: absolute;
            top: 2px;
            right: 2px;
            width: 21px;
            height: 21px;
            background: #fff;
            border-radius: 90px;
            transition: 0.3s;
        }
        
        .theme-switch input:checked + label {
            background: var(--primary-color);
        }
        
        .theme-switch input:checked + label:after {
            right: calc(100% - 2px - 21px);
        }
        
        @media (max-width: 768px) {
            #sidebar {
                margin-right: -250px;
            }
            #sidebar.active {
                margin-right: 0;
            }
            #content {
                margin-right: 0;
            }
            #content.active {
                margin-right: 250px;
            }
            .search-form {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <!-- Sidebar -->
        <nav id="sidebar">
            <div class="sidebar-header">
                <h3>VelaSweets</h3>
            </div>
            
            <div class="user-info">
                <div class="user-avatar" id="userAvatar"></div>
                <div class="user-details">
                    <div class="user-name" id="userName">-</div>
                    <div class="user-role" id="userRole">-</div>
                </div>
            </div>
            
            <ul class="list-unstyled components">
                <li class="active">
                    <a href="dashboard.html">
                        <i class="bi bi-speedometer2"></i>
                        لوحة التحكم
                    </a>
                </li>
                <li>
                    <a href="orders.html">
                        <i class="bi bi-cart3"></i>
                        الطلبات
                    </a>
                </li>
                <li>
                    <a href="products.html">
                        <i class="bi bi-box-seam"></i>
                        المنتجات
                    </a>
                </li>
                <li>
                    <a href="categories.html">
                        <i class="bi bi-tags"></i>
                        الأصناف
                    </a>
                </li>
                <li>
                    <a href="customers.html">
                        <i class="bi bi-people"></i>
                        الزبائن
                    </a>
                </li>
                <li>
                    <a href="reports.html">
                        <i class="bi bi-bar-chart"></i>
                        التقارير
                    </a>
                </li>
                <li>
                    <a href="promotions.html">
                        <i class="bi bi-gift"></i>
                        العروض والخصومات
                    </a>
                </li>
                <li>
                    <a href="settings.html">
                        <i class="bi bi-gear"></i>
                        الإعدادات
                    </a>
                </li>
                <li>
                    <a href="users.html">
                        <i class="bi bi-person-badge"></i>
                        المستخدمون
                    </a>
                </li>
            </ul>
            
            <div class="sidebar-footer">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>الوضع المظلم</span>
                    <div class="theme-switch">
                        <input type="checkbox" id="themeSwitch">
                        <label for="themeSwitch"></label>
                    </div>
                </div>
                <button class="btn btn-danger w-100" id="logoutBtn">
                    <i class="bi bi-box-arrow-left me-2"></i>تسجيل الخروج
                </button>
            </div>
        </nav>
        
        <!-- Page Content -->
        <div id="content">
            <nav class="navbar navbar-expand">
                <div class="container-fluid">
                    <button type="button" id="sidebarCollapse" class="navbar-btn">
                        <i class="bi bi-list"></i>
                    </button>
                    
                    <div class="ms-auto d-flex align-items-center">
                        <form class="search-form">
                            <input type="text" placeholder="بحث...">
                            <button type="submit"><i class="bi bi-search"></i></button>
                        </form>
                        
                        <div class="notification-bell">
                            <i class="bi bi-bell"></i>
                            <span class="badge">3</span>
                        </div>
                        
                        <div class="dropdown">
                            <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-gear"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton">
                                <li><a class="dropdown-item" href="profile.html">الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="settings.html">الإعدادات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" id="dropdownLogoutBtn">تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </nav>
            
            <div class="container-fluid">
                <div class="row mb-4">
                    <div class="col-md-8">
                        <h2>مرحباً بك، <span id="welcomeUserName">-</span></h2>
                        <p class="text-muted">إليك ملخص نشاط متجرك اليوم</p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="current-date">
                            <p class="mb-0"><i class="bi bi-calendar3 me-2"></i><span id="currentDate">-</span></p>
                        </div>
                    </div>
                </div>
                
                <!-- Stats Cards -->
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="stats-card">
                            <div class="stats-icon bg-primary-custom">
                                <i class="bi bi-currency-dollar"></i>
                            </div>
                            <div class="stats-title">إجمالي المبيعات</div>
                            <div class="stats-value">$12,650</div>
                            <div class="stats-desc">
                                <span class="trend-up"><i class="bi bi-arrow-up-short"></i> 12%</span> مقارنة بالأمس
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="stats-card">
                            <div class="stats-icon bg-secondary-custom">
                                <i class="bi bi-cart-check"></i>
                            </div>
                            <div class="stats-title">الطلبات الجديدة</div>
                            <div class="stats-value">45</div>
                            <div class="stats-desc">
                                <span class="trend-up"><i class="bi bi-arrow-up-short"></i> 8%</span> مقارنة بالأمس
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="stats-card">
                            <div class="stats-icon bg-tertiary-custom">
                                <i class="bi bi-person-plus"></i>
                            </div>
                            <div class="stats-title">زبائن جدد</div>
                            <div class="stats-value">18</div>
                            <div class="stats-desc">
                                <span class="trend-down"><i class="bi bi-arrow-down-short"></i> 3%</span> مقارنة بالأمس
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="stats-card">
                            <div class="stats-icon bg-info-custom">
                                <i class="bi bi-eye"></i>
                            </div>
                            <div class="stats-title">زوار الموقع</div>
                            <div class="stats-value">245</div>
                            <div class="stats-desc">
                                <span class="trend-up"><i class="bi bi-arrow-up-short"></i> 15%</span> مقارنة بالأمس
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Charts -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="chart-container">
                            <h4><i class="bi bi-bar-chart-fill me-2"></i>تحليل المبيعات</h4>
                            <canvas id="salesChart" height="250"></canvas>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="chart-container">
                            <h4><i class="bi bi-pie-chart-fill me-2"></i>المنتجات الأكثر مبيعاً</h4>
                            <canvas id="productsChart" height="250"></canvas>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Orders -->
                <div class="row">
                    <div class="col-12">
                        <div class="recent-orders">
                            <h4><i class="bi bi-list-check me-2"></i>أحدث الطلبات</h4>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>رقم الطلب</th>
                                            <th>اسم الزبون</th>
                                            <th>التاريخ</th>
                                            <th>المبلغ</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>#ORD-001</td>
                                            <td>أحمد محمد</td>
                                            <td>10-11-2023</td>
                                            <td>$150.00</td>
                                            <td><span class="badge-status badge-completed">مكتمل</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>#ORD-002</td>
                                            <td>فاطمة علي</td>
                                            <td>10-11-2023</td>
                                            <td>$85.50</td>
                                            <td><span class="badge-status badge-pending">قيد التنفيذ</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>#ORD-003</td>
                                            <td>محمد خالد</td>
                                            <td>9-11-2023</td>
                                            <td>$220.00</td>
                                            <td><span class="badge-status badge-completed">مكتمل</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>#ORD-004</td>
                                            <td>سارة أحمد</td>
                                            <td>9-11-2023</td>
                                            <td>$65.25</td>
                                            <td><span class="badge-status badge-cancelled">ملغي</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>#ORD-005</td>
                                            <td>عمر حسن</td>
                                            <td>8-11-2023</td>
                                            <td>$175.00</td>
                                            <td><span class="badge-status badge-pending">قيد التنفيذ</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center mt-3">
                                <a href="orders.html" class="btn btn-outline-primary">عرض جميع الطلبات</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
    <script src="database/users.js"></script>
    <script src="scripts/auth.js"></script>
    <script>
        // تعريف دالة للحصول على الكوكيز (متاحة للاستخدام في أي مكان في الكود)
        function getCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
            return null;
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            // حماية الصفحة - التحقق من تسجيل الدخول
            if (typeof protectPage === 'function') {
                try {
                    protectPage('Administrationregistration.html', ['all']);
                } catch (e) {
                    // تحويل المستخدم إلى صفحة تسجيل الدخول
                    window.location.href = 'Administrationregistration.html';
                }
            } else {
                // التحقق المباشر من حالة تسجيل الدخول
                let isLoggedIn = false;
                
                try {
                    const token = localStorage.getItem('auth_token');
                    const userData = localStorage.getItem('user_data');
                    if (token && userData) {
                        isLoggedIn = true;
                    }
                } catch (localStorageError) {
                    console.log('غير قادر على استخدام localStorage، سيتم التحقق من الكوكيز');
                }
                
                // التحقق من الكوكيز إذا فشل localStorage
                if (!isLoggedIn) {
                    const userLoggedIn = getCookie('user_logged_in');
                    const adminUser = getCookie('admin_user');
                    
                    if (userLoggedIn === 'true' || adminUser) {
                        isLoggedIn = true;
                    }
                }
                
                // إذا لم يتم العثور على بيانات تسجيل الدخول، انتقل إلى صفحة تسجيل الدخول
                if (!isLoggedIn) {
                    window.location.href = 'Administrationregistration.html';
                    return;
                }
            }
            
            // تحميل بيانات المستخدم
            let userData = {};
            
            // محاولة قراءة البيانات من localStorage
            try {
                const userDataStr = localStorage.getItem('user_data');
                if (userDataStr) {
                    userData = JSON.parse(userDataStr);
                }
            } catch (e) {
                console.log('لا يمكن قراءة بيانات المستخدم من localStorage');
            }
            
            // إذا لم تكن هناك بيانات في localStorage، حاول من الكوكيز
            if (Object.keys(userData).length === 0) {
                const adminUser = getCookie('admin_user');
                const adminRole = getCookie('admin_role');
                
                if (adminUser) {
                    userData = {
                        name: adminUser.split('@')[0] || 'مستخدم',
                        email: adminUser,
                        role: adminRole || 'employee',
                        permissions: ["all"]
                    };
                }
            }
            
            // عرض معلومات المستخدم في الشريط الجانبي
            document.getElementById('userName').textContent = userData.name || 'مستخدم';
            document.getElementById('welcomeUserName').textContent = userData.name || 'مستخدم';
            document.getElementById('userRole').textContent = userData.role === 'admin' ? 'مدير النظام' : 'موظف';
            
            // تعيين الحرف الأول من اسم المستخدم في الصورة الرمزية
            const userAvatar = document.getElementById('userAvatar');
            if (userData.name) {
                userAvatar.textContent = userData.name.charAt(0).toUpperCase();
            } else {
                userAvatar.textContent = 'U';
            }
            
            // عرض التاريخ الحالي
            const now = new Date();
            const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
            document.getElementById('currentDate').textContent = now.toLocaleDateString('ar-EG', options);
            
            // تبديل الشريط الجانبي
            document.getElementById('sidebarCollapse').addEventListener('click', function() {
                document.getElementById('sidebar').classList.toggle('active');
                document.getElementById('content').classList.toggle('active');
            });
            
            // تبديل الوضع المظلم
            const themeSwitch = document.getElementById('themeSwitch');
            
            // تحقق من الوضع المحفوظ
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'dark') {
                document.documentElement.setAttribute('data-theme', 'dark');
                themeSwitch.checked = true;
            }
            
            themeSwitch.addEventListener('change', function() {
                if (this.checked) {
                    document.documentElement.setAttribute('data-theme', 'dark');
                    localStorage.setItem('theme', 'dark');
                } else {
                    document.documentElement.setAttribute('data-theme', 'light');
                    localStorage.setItem('theme', 'light');
                }
            });
            
            // تسجيل الخروج
            document.getElementById('logoutBtn').addEventListener('click', handleLogout);
            document.getElementById('dropdownLogoutBtn').addEventListener('click', handleLogout);
            
            function handleLogout() {
                // استخدام دالة تسجيل الخروج من ملف auth.js إذا كانت متاحة
                if (typeof logout === 'function') {
                    logout(false);
                    return;
                }
                
                // حذف بيانات الجلسة من كل مصادر التخزين الممكنة
                try {
                    // حذف بيانات localStorage
                    localStorage.removeItem('auth_token');
                    localStorage.removeItem('user_data');
                } catch (e) {
                    console.log('لا يمكن الوصول إلى localStorage');
                }
                
                // حذف الكوكيز
                const expiry = 'Thu, 01 Jan 1970 00:00:01 GMT';
                document.cookie = 'user_logged_in=; path=/; expires=' + expiry;
                document.cookie = 'admin_user=; path=/; expires=' + expiry;
                document.cookie = 'admin_role=; path=/; expires=' + expiry;
                document.cookie = 'auth_active=; path=/; expires=' + expiry;
                
                // إضافة تأخير قصير قبل إعادة التوجيه
                setTimeout(function() {
                    window.location.href = 'Administrationregistration.html';
                }, 500);
            }
            
            // إنشاء الرسوم البيانية
            createSalesChart();
            createProductsChart();
        });
        
        // رسم بياني للمبيعات
        function createSalesChart() {
            const ctx = document.getElementById('salesChart').getContext('2d');
            const salesChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'],
                    datasets: [{
                        label: 'المبيعات',
                        data: [3500, 4200, 3800, 5100, 4700, 6200, 5400],
                        backgroundColor: 'rgba(90, 40, 125, 0.2)',
                        borderColor: '#5a287d',
                        borderWidth: 2,
                        tension: 0.3,
                        pointBackgroundColor: '#5a287d',
                        pointRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                font: {
                                    family: 'Cairo'
                                }
                            }
                        },
                        tooltip: {
                            titleFont: {
                                family: 'Cairo'
                            },
                            bodyFont: {
                                family: 'Cairo'
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                drawBorder: false
                            },
                            ticks: {
                                font: {
                                    family: 'Cairo'
                                }
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                font: {
                                    family: 'Cairo'
                                }
                            }
                        }
                    }
                }
            });
        }
        
        // رسم بياني للمنتجات
        function createProductsChart() {
            const ctx = document.getElementById('productsChart').getContext('2d');
            const productsChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['كيك الشوكولاتة', 'البقلاوة', 'كنافة', 'بسبوسة', 'كوكيز'],
                    datasets: [{
                        data: [35, 25, 20, 15, 5],
                        backgroundColor: [
                            '#5a287d',
                            '#f8a5c2',
                            '#b07d28',
                            '#17a2b8',
                            '#6c757d'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                font: {
                                    family: 'Cairo'
                                }
                            }
                        },
                        tooltip: {
                            titleFont: {
                                family: 'Cairo'
                            },
                            bodyFont: {
                                family: 'Cairo'
                            }
                        }
                    }
                }
            });
        }
    </script>
</body>
</html>
