/**
 * نظام المصادقة المحسن للعملاء - VelaSweets
 * يحتوي على جميع الوظائف المتعلقة بتسجيل الدخول وإنشاء الحسابات للعملاء
 */

// متغيرات عامة
let currentCustomer = null;
let customerToken = null;

// قواعد التحقق من صحة البيانات
const VALIDATION_RULES = {
    phone: {
        pattern: /^07\d{9}$/,
        message: 'رقم الهاتف يجب أن يبدأ بـ 07 ويتكون من 11 رقماً'
    },
    email: {
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        message: 'يرجى إدخال بريد إلكتروني صحيح'
    },
    password: {
        minLength: 6,
        message: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'
    },
    name: {
        minLength: 2,
        pattern: /^[\u0600-\u06FFa-zA-Z\s]+$/,
        message: 'الاسم يجب أن يحتوي على أحرف عربية أو إنجليزية فقط'
    }
};

/**
 * التحقق من صحة البيانات
 */
function validateInput(value, type) {
    if (!value || value.trim() === '') {
        return { valid: false, message: 'هذا الحقل مطلوب' };
    }

    const rule = VALIDATION_RULES[type];
    if (!rule) return { valid: true };

    const trimmedValue = value.trim();

    // التحقق من الطول الأدنى
    if (rule.minLength && trimmedValue.length < rule.minLength) {
        return { valid: false, message: rule.message };
    }

    // التحقق من النمط
    if (rule.pattern && !rule.pattern.test(trimmedValue)) {
        return { valid: false, message: rule.message };
    }

    return { valid: true };
}

/**
 * التحقق من عدم وجود مستخدم مكرر
 */
function checkDuplicateCustomer(email, phone, excludeId = null) {
    const customers = JSON.parse(localStorage.getItem('customers') || '[]');
    
    const duplicate = customers.find(customer => 
        customer.id !== excludeId && (
            customer.email.toLowerCase() === email.toLowerCase() ||
            customer.phone === phone
        )
    );

    if (duplicate) {
        if (duplicate.email.toLowerCase() === email.toLowerCase()) {
            return { isDuplicate: true, message: 'هذا البريد الإلكتروني مسجل مسبقاً' };
        }
        if (duplicate.phone === phone) {
            return { isDuplicate: true, message: 'رقم الهاتف هذا مسجل مسبقاً' };
        }
    }

    return { isDuplicate: false };
}

/**
 * تسجيل عميل جديد
 */
function registerCustomer(customerData) {
    try {
        // التحقق من صحة البيانات
        const validations = [
            { field: 'fullName', type: 'name', value: customerData.fullName },
            { field: 'email', type: 'email', value: customerData.email },
            { field: 'phone', type: 'phone', value: customerData.phone },
            { field: 'password', type: 'password', value: customerData.password }
        ];

        for (const validation of validations) {
            const result = validateInput(validation.value, validation.type);
            if (!result.valid) {
                return { success: false, message: result.message };
            }
        }

        // التحقق من عدم وجود مستخدم مكرر
        const duplicateCheck = checkDuplicateCustomer(customerData.email, customerData.phone);
        if (duplicateCheck.isDuplicate) {
            return { success: false, message: duplicateCheck.message };
        }

        // إنشاء العميل الجديد
        const newCustomer = {
            id: Date.now(),
            fullName: customerData.fullName.trim(),
            email: customerData.email.trim().toLowerCase(),
            phone: customerData.phone.trim(),
            province: customerData.province || '',
            address: customerData.address || '',
            password: customerData.password, // في الإنتاج يجب تشفير كلمة المرور
            createdAt: new Date().toISOString(),
            isActive: true,
            role: 'customer'
        };

        // حفظ العميل
        const customers = JSON.parse(localStorage.getItem('customers') || '[]');
        customers.push(newCustomer);
        localStorage.setItem('customers', JSON.stringify(customers));

        return { 
            success: true, 
            customer: newCustomer,
            message: 'تم إنشاء الحساب بنجاح'
        };

    } catch (error) {
        console.error('خطأ في تسجيل العميل:', error);
        return { 
            success: false, 
            message: 'حدث خطأ أثناء إنشاء الحساب'
        };
    }
}

/**
 * تسجيل دخول العميل
 */
function authenticateCustomer(email, password) {
    try {
        const customers = JSON.parse(localStorage.getItem('customers') || '[]');
        
        const customer = customers.find(c => 
            c.email.toLowerCase() === email.toLowerCase() && 
            c.password === password &&
            c.isActive
        );

        if (customer) {
            return {
                success: true,
                customer: customer,
                message: 'تم تسجيل الدخول بنجاح'
            };
        }

        return {
            success: false,
            message: 'البريد الإلكتروني أو كلمة المرور غير صحيحة'
        };

    } catch (error) {
        console.error('خطأ في تسجيل الدخول:', error);
        return {
            success: false,
            message: 'حدث خطأ أثناء تسجيل الدخول'
        };
    }
}

/**
 * إنشاء توكن JWT بسيط
 */
function createCustomerToken(customer) {
    const payload = {
        userId: customer.id,
        email: customer.email,
        role: customer.role,
        fullName: customer.fullName,
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // انتهاء الصلاحية بعد 24 ساعة
    };

    return btoa(JSON.stringify(payload));
}

/**
 * التحقق من صحة التوكن
 */
function validateCustomerToken(token) {
    try {
        const payload = JSON.parse(atob(token));
        const currentTime = Math.floor(Date.now() / 1000);
        
        if (payload.exp < currentTime) {
            return { valid: false, message: 'انتهت صلاحية الجلسة' };
        }

        return { valid: true, payload: payload };
    } catch (error) {
        return { valid: false, message: 'توكن غير صحيح' };
    }
}

/**
 * الحصول على العميل الحالي
 */
function getCurrentCustomer() {
    try {
        const token = localStorage.getItem('customer_token');
        if (!token) return null;

        const validation = validateCustomerToken(token);
        if (!validation.valid) {
            // حذف التوكن المنتهي الصلاحية
            localStorage.removeItem('customer_token');
            localStorage.removeItem('customer_data');
            return null;
        }

        const customerData = localStorage.getItem('customer_data');
        return customerData ? JSON.parse(customerData) : null;
    } catch (error) {
        console.error('خطأ في الحصول على بيانات العميل:', error);
        return null;
    }
}

/**
 * تسجيل خروج العميل
 */
function logoutCustomer() {
    localStorage.removeItem('customer_token');
    localStorage.removeItem('customer_data');
    currentCustomer = null;
    customerToken = null;
    
    // إعادة تحميل الصفحة لتحديث واجهة المستخدم
    window.location.reload();
}

/**
 * تحديث بيانات العميل
 */
function updateCustomerProfile(customerId, updatedData) {
    try {
        const customers = JSON.parse(localStorage.getItem('customers') || '[]');
        const customerIndex = customers.findIndex(c => c.id === customerId);
        
        if (customerIndex === -1) {
            return { success: false, message: 'العميل غير موجود' };
        }

        // التحقق من البيانات المحدثة
        if (updatedData.email || updatedData.phone) {
            const duplicateCheck = checkDuplicateCustomer(
                updatedData.email || customers[customerIndex].email,
                updatedData.phone || customers[customerIndex].phone,
                customerId
            );
            
            if (duplicateCheck.isDuplicate) {
                return { success: false, message: duplicateCheck.message };
            }
        }

        // تحديث البيانات
        customers[customerIndex] = {
            ...customers[customerIndex],
            ...updatedData,
            updatedAt: new Date().toISOString()
        };

        localStorage.setItem('customers', JSON.stringify(customers));

        // تحديث بيانات الجلسة
        const currentData = JSON.parse(localStorage.getItem('customer_data') || '{}');
        const updatedSessionData = { ...currentData, ...updatedData };
        localStorage.setItem('customer_data', JSON.stringify(updatedSessionData));

        return { 
            success: true, 
            customer: customers[customerIndex],
            message: 'تم تحديث البيانات بنجاح'
        };

    } catch (error) {
        console.error('خطأ في تحديث البيانات:', error);
        return { 
            success: false, 
            message: 'حدث خطأ أثناء تحديث البيانات'
        };
    }
}

// تصدير الدوال للاستخدام العام
window.registerCustomer = registerCustomer;
window.authenticateCustomer = authenticateCustomer;
window.createCustomerToken = createCustomerToken;
window.validateCustomerToken = validateCustomerToken;
window.getCurrentCustomer = getCurrentCustomer;
window.logoutCustomer = logoutCustomer;
window.updateCustomerProfile = updateCustomerProfile;
window.validateInput = validateInput;
window.checkDuplicateCustomer = checkDuplicateCustomer;
