<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام - VelaSweets</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #5a287d 0%, #f8a5c2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #f0f0f0;
            border-radius: 10px;
        }
        
        .test-section.success {
            border-color: #28a745;
            background: #f8fff9;
        }
        
        .test-section.error {
            border-color: #dc3545;
            background: #fff8f8;
        }
        
        .test-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #5a287d;
            margin-bottom: 15px;
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .test-item:last-child {
            border-bottom: none;
        }
        
        .test-status {
            font-weight: 600;
        }
        
        .test-status.pass {
            color: #28a745;
        }
        
        .test-status.fail {
            color: #dc3545;
        }
        
        .btn-test {
            background: #5a287d;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            font-size: 0.9rem;
            margin: 2px;
        }
        
        .btn-test:hover {
            background: #4a1f6b;
            color: white;
        }
        
        .results-summary {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">🧪 اختبار شامل لنظام VelaSweets</h1>
        
        <!-- اختبار نظام المصادقة -->
        <div class="test-section" id="authTests">
            <h2 class="test-title">🔐 اختبار نظام المصادقة</h2>
            <div class="test-item">
                <span>تحميل دوال المصادقة</span>
                <span class="test-status" id="authFunctionsStatus">⏳ جاري الاختبار...</span>
            </div>
            <div class="test-item">
                <span>التحقق من صحة رقم الهاتف (07xxxxxxxxx)</span>
                <span class="test-status" id="phoneValidationStatus">⏳ جاري الاختبار...</span>
            </div>
            <div class="test-item">
                <span>التحقق من عدم قبول الحسابات المكررة</span>
                <span class="test-status" id="duplicateCheckStatus">⏳ جاري الاختبار...</span>
            </div>
            <div class="test-item">
                <span>إنشاء حساب جديد</span>
                <button class="btn-test" onclick="testRegister()">اختبار التسجيل</button>
            </div>
            <div class="test-item">
                <span>تسجيل الدخول</span>
                <button class="btn-test" onclick="testLogin()">اختبار الدخول</button>
            </div>
        </div>
        
        <!-- اختبار العملة -->
        <div class="test-section" id="currencyTests">
            <h2 class="test-title">💰 اختبار نظام العملة</h2>
            <div class="test-item">
                <span>تحميل نظام العملة</span>
                <span class="test-status" id="currencySystemStatus">⏳ جاري الاختبار...</span>
            </div>
            <div class="test-item">
                <span>تنسيق الأسعار بالدينار العراقي</span>
                <span class="test-status" id="priceFormatStatus">⏳ جاري الاختبار...</span>
            </div>
            <div class="test-item">
                <span>حساب رسوم الشحن (البصرة: 3000، أخرى: 5000)</span>
                <span class="test-status" id="shippingCostStatus">⏳ جاري الاختبار...</span>
            </div>
        </div>
        
        <!-- اختبار اللغات -->
        <div class="test-section" id="languageTests">
            <h2 class="test-title">🌐 اختبار نظام اللغات</h2>
            <div class="test-item">
                <span>تحميل نظام اللغات</span>
                <span class="test-status" id="languageSystemStatus">⏳ جاري الاختبار...</span>
            </div>
            <div class="test-item">
                <span>دعم اللغات الثلاث (عربي، كردي، إنجليزي)</span>
                <span class="test-status" id="languageSupportStatus">⏳ جاري الاختبار...</span>
            </div>
            <div class="test-item">
                <span>تغيير اتجاه الصفحة (RTL/LTR)</span>
                <button class="btn-test" onclick="testLanguageDirection()">اختبار الاتجاه</button>
            </div>
        </div>
        
        <!-- اختبار المنتجات -->
        <div class="test-section" id="productTests">
            <h2 class="test-title">🛍️ اختبار نظام المنتجات</h2>
            <div class="test-item">
                <span>تحميل قاعدة بيانات المنتجات</span>
                <span class="test-status" id="productsLoadStatus">⏳ جاري الاختبار...</span>
            </div>
            <div class="test-item">
                <span>عرض الأسعار بالدينار العراقي</span>
                <span class="test-status" id="productPricesStatus">⏳ جاري الاختبار...</span>
            </div>
        </div>
        
        <!-- اختبار السلة والطلبات -->
        <div class="test-section" id="cartTests">
            <h2 class="test-title">🛒 اختبار السلة والطلبات</h2>
            <div class="test-item">
                <span>إضافة منتج للسلة</span>
                <button class="btn-test" onclick="testAddToCart()">اختبار السلة</button>
            </div>
            <div class="test-item">
                <span>حساب رسوم الشحن حسب المحافظة</span>
                <button class="btn-test" onclick="testShippingCalculation()">اختبار الشحن</button>
            </div>
        </div>
        
        <!-- ملخص النتائج -->
        <div class="results-summary">
            <h3>📊 ملخص نتائج الاختبار</h3>
            <div id="testResults">
                <p>جاري تشغيل الاختبارات...</p>
            </div>
        </div>
        
        <!-- أزرار الإجراءات -->
        <div class="text-center mt-4">
            <button class="btn btn-primary me-3" onclick="runAllTests()">
                <i class="bi bi-play-circle me-2"></i>تشغيل جميع الاختبارات
            </button>
            <button class="btn btn-success me-3" onclick="resetTestData()">
                <i class="bi bi-arrow-clockwise me-2"></i>إعادة تعيين البيانات
            </button>
            <a href="index.html" class="btn btn-outline-primary">
                <i class="bi bi-house me-2"></i>العودة للموقع
            </a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="scripts/customer-auth.js"></script>
    <script src="scripts/currency.js"></script>
    <script src="scripts/language.js"></script>
    <script src="database/products.js"></script>
    <script src="init-data.js"></script>
    <script>
        let testResults = {
            passed: 0,
            failed: 0,
            total: 0
        };
        
        document.addEventListener('DOMContentLoaded', function() {
            runAllTests();
        });
        
        function runAllTests() {
            testResults = { passed: 0, failed: 0, total: 0 };
            
            // اختبار نظام المصادقة
            testAuthSystem();
            
            // اختبار نظام العملة
            testCurrencySystem();
            
            // اختبار نظام اللغات
            testLanguageSystem();
            
            // اختبار المنتجات
            testProductSystem();
            
            // عرض النتائج
            setTimeout(updateResultsSummary, 1000);
        }
        
        function testAuthSystem() {
            // اختبار تحميل دوال المصادقة
            const authFunctionsExist = typeof registerCustomer === 'function' && 
                                     typeof authenticateCustomer === 'function' &&
                                     typeof validateInput === 'function';
            updateTestStatus('authFunctionsStatus', authFunctionsExist);
            
            // اختبار التحقق من رقم الهاتف
            const phoneValidation = validateInput('07123456789', 'phone');
            const invalidPhone = validateInput('123456789', 'phone');
            const phoneTestPass = phoneValidation.valid && !invalidPhone.valid;
            updateTestStatus('phoneValidationStatus', phoneTestPass);
            
            // اختبار التحقق من الحسابات المكررة
            const duplicateCheck = checkDuplicateCustomer('<EMAIL>', '07123456789');
            updateTestStatus('duplicateCheckStatus', !duplicateCheck.isDuplicate);
        }
        
        function testCurrencySystem() {
            // اختبار تحميل نظام العملة
            const currencySystemExists = typeof formatCurrency === 'function' && 
                                        typeof getShippingCost === 'function' &&
                                        typeof CURRENCY_CONFIG !== 'undefined';
            updateTestStatus('currencySystemStatus', currencySystemExists);
            
            // اختبار تنسيق الأسعار
            if (currencySystemExists) {
                const formattedPrice = formatCurrency(15000);
                const priceFormatCorrect = formattedPrice.includes('د.ع') && formattedPrice.includes('15,000');
                updateTestStatus('priceFormatStatus', priceFormatCorrect);
                
                // اختبار رسوم الشحن
                const basraShipping = getShippingCost('البصرة');
                const baghdadShipping = getShippingCost('بغداد');
                const shippingCorrect = basraShipping === 3000 && baghdadShipping === 5000;
                updateTestStatus('shippingCostStatus', shippingCorrect);
            }
        }
        
        function testLanguageSystem() {
            // اختبار تحميل نظام اللغات
            const languageSystemExists = typeof changeLanguage === 'function' && 
                                        typeof t === 'function' &&
                                        typeof SUPPORTED_LANGUAGES !== 'undefined';
            updateTestStatus('languageSystemStatus', languageSystemExists);
            
            // اختبار دعم اللغات الثلاث
            if (languageSystemExists) {
                const supportedLangs = Object.keys(SUPPORTED_LANGUAGES);
                const hasAllLanguages = supportedLangs.includes('ar') && 
                                      supportedLangs.includes('ku') && 
                                      supportedLangs.includes('en');
                updateTestStatus('languageSupportStatus', hasAllLanguages);
            }
        }
        
        function testProductSystem() {
            // اختبار تحميل المنتجات
            const productsExist = typeof window.products !== 'undefined' && 
                                Array.isArray(window.products) && 
                                window.products.length > 0;
            updateTestStatus('productsLoadStatus', productsExist);
            
            // اختبار أسعار المنتجات
            if (productsExist) {
                const firstProduct = window.products[0];
                const priceInIQD = firstProduct.price > 1000; // الأسعار بالدينار العراقي أكبر من 1000
                updateTestStatus('productPricesStatus', priceInIQD);
            }
        }
        
        function testRegister() {
            const testData = {
                fullName: 'اختبار المستخدم',
                email: 'test' + Date.now() + '@example.com',
                phone: '07' + Math.floor(Math.random() * 1000000000),
                province: 'بغداد',
                address: 'عنوان تجريبي',
                password: 'Test123!'
            };
            
            const result = registerCustomer(testData);
            alert(result.success ? 'نجح اختبار التسجيل!' : 'فشل اختبار التسجيل: ' + result.message);
        }
        
        function testLogin() {
            const customers = JSON.parse(localStorage.getItem('customers') || '[]');
            if (customers.length > 0) {
                const testCustomer = customers[0];
                const result = authenticateCustomer(testCustomer.email, testCustomer.password);
                alert(result.success ? 'نجح اختبار تسجيل الدخول!' : 'فشل اختبار تسجيل الدخول: ' + result.message);
            } else {
                alert('لا توجد حسابات للاختبار');
            }
        }
        
        function testLanguageDirection() {
            changeLanguage('en');
            setTimeout(() => {
                const isLTR = document.documentElement.getAttribute('dir') === 'ltr';
                changeLanguage('ar');
                setTimeout(() => {
                    const isRTL = document.documentElement.getAttribute('dir') === 'rtl';
                    alert(isLTR && isRTL ? 'نجح اختبار تغيير الاتجاه!' : 'فشل اختبار تغيير الاتجاه!');
                }, 100);
            }, 100);
        }
        
        function testAddToCart() {
            const cart = [];
            const testProduct = {
                id: 999,
                name: 'منتج تجريبي',
                price: 15000,
                image: 'test.jpg'
            };
            
            cart.push({
                id: testProduct.id,
                name: testProduct.name,
                price: testProduct.price,
                image: testProduct.image,
                quantity: 1
            });
            
            localStorage.setItem('cart', JSON.stringify(cart));
            alert('تم إضافة منتج تجريبي للسلة بنجاح!');
        }
        
        function testShippingCalculation() {
            const basraShipping = getShippingCost('البصرة');
            const baghdadShipping = getShippingCost('بغداد');
            
            const message = `رسوم الشحن:\nالبصرة: ${formatCurrency(basraShipping)}\nبغداد: ${formatCurrency(baghdadShipping)}`;
            alert(message);
        }
        
        function updateTestStatus(elementId, passed) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = passed ? '✅ نجح' : '❌ فشل';
                element.className = 'test-status ' + (passed ? 'pass' : 'fail');
                
                testResults.total++;
                if (passed) {
                    testResults.passed++;
                } else {
                    testResults.failed++;
                }
            }
        }
        
        function updateResultsSummary() {
            const resultsDiv = document.getElementById('testResults');
            const successRate = Math.round((testResults.passed / testResults.total) * 100);
            
            resultsDiv.innerHTML = `
                <div class="row">
                    <div class="col-md-3">
                        <strong>إجمالي الاختبارات:</strong> ${testResults.total}
                    </div>
                    <div class="col-md-3">
                        <strong style="color: #28a745;">نجح:</strong> ${testResults.passed}
                    </div>
                    <div class="col-md-3">
                        <strong style="color: #dc3545;">فشل:</strong> ${testResults.failed}
                    </div>
                    <div class="col-md-3">
                        <strong>معدل النجاح:</strong> ${successRate}%
                    </div>
                </div>
                <div class="mt-3">
                    <div class="progress">
                        <div class="progress-bar bg-success" style="width: ${successRate}%"></div>
                    </div>
                </div>
            `;
        }
        
        function resetTestData() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟')) {
                if (typeof resetAllData === 'function') {
                    resetAllData();
                } else {
                    localStorage.clear();
                    alert('تم حذف جميع البيانات. سيتم إعادة تحميل الصفحة.');
                    window.location.reload();
                }
            }
        }
    </script>
</body>
</html>
