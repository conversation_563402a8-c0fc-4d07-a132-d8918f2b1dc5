/**
 * نظام إدارة العملة - VelaSweets
 * العملة الرسمية: الدينار العراقي (IQD)
 */

// إعدادات العملة
const CURRENCY_CONFIG = {
    code: 'IQD',
    symbol: 'د.ع',
    name: 'دينار عراقي',
    nameEn: 'Iraqi Dinar',
    nameKu: 'دیناری عێراقی',
    decimalPlaces: 0, // الدينار العراقي لا يستخدم كسور عادة
    thousandsSeparator: ',',
    decimalSeparator: '.',
    symbolPosition: 'after' // الرمز بعد الرقم
};

// أسعار الشحن حسب المحافظة
const SHIPPING_RATES = {
    'البصرة': 3000,
    'بغداد': 5000,
    'أربيل': 5000,
    'النجف': 5000,
    'كربلاء': 5000,
    'الموصل': 5000,
    'السليمانية': 5000,
    'دهوك': 5000,
    'كركوك': 5000,
    'الأنبار': 5000,
    'بابل': 5000,
    'ديالى': 5000,
    'ذي قار': 5000,
    'صلاح الدين': 5000,
    'القادسية': 5000,
    'المثنى': 5000,
    'واسط': 5000,
    'ميسان': 5000,
    'default': 5000 // السعر الافتراضي لأي محافظة أخرى
};

/**
 * تنسيق المبلغ بالدينار العراقي
 * @param {number} amount - المبلغ
 * @param {boolean} showSymbol - إظهار رمز العملة
 * @returns {string} المبلغ منسق
 */
function formatCurrency(amount, showSymbol = true) {
    if (typeof amount !== 'number' || isNaN(amount)) {
        amount = 0;
    }
    
    // تقريب المبلغ لأقرب عدد صحيح
    amount = Math.round(amount);
    
    // تنسيق الأرقام بالفواصل
    const formattedAmount = amount.toLocaleString('ar-IQ');
    
    if (showSymbol) {
        return `${formattedAmount} ${CURRENCY_CONFIG.symbol}`;
    }
    
    return formattedAmount;
}

/**
 * تحويل المبلغ من دولار إلى دينار عراقي
 * @param {number} usdAmount - المبلغ بالدولار
 * @returns {number} المبلغ بالدينار العراقي
 */
function convertUSDToIQD(usdAmount) {
    // سعر الصرف التقريبي (يمكن تحديثه حسب السعر الحالي)
    const exchangeRate = 1320; // 1 دولار = 1320 دينار عراقي
    return Math.round(usdAmount * exchangeRate);
}

/**
 * الحصول على رسوم الشحن حسب المحافظة
 * @param {string} province - اسم المحافظة
 * @returns {number} رسوم الشحن بالدينار العراقي
 */
function getShippingCost(province) {
    if (!province) {
        return SHIPPING_RATES.default;
    }
    
    // البحث عن المحافظة (مع تجاهل الحالة والمسافات)
    const normalizedProvince = province.trim();
    
    return SHIPPING_RATES[normalizedProvince] || SHIPPING_RATES.default;
}

/**
 * حساب إجمالي الطلب مع الشحن
 * @param {number} subtotal - المجموع الفرعي
 * @param {string} province - المحافظة
 * @returns {object} تفاصيل الحساب
 */
function calculateOrderTotal(subtotal, province) {
    const shipping = getShippingCost(province);
    const total = subtotal + shipping;
    
    return {
        subtotal: subtotal,
        shipping: shipping,
        total: total,
        formattedSubtotal: formatCurrency(subtotal),
        formattedShipping: formatCurrency(shipping),
        formattedTotal: formatCurrency(total)
    };
}

/**
 * تحديث جميع أسعار المنتجات من دولار إلى دينار عراقي
 */
function updateProductPrices() {
    try {
        const products = JSON.parse(localStorage.getItem('velasweets_products') || '[]');
        let updated = false;
        
        const updatedProducts = products.map(product => {
            // التحقق من وجود أسعار بالدولار
            if (product.price && product.price < 100) { // افتراض أن الأسعار أقل من 100 هي بالدولار
                product.price = convertUSDToIQD(product.price);
                updated = true;
            }
            
            if (product.salePrice && product.salePrice < 100) {
                product.salePrice = convertUSDToIQD(product.salePrice);
                updated = true;
            }
            
            return product;
        });
        
        if (updated) {
            localStorage.setItem('velasweets_products', JSON.stringify(updatedProducts));
            console.log('تم تحديث أسعار المنتجات إلى الدينار العراقي');
        }
        
        return updatedProducts;
    } catch (error) {
        console.error('خطأ في تحديث أسعار المنتجات:', error);
        return [];
    }
}

/**
 * تحديث أسعار الطلبات المحفوظة
 */
function updateOrderPrices() {
    try {
        const orders = JSON.parse(localStorage.getItem('customer_orders') || '[]');
        let updated = false;
        
        const updatedOrders = orders.map(order => {
            // تحديث إجمالي الطلب
            if (order.total && order.total < 1000) { // افتراض أن المبالغ أقل من 1000 هي بالدولار
                order.total = convertUSDToIQD(order.total);
                updated = true;
            }
            
            // تحديث رسوم الشحن
            if (order.shipping && order.shipping < 100) {
                order.shipping = getShippingCost(order.customerData?.province || 'بغداد');
                updated = true;
            }
            
            // تحديث أسعار المنتجات في الطلب
            if (order.items && Array.isArray(order.items)) {
                order.items = order.items.map(item => {
                    if (item.price && item.price < 100) {
                        item.price = convertUSDToIQD(item.price);
                        updated = true;
                    }
                    return item;
                });
            }
            
            return order;
        });
        
        if (updated) {
            localStorage.setItem('customer_orders', JSON.stringify(updatedOrders));
            console.log('تم تحديث أسعار الطلبات إلى الدينار العراقي');
        }
        
        return updatedOrders;
    } catch (error) {
        console.error('خطأ في تحديث أسعار الطلبات:', error);
        return [];
    }
}

/**
 * تحديث عرض الأسعار في الصفحة
 */
function updatePriceDisplay() {
    // تحديث أسعار المنتجات
    const priceElements = document.querySelectorAll('.price, .product-price, .current-price');
    priceElements.forEach(element => {
        const text = element.textContent;
        const priceMatch = text.match(/\$?(\d+(?:\.\d{2})?)/);
        
        if (priceMatch) {
            const price = parseFloat(priceMatch[1]);
            if (price < 100) { // افتراض أن الأسعار أقل من 100 هي بالدولار
                const iqd = convertUSDToIQD(price);
                element.textContent = text.replace(priceMatch[0], formatCurrency(iqd));
            }
        }
    });
    
    // تحديث رسوم الشحن
    const shippingElements = document.querySelectorAll('.shipping-cost, .delivery-cost');
    shippingElements.forEach(element => {
        const province = element.dataset.province || 'بغداد';
        const cost = getShippingCost(province);
        element.textContent = formatCurrency(cost);
    });
}

/**
 * تهيئة نظام العملة
 */
function initializeCurrency() {
    // تحديث أسعار المنتجات والطلبات
    updateProductPrices();
    updateOrderPrices();
    
    // تحديث عرض الأسعار في الصفحة
    updatePriceDisplay();
    
    // حفظ إعدادات العملة
    localStorage.setItem('currency_config', JSON.stringify(CURRENCY_CONFIG));
    localStorage.setItem('shipping_rates', JSON.stringify(SHIPPING_RATES));
    
    console.log('تم تهيئة نظام العملة - الدينار العراقي');
}

/**
 * الحصول على قائمة المحافظات مع رسوم الشحن
 */
function getProvincesWithShipping() {
    return Object.keys(SHIPPING_RATES)
        .filter(key => key !== 'default')
        .map(province => ({
            name: province,
            shipping: SHIPPING_RATES[province],
            formattedShipping: formatCurrency(SHIPPING_RATES[province])
        }));
}

// تصدير الدوال للاستخدام العام
window.formatCurrency = formatCurrency;
window.convertUSDToIQD = convertUSDToIQD;
window.getShippingCost = getShippingCost;
window.calculateOrderTotal = calculateOrderTotal;
window.updateProductPrices = updateProductPrices;
window.updateOrderPrices = updateOrderPrices;
window.updatePriceDisplay = updatePriceDisplay;
window.initializeCurrency = initializeCurrency;
window.getProvincesWithShipping = getProvincesWithShipping;
window.CURRENCY_CONFIG = CURRENCY_CONFIG;
window.SHIPPING_RATES = SHIPPING_RATES;

// تهيئة النظام عند تحميل الملف
document.addEventListener('DOMContentLoaded', function() {
    initializeCurrency();
});
