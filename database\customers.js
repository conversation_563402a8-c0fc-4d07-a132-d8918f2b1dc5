/**
 * قاعدة بيانات العملاء لمتجر VelaSweets
 * يحتوي على بيانات العملاء المسجلين في المتجر
 */

// مصفوفة العملاء المسجلين
let customers = [
    {
        id: 1,
        fullName: "محمد أحمد",
        email: "<EMAIL>",
        phone: "0123456789",
        password: "P@ssw0rd123",
        province: "بغداد",
        address: "حي الكرادة، شارع 15",
        backupPhone: "0123456788",
        createdAt: "2023-10-15T10:30:00",
        lastLogin: "2023-11-20T15:45:00",
        orders: [1, 3, 5],
        status: "active"
    },
    {
        id: 2,
        fullName: "فاطمة علي",
        email: "<EMAIL>",
        phone: "0987654321",
        password: "F@tima2023",
        province: "البصرة",
        address: "حي العشار، شارع الكورنيش",
        backupPhone: "",
        createdAt: "2023-09-25T14:20:00",
        lastLogin: "2023-11-18T09:15:00",
        orders: [2, 4],
        status: "active"
    },
    {
        id: 3,
        fullName: "أحمد محمود",
        email: "<EMAIL>",
        phone: "0555666777",
        password: "Ahmed123!",
        province: "أربيل",
        address: "شارع 60 متري",
        backupPhone: "0555666888",
        createdAt: "2023-08-10T11:45:00",
        lastLogin: "2023-11-19T18:30:00",
        orders: [6],
        status: "active"
    }
];

/**
 * دالة للتحقق من وجود عميل بالبريد الإلكتروني
 * @param {string} email - البريد الإلكتروني للعميل
 * @returns {boolean} - هل يوجد عميل بهذا البريد الإلكتروني؟
 */
function isEmailExists(email) {
    return customers.some(customer => customer.email.toLowerCase() === email.toLowerCase());
}

/**
 * دالة للتحقق من وجود عميل برقم الهاتف
 * @param {string} phone - رقم هاتف العميل
 * @returns {boolean} - هل يوجد عميل بهذا رقم الهاتف؟
 */
function isPhoneExists(phone) {
    return customers.some(customer => customer.phone === phone);
}

/**
 * دالة للتحقق من صحة رقم الهاتف
 * @param {string} phone - رقم الهاتف
 * @returns {boolean} - هل رقم الهاتف صحيح؟
 */
function isValidPhone(phone) {
    // التحقق من أن رقم الهاتف يبدأ بـ 07 ويتكون من 11 رقماً
    return /^07\d{9}$/.test(phone);
}

/**
 * دالة للتحقق من قوة كلمة المرور
 * @param {string} password - كلمة المرور
 * @returns {boolean} - هل كلمة المرور قوية؟
 */
function isStrongPassword(password) {
    // التحقق من طول كلمة المرور
    if (password.length < 8) return false;
    
    // التحقق من وجود حرف كبير
    if (!/[A-Z]/.test(password)) return false;
    
    // التحقق من وجود حرف صغير
    if (!/[a-z]/.test(password)) return false;
    
    // التحقق من وجود رقم
    if (!/[0-9]/.test(password)) return false;
    
    // التحقق من وجود رمز خاص
    if (!/[^A-Za-z0-9]/.test(password)) return false;
    
    return true;
}

/**
 * دالة لتسجيل عميل جديد
 * @param {Object} customerData - بيانات العميل الجديد
 * @returns {Object} - نتيجة عملية التسجيل
 */
function registerCustomer(customerData) {
    // التحقق من وجود البريد الإلكتروني مسبقاً
    if (isEmailExists(customerData.email)) {
        return {
            success: false,
            message: "البريد الإلكتروني مستخدم بالفعل"
        };
    }
    
    // التحقق من وجود رقم الهاتف مسبقاً
    if (isPhoneExists(customerData.phone)) {
        return {
            success: false,
            message: "رقم الهاتف مستخدم بالفعل"
        };
    }
    
    // التحقق من صحة رقم الهاتف
    if (!isValidPhone(customerData.phone)) {
        return {
            success: false,
            message: "رقم الهاتف غير صحيح، يجب أن يبدأ بـ 07 ويتكون من 11 رقماً"
        };
    }
    
    // التحقق من صحة رقم الهاتف الاحتياطي إذا تم إدخاله
    if (customerData.backupPhone && !isValidPhone(customerData.backupPhone)) {
        return {
            success: false,
            message: "رقم الهاتف الاحتياطي غير صحيح، يجب أن يبدأ بـ 07 ويتكون من 11 رقماً"
        };
    }
    
    // التحقق من قوة كلمة المرور
    if (!isStrongPassword(customerData.password)) {
        return {
            success: false,
            message: "كلمة المرور ضعيفة. يجب أن تحتوي على حروف كبيرة وصغيرة وأرقام ورموز خاصة"
        };
    }
    
    // إنشاء معرف جديد
    const newId = customers.length > 0 ? Math.max(...customers.map(c => c.id)) + 1 : 1;
    
    // إنشاء كائن العميل الجديد
    const newCustomer = {
        id: newId,
        fullName: customerData.fullName,
        email: customerData.email,
        phone: customerData.phone,
        password: customerData.password, // في الإنتاج، يجب تشفير كلمة المرور
        province: customerData.province || "",
        address: customerData.address || "",
        backupPhone: customerData.backupPhone || "",
        createdAt: new Date().toISOString(),
        lastLogin: null,
        orders: [],
        status: "active"
    };
    
    // إضافة العميل إلى المصفوفة
    customers.push(newCustomer);
    
    // حفظ البيانات في التخزين المحلي
    saveCustomersData();
    
    // إرجاع نتيجة العملية
    return {
        success: true,
        message: "تم تسجيل العميل بنجاح",
        customerId: newId
    };
}

/**
 * دالة للتحقق من صحة بيانات تسجيل الدخول
 * @param {string} email - البريد الإلكتروني للعميل
 * @param {string} password - كلمة المرور
 * @returns {Object|null} - كائن العميل إذا كانت البيانات صحيحة، وإلا null
 */
function authenticateCustomer(email, password) {
    const customer = customers.find(c => c.email.toLowerCase() === email.toLowerCase() && c.password === password);
    if (customer) {
        // تحديث وقت آخر تسجيل دخول
        customer.lastLogin = new Date().toISOString();
        
        // حفظ البيانات في التخزين المحلي
        saveCustomersData();
        
        return {
            id: customer.id,
            fullName: customer.fullName,
            email: customer.email,
            phone: customer.phone,
            province: customer.province,
            address: customer.address,
            backupPhone: customer.backupPhone,
            createdAt: customer.createdAt,
            lastLogin: customer.lastLogin,
            orders: customer.orders,
            status: customer.status
        };
    }
    return null;
}

/**
 * دالة للبحث عن عميل بواسطة المعرف
 * @param {number} id - معرف العميل
 * @returns {Object|null} - كائن العميل إذا تم العثور عليه، وإلا null
 */
function getCustomerById(id) {
    const customer = customers.find(c => c.id === id);
    if (customer) {
        // إخفاء كلمة المرور
        const { password, ...customerData } = customer;
        return customerData;
    }
    return null;
}

/**
 * دالة للبحث عن عميل بواسطة البريد الإلكتروني
 * @param {string} email - البريد الإلكتروني للعميل
 * @returns {Object|null} - كائن العميل إذا تم العثور عليه، وإلا null
 */
function getCustomerByEmail(email) {
    const customer = customers.find(c => c.email.toLowerCase() === email.toLowerCase());
    if (customer) {
        // إخفاء كلمة المرور
        const { password, ...customerData } = customer;
        return customerData;
    }
    return null;
}

/**
 * دالة لتحديث بيانات العميل
 * @param {number} id - معرف العميل
 * @param {Object} updatedData - البيانات المحدثة
 * @returns {Object} - نتيجة عملية التحديث
 */
function updateCustomer(id, updatedData) {
    // البحث عن العميل
    const customerIndex = customers.findIndex(c => c.id === id);
    if (customerIndex === -1) {
        return {
            success: false,
            message: "لم يتم العثور على العميل"
        };
    }
    
    const customer = customers[customerIndex];
    
    // التحقق من عدم وجود تعارض في البريد الإلكتروني
    if (updatedData.email && updatedData.email !== customer.email) {
        if (isEmailExists(updatedData.email)) {
            return {
                success: false,
                message: "البريد الإلكتروني مستخدم بالفعل"
            };
        }
    }
    
    // التحقق من عدم وجود تعارض في رقم الهاتف
    if (updatedData.phone && updatedData.phone !== customer.phone) {
        if (isPhoneExists(updatedData.phone)) {
            return {
                success: false,
                message: "رقم الهاتف مستخدم بالفعل"
            };
        }
        
        // التحقق من صحة رقم الهاتف
        if (!isValidPhone(updatedData.phone)) {
            return {
                success: false,
                message: "رقم الهاتف غير صحيح، يجب أن يبدأ بـ 07 ويتكون من 11 رقماً"
            };
        }
    }
    
    // التحقق من صحة رقم الهاتف الاحتياطي إذا تم تحديثه
    if (updatedData.backupPhone && updatedData.backupPhone !== customer.backupPhone) {
        if (!isValidPhone(updatedData.backupPhone)) {
            return {
                success: false,
                message: "رقم الهاتف الاحتياطي غير صحيح، يجب أن يبدأ بـ 07 ويتكون من 11 رقماً"
            };
        }
    }
    
    // تحديث البيانات
    const updatedCustomer = {
        ...customer,
        fullName: updatedData.fullName || customer.fullName,
        email: updatedData.email || customer.email,
        phone: updatedData.phone || customer.phone,
        province: updatedData.province || customer.province,
        address: updatedData.address || customer.address,
        backupPhone: updatedData.backupPhone || customer.backupPhone,
        status: updatedData.status || customer.status
    };
    
    // حفظ البيانات المحدثة
    customers[customerIndex] = updatedCustomer;
    
    // حفظ البيانات في التخزين المحلي
    saveCustomersData();
    
    return {
        success: true,
        message: "تم تحديث بيانات العميل بنجاح",
        customer: {
            id: updatedCustomer.id,
            fullName: updatedCustomer.fullName,
            email: updatedCustomer.email,
            phone: updatedCustomer.phone,
            province: updatedCustomer.province,
            address: updatedCustomer.address,
            backupPhone: updatedCustomer.backupPhone,
            status: updatedCustomer.status
        }
    };
}

/**
 * دالة لتغيير كلمة المرور
 * @param {number} id - معرف العميل
 * @param {string} currentPassword - كلمة المرور الحالية
 * @param {string} newPassword - كلمة المرور الجديدة
 * @returns {Object} - نتيجة عملية تغيير كلمة المرور
 */
function changePassword(id, currentPassword, newPassword) {
    const customerIndex = customers.findIndex(c => c.id === id);
    if (customerIndex === -1) {
        return {
            success: false,
            message: "لم يتم العثور على العميل"
        };
    }
    
    // التحقق من كلمة المرور الحالية
    if (customers[customerIndex].password !== currentPassword) {
        return {
            success: false,
            message: "كلمة المرور الحالية غير صحيحة"
        };
    }
    
    // التحقق من قوة كلمة المرور الجديدة
    if (!isStrongPassword(newPassword)) {
        return {
            success: false,
            message: "كلمة المرور ضعيفة. يجب أن تحتوي على حروف كبيرة وصغيرة وأرقام ورموز خاصة"
        };
    }
    
    // تحديث كلمة المرور
    customers[customerIndex].password = newPassword;
    
    // حفظ البيانات في التخزين المحلي
    saveCustomersData();
    
    return {
        success: true,
        message: "تم تغيير كلمة المرور بنجاح"
    };
}

/**
 * دالة لحفظ بيانات العملاء في التخزين المحلي
 */
function saveCustomersData() {
    try {
        const customersToSave = customers.map(customer => {
            // حذف كلمات المرور قبل التخزين في حالة الإنتاج
            // في هذه النسخة التجريبية نحتفظ بها للتبسيط
            return customer;
        });
        
        localStorage.setItem('velasweets_customers', JSON.stringify(customersToSave));
        
        // إرسال حدث تحديث بيانات العملاء
        if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('customers_updated', { detail: customersToSave }));
        }
        
        return true;
    } catch (error) {
        return false;
    }
}

/**
 * دالة لتحميل بيانات العملاء من التخزين المحلي
 */
function loadCustomersData() {
    try {
        const savedData = localStorage.getItem('velasweets_customers');
        if (savedData) {
            const parsedData = JSON.parse(savedData);
            if (Array.isArray(parsedData) && parsedData.length > 0) {
                customers = parsedData;
                return true;
            }
        }
        
        // إذا لم يتم العثور على بيانات محفوظة، حفظ البيانات الافتراضية
        saveCustomersData();
        return false;
    } catch (error) {
        return false;
    }
}

// تنفيذ تحميل البيانات عند بدء تشغيل الصفحة
(function initializeCustomers() {
    loadCustomersData();
    
    // الاستماع لأحداث تخزين في نوافذ أخرى
    if (typeof window !== 'undefined') {
        window.addEventListener('storage', function(event) {
            if (event.key === 'velasweets_customers') {
                loadCustomersData();
                
                // إرسال حدث لإخطار الصفحة بتحديث البيانات
                window.dispatchEvent(new CustomEvent('customers_reloaded'));
            }
        });
    }
})();

// تصدير الدوال
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        customers,
        isEmailExists,
        isPhoneExists,
        isStrongPassword,
        registerCustomer,
        authenticateCustomer,
        getCustomerById,
        getCustomerByEmail,
        updateCustomer,
        changePassword,
        saveCustomersData,
        loadCustomersData
    };
}

// تصدير الدوال للنافذة العامة
if (typeof window !== 'undefined') {
    window.customers = customers;
    window.isEmailExists = isEmailExists;
    window.isPhoneExists = isPhoneExists;
    window.isStrongPassword = isStrongPassword;
    window.registerCustomer = registerCustomer;
    window.authenticateCustomer = authenticateCustomer;
    window.getCustomerById = getCustomerById;
    window.getCustomerByEmail = getCustomerByEmail;
    window.updateCustomer = updateCustomer;
    window.changePassword = changePassword;
    window.saveCustomersData = saveCustomersData;
    window.loadCustomersData = loadCustomersData;
} 