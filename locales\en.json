{"site.title": "VelaSweets - Sweets Store", "nav.home": "Home", "nav.products": "Products", "nav.cart": "<PERSON><PERSON>", "nav.about": "About Us", "nav.contact": "Contact", "nav.login": "<PERSON><PERSON>", "nav.register": "Register", "nav.profile": "Profile", "nav.logout": "Logout", "nav.language": "Language", "home.welcome": "Delicious Traditional Sweets", "home.subtitle": "Enjoy the finest Eastern and Western sweets made with love and perfection", "home.shop_now": "Shop Now", "home.featured": "Our Featured Products", "home.view_all": "View All", "products.title": "Our Products", "products.all": "All Products", "products.cakes": "Cakes", "products.eastern": "Eastern Sweets", "products.western": "Western Sweets", "products.cookies": "Cookies", "products.add_to_cart": "Add to Cart", "products.out_of_stock": "Out of Stock", "products.price": "Price", "products.available": "Available", "cart.title": "Shopping Cart", "cart.empty": "Cart is Empty", "cart.item": "<PERSON><PERSON>", "cart.quantity": "Quantity", "cart.price": "Price", "cart.total": "Total", "cart.subtotal": "Subtotal", "cart.shipping": "Shipping", "cart.final_total": "Final Total", "cart.checkout": "Checkout", "cart.continue_shopping": "Continue Shopping", "cart.remove": "Remove", "cart.update": "Update", "auth.login": "<PERSON><PERSON>", "auth.register": "Create New Account", "auth.email": "Email", "auth.password": "Password", "auth.confirm_password": "Confirm Password", "auth.full_name": "Full Name", "auth.phone": "Phone Number", "auth.province": "Province", "auth.address": "Address", "auth.remember_me": "Remember Me", "auth.forgot_password": "Forgot Password?", "auth.have_account": "Have an account?", "auth.no_account": "Don't have an account?", "auth.login_success": "Login successful", "auth.register_success": "Account created successfully", "auth.login_error": "Error in email or password", "auth.register_error": "Error creating account", "profile.title": "Profile", "profile.personal_info": "Personal Information", "profile.order_history": "Order History", "profile.settings": "Settings", "profile.edit": "Edit", "profile.save": "Save", "profile.cancel": "Cancel", "order.title": "Order", "order.number": "Order Number", "order.date": "Order Date", "order.status": "Order Status", "order.pending": "Pending", "order.processing": "Processing", "order.shipped": "Shipped", "order.delivered": "Delivered", "order.cancelled": "Cancelled", "contact.title": "Contact Us", "contact.name": "Name", "contact.email": "Email", "contact.subject": "Subject", "contact.message": "Message", "contact.send": "Send", "contact.phone": "Phone", "contact.address": "Address", "contact.hours": "Working Hours", "about.title": "About Us", "about.description": "We are VelaSweets store specialized in making the most delicious traditional and modern sweets", "about.mission": "Our Mission", "about.vision": "Our Vision", "about.values": "Our Values", "footer.about": "About VelaSweets", "footer.links": "Useful Links", "footer.contact": "Contact Information", "footer.social": "Follow Us", "footer.rights": "All Rights Reserved", "common.loading": "Loading...", "common.error": "An error occurred", "common.success": "Success", "common.confirm": "Confirm", "common.cancel": "Cancel", "common.yes": "Yes", "common.no": "No", "common.save": "Save", "common.edit": "Edit", "common.delete": "Delete", "common.view": "View", "common.search": "Search", "common.filter": "Filter", "common.sort": "Sort", "common.next": "Next", "common.previous": "Previous", "common.close": "Close"}