<!DOCTYPE html>
<html lang="ar" dir="rtl" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الملف الشخصي - متجر VelaSweets</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap');
        
        :root {
            --primary-color: #5a287d;
            --secondary-color: #f8a5c2;
            --tertiary-color: #b07d28;
            --text-color: #333;
            --text-light: #fff;
            --bg-color: #f8f9fa;
            --card-bg: #fff;
            --sidebar-bg: #fff;
            --sidebar-hover: #f1f1f1;
            --sidebar-active: #5a287d;
            --sidebar-active-text: #fff;
            --border-color: #dee2e6;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --transition-speed: 0.3s;
        }
        
        [data-theme="dark"] {
            --primary-color: #7e3ca3;
            --secondary-color: #ff9aba;
            --tertiary-color: #d9a440;
            --text-color: #e0e0e0;
            --text-light: #fff;
            --bg-color: #121212;
            --card-bg: #1e1e1e;
            --sidebar-bg: #1e1e1e;
            --sidebar-hover: #2d2d2d;
            --sidebar-active: #7e3ca3;
            --sidebar-active-text: #fff;
            --border-color: #2d2d2d;
            --shadow-color: rgba(0, 0, 0, 0.5);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            direction: rtl;
            min-height: 100vh;
        }
        
        .wrapper {
            display: flex;
            width: 100%;
            align-items: stretch;
        }
        
        #content {
            width: 100%;
            padding: 20px;
            min-height: 100vh;
            transition: all 0.3s;
        }
        
        .navbar {
            background-color: var(--card-bg);
            box-shadow: 0 2px 10px var(--shadow-color);
            padding: 15px 20px;
            margin-bottom: 30px;
            border-radius: 10px;
        }
        
        .profile-container {
            background-color: var(--card-bg);
            border-radius: 10px;
            box-shadow: 0 2px 10px var(--shadow-color);
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .profile-header {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .profile-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            font-weight: bold;
            margin-left: 20px;
        }
        
        .profile-info h2 {
            margin-bottom: 5px;
            color: var(--text-color);
        }
        
        .profile-info p {
            color: var(--text-color);
            opacity: 0.7;
            margin-bottom: 0;
        }
        
        .profile-form h3 {
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
            font-size: 1.2rem;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: var(--bg-color);
            color: var(--text-color);
            transition: all 0.3s;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(90, 40, 125, 0.25);
            outline: none;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .btn-primary:hover {
            opacity: 0.9;
            transform: translateY(-2px);
        }
        
        .password-section {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
        }
        
        .alert {
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background-color: rgba(40, 167, 69, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(40, 167, 69, 0.2);
        }
        
        .alert-danger {
            background-color: rgba(220, 53, 69, 0.1);
            color: var(--danger-color);
            border: 1px solid rgba(220, 53, 69, 0.2);
        }
        
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <div id="content">
            <nav class="navbar navbar-expand">
                <div class="container-fluid">
                    <a href="index.html" class="navbar-brand">
                        <h2>VelaSweets</h2>
                    </a>
                    
                    <div class="collapse navbar-collapse">
                        <ul class="navbar-nav ms-auto">
                            <li class="nav-item">
                                <a class="nav-link" href="index.html">الرئيسية</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="products.html">المنتجات</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="cart.html">سلة التسوق</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="orders.html">طلباتي</a>
                            </li>
                            <li class="nav-item active">
                                <a class="nav-link" href="profile.html">حسابي</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" id="logoutBtn">تسجيل الخروج</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>
            
            <div class="container">
                <div class="profile-container">
                    <div class="profile-header">
                        <div class="profile-avatar" id="profileAvatar"></div>
                        <div class="profile-info">
                            <h2 id="profileName">-</h2>
                            <p id="profileEmail">-</p>
                        </div>
                    </div>
                    
                    <div class="profile-form">
                        <div class="alert alert-success d-none" id="successAlert"></div>
                        <div class="alert alert-danger d-none" id="errorAlert"></div>
                        
                        <h3>بيانات الحساب</h3>
                        <form id="profileForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="fullName">الاسم الكامل</label>
                                        <input type="text" id="fullName" class="form-control" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="email">البريد الإلكتروني</label>
                                        <input type="email" id="email" class="form-control" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="phone">رقم الهاتف</label>
                                        <input type="tel" id="phone" class="form-control" pattern="07[0-9]{9}" title="يجب أن يبدأ الرقم بـ 07 ويتكون من 11 رقماً فقط" required>
                                        <small class="form-text text-muted">يجب أن يبدأ بـ 07 ويتكون من 11 رقماً</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="backupPhone">رقم الهاتف الاحتياطي</label>
                                        <input type="tel" id="backupPhone" class="form-control" pattern="07[0-9]{9}" title="يجب أن يبدأ الرقم بـ 07 ويتكون من 11 رقماً فقط (إذا تم إدخاله)">
                                        <small class="form-text text-muted">اختياري، يجب أن يبدأ بـ 07 ويتكون من 11 رقماً</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="province">المحافظة</label>
                                        <select id="province" class="form-control" required>
                                            <option value="" disabled>اختر المحافظة</option>
                                            <option value="بغداد">بغداد</option>
                                            <option value="البصرة">البصرة</option>
                                            <option value="نينوى">نينوى</option>
                                            <option value="أربيل">أربيل</option>
                                            <option value="النجف">النجف</option>
                                            <option value="ذي قار">ذي قار</option>
                                            <option value="كركوك">كركوك</option>
                                            <option value="الأنبار">الأنبار</option>
                                            <option value="ديالى">ديالى</option>
                                            <option value="المثنى">المثنى</option>
                                            <option value="القادسية">القادسية</option>
                                            <option value="ميسان">ميسان</option>
                                            <option value="واسط">واسط</option>
                                            <option value="صلاح الدين">صلاح الدين</option>
                                            <option value="دهوك">دهوك</option>
                                            <option value="السليمانية">السليمانية</option>
                                            <option value="بابل">بابل</option>
                                            <option value="كربلاء">كربلاء</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="address">العنوان التفصيلي</label>
                                        <textarea id="address" class="form-control" rows="2"></textarea>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary" id="saveProfileBtn">
                                    <span class="spinner d-none" id="profileSpinner"></span>
                                    حفظ التغييرات
                                </button>
                            </div>
                        </form>
                        
                        <div class="password-section">
                            <h3>تغيير كلمة المرور</h3>
                            <form id="passwordForm">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="currentPassword">كلمة المرور الحالية</label>
                                            <input type="password" id="currentPassword" class="form-control" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="newPassword">كلمة المرور الجديدة</label>
                                            <input type="password" id="newPassword" class="form-control" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="confirmPassword">تأكيد كلمة المرور الجديدة</label>
                                            <input type="password" id="confirmPassword" class="form-control" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="d-flex justify-content-end">
                                    <button type="submit" class="btn btn-primary" id="changePasswordBtn">
                                        <span class="spinner d-none" id="passwordSpinner"></span>
                                        تغيير كلمة المرور
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="scripts/auth.js"></script>
    <script src="database/customers.js"></script>
    <script src="scripts/customer-auth.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من تسجيل الدخول باستخدام النظام الجديد
            const currentCustomer = getCurrentCustomer();
            if (!currentCustomer) {
                alert('يجب تسجيل الدخول أولاً');
                window.location.href = 'login.html?redirect=' + encodeURIComponent(window.location.pathname);
                return;
            }

            // تحميل بيانات المستخدم
            let customerData = currentCustomer;
                return;
            }
            
            // التحقق من وجود بيانات المستخدم
            if (!customerData || !customerData.id) {
                console.error('بيانات المستخدم غير متوفرة');
                window.location.href = 'login.html';
                return;
            }
            
            // تعبئة بيانات المستخدم في النموذج
            document.getElementById('profileName').textContent = customerData.fullName || '-';
            document.getElementById('profileEmail').textContent = customerData.email || '-';
            document.getElementById('fullName').value = customerData.fullName || '';
            document.getElementById('email').value = customerData.email || '';
            document.getElementById('phone').value = customerData.phone || '';
            document.getElementById('backupPhone').value = customerData.backupPhone || '';
            document.getElementById('province').value = customerData.province || '';
            document.getElementById('address').value = customerData.address || '';
            
            // تعيين الحرف الأول من اسم المستخدم في الصورة الرمزية
            const profileAvatar = document.getElementById('profileAvatar');
            if (customerData.fullName) {
                profileAvatar.textContent = customerData.fullName.charAt(0).toUpperCase();
            } else {
                profileAvatar.textContent = 'U';
            }
            
            // معالجة حدث تحديث الملف الشخصي
            document.getElementById('profileForm').addEventListener('submit', function(event) {
                event.preventDefault();
                
                const successAlert = document.getElementById('successAlert');
                const errorAlert = document.getElementById('errorAlert');
                const saveProfileBtn = document.getElementById('saveProfileBtn');
                const profileSpinner = document.getElementById('profileSpinner');
                
                // إخفاء رسائل الخطأ والنجاح
                successAlert.classList.add('d-none');
                errorAlert.classList.add('d-none');
                
                // إظهار مؤشر التحميل
                profileSpinner.classList.remove('d-none');
                saveProfileBtn.setAttribute('disabled', 'disabled');
                
                // جمع البيانات المحدثة
                const updatedData = {
                    fullName: document.getElementById('fullName').value,
                    email: document.getElementById('email').value,
                    phone: document.getElementById('phone').value,
                    backupPhone: document.getElementById('backupPhone').value,
                    province: document.getElementById('province').value,
                    address: document.getElementById('address').value
                };
                
                // التحقق من رقم الهاتف
                const phoneRegex = /^07\d{9}$/;
                if (!phoneRegex.test(updatedData.phone)) {
                    errorAlert.textContent = 'رقم الهاتف غير صحيح، يجب أن يبدأ بـ 07 ويتكون من 11 رقماً';
                    errorAlert.classList.remove('d-none');
                    profileSpinner.classList.add('d-none');
                    saveProfileBtn.removeAttribute('disabled');
                    return;
                }
                
                // التحقق من رقم الهاتف الاحتياطي إذا تم إدخاله
                if (updatedData.backupPhone && !phoneRegex.test(updatedData.backupPhone)) {
                    errorAlert.textContent = 'رقم الهاتف الاحتياطي غير صحيح، يجب أن يبدأ بـ 07 ويتكون من 11 رقماً';
                    errorAlert.classList.remove('d-none');
                    profileSpinner.classList.add('d-none');
                    saveProfileBtn.removeAttribute('disabled');
                    return;
                }
                
                // استخدام نظام المصادقة الجديد لتحديث البيانات
                const result = updateCustomerProfile(customerData.id, updatedData);

                if (result.success) {
                    // تحديث البيانات المعروضة
                    document.getElementById('profileName').textContent = result.customer.fullName;
                    document.getElementById('profileEmail').textContent = result.customer.email;

                    // تحديث الصورة الرمزية
                    if (result.customer.fullName) {
                        profileAvatar.textContent = result.customer.fullName.charAt(0).toUpperCase();
                    }

                    // عرض رسالة النجاح
                    successAlert.textContent = result.message;
                    successAlert.classList.remove('d-none');
                } else {
                    // عرض رسالة الخطأ
                    errorAlert.textContent = result.message;
                    errorAlert.classList.remove('d-none');
                }
                
                // إخفاء مؤشر التحميل
                profileSpinner.classList.add('d-none');
                saveProfileBtn.removeAttribute('disabled');
            });
            
            // معالجة حدث تغيير كلمة المرور
            document.getElementById('passwordForm').addEventListener('submit', function(event) {
                event.preventDefault();
                
                const successAlert = document.getElementById('successAlert');
                const errorAlert = document.getElementById('errorAlert');
                const changePasswordBtn = document.getElementById('changePasswordBtn');
                const passwordSpinner = document.getElementById('passwordSpinner');
                
                // إخفاء رسائل الخطأ والنجاح
                successAlert.classList.add('d-none');
                errorAlert.classList.add('d-none');
                
                // الحصول على قيم كلمات المرور
                const currentPassword = document.getElementById('currentPassword').value;
                const newPassword = document.getElementById('newPassword').value;
                const confirmPassword = document.getElementById('confirmPassword').value;
                
                // التحقق من تطابق كلمتي المرور الجديدتين
                if (newPassword !== confirmPassword) {
                    errorAlert.textContent = 'كلمتا المرور الجديدتين غير متطابقتين';
                    errorAlert.classList.remove('d-none');
                    return;
                }
                
                // إظهار مؤشر التحميل
                passwordSpinner.classList.remove('d-none');
                changePasswordBtn.setAttribute('disabled', 'disabled');
                
                // التحقق من توفر دالة تغيير كلمة المرور
                if (typeof changePassword === 'function') {
                    try {
                        const result = changePassword(customerData.id, currentPassword, newPassword);
                        
                        if (result.success) {
                            // إعادة تعيين النموذج
                            document.getElementById('passwordForm').reset();
                            
                            // عرض رسالة النجاح
                            successAlert.textContent = result.message;
                            successAlert.classList.remove('d-none');
                        } else {
                            // عرض رسالة الخطأ
                            errorAlert.textContent = result.message;
                            errorAlert.classList.remove('d-none');
                        }
                    } catch (error) {
                        console.error('خطأ في تغيير كلمة المرور:', error);
                        errorAlert.textContent = 'حدث خطأ أثناء تغيير كلمة المرور';
                        errorAlert.classList.remove('d-none');
                    }
                } else {
                    // محاكاة نجاح تغيير كلمة المرور
                    document.getElementById('passwordForm').reset();
                    
                    // عرض رسالة النجاح
                    successAlert.textContent = 'تم تغيير كلمة المرور بنجاح';
                    successAlert.classList.remove('d-none');
                }
                
                // إخفاء مؤشر التحميل
                passwordSpinner.classList.add('d-none');
                changePasswordBtn.removeAttribute('disabled');
            });
            
            // معالجة تسجيل الخروج
            document.getElementById('logoutBtn').addEventListener('click', function() {
                if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                    logoutCustomer();
                }
            });
        });
    </script>
</body>
</html> 