/**
 * نظام اللغات المتعددة - VelaSweets
 * يدعم العربية والكردية والإنجليزية
 */

// اللغات المدعومة
const SUPPORTED_LANGUAGES = {
    ar: {
        code: 'ar',
        name: 'العربية',
        direction: 'rtl',
        flag: '🇮🇶'
    },
    ku: {
        code: 'ku',
        name: 'کوردی',
        direction: 'rtl',
        flag: '🏴'
    },
    en: {
        code: 'en',
        name: 'English',
        direction: 'ltr',
        flag: '🇺🇸'
    }
};

// اللغة الافتراضية
const DEFAULT_LANGUAGE = 'ar';

// متغير اللغة الحالية
let currentLanguage = DEFAULT_LANGUAGE;

// ملفات الترجمة
const translations = {
    ar: {
        // الواجهة الرئيسية
        'site.title': 'VelaSweets - متجر الحلويات',
        'nav.home': 'الرئيسية',
        'nav.products': 'المنتجات',
        'nav.about': 'من نحن',
        'nav.contact': 'اتصل بنا',
        'nav.cart': 'السلة',
        'nav.orders': 'طلباتي',
        'nav.profile': 'الملف الشخصي',
        'nav.settings': 'الإعدادات',
        'nav.login': 'تسجيل الدخول',
        'nav.register': 'إنشاء حساب',
        'nav.logout': 'تسجيل الخروج',
        
        // الصفحة الرئيسية
        'home.welcome': 'مرحباً بك في VelaSweets',
        'home.subtitle': 'أشهى الحلويات الشرقية والغربية',
        'home.featured': 'المنتجات المميزة',
        'home.view_all': 'عرض الكل',
        
        // المنتجات
        'products.title': 'منتجاتنا',
        'products.search': 'البحث في المنتجات...',
        'products.filter': 'تصفية',
        'products.sort': 'ترتيب حسب',
        'products.add_to_cart': 'أضف إلى السلة',
        'products.out_of_stock': 'نفد من المخزن',
        
        // السلة
        'cart.title': 'سلة التسوق',
        'cart.empty': 'سلة التسوق فارغة',
        'cart.subtotal': 'المجموع الفرعي',
        'cart.shipping': 'الشحن',
        'cart.total': 'المجموع الكلي',
        'cart.checkout': 'إتمام الطلب',
        'cart.continue_shopping': 'متابعة التسوق',
        
        // النماذج
        'form.name': 'الاسم الكامل',
        'form.email': 'البريد الإلكتروني',
        'form.phone': 'رقم الهاتف',
        'form.password': 'كلمة المرور',
        'form.confirm_password': 'تأكيد كلمة المرور',
        'form.province': 'المحافظة',
        'form.address': 'العنوان',
        'form.submit': 'إرسال',
        'form.cancel': 'إلغاء',
        
        // الرسائل
        'msg.success': 'تم بنجاح!',
        'msg.error': 'حدث خطأ!',
        'msg.loading': 'جاري التحميل...',
        'msg.login_required': 'يجب تسجيل الدخول أولاً',
        
        // العملة
        'currency.symbol': 'د.ع',
        'currency.name': 'دينار عراقي'
    },
    
    ku: {
        // الواجهة الرئيسية
        'site.title': 'VelaSweets - فرۆشگای شیرینی',
        'nav.home': 'سەرەتا',
        'nav.products': 'بەرهەمەکان',
        'nav.about': 'دەربارەمان',
        'nav.contact': 'پەیوەندی',
        'nav.cart': 'سەبەتە',
        'nav.orders': 'داواکارییەکانم',
        'nav.profile': 'پرۆفایل',
        'nav.settings': 'ڕێکخستنەکان',
        'nav.login': 'چوونەژوورەوە',
        'nav.register': 'دروستکردنی هەژمار',
        'nav.logout': 'چوونەدەرەوە',
        
        // الصفحة الرئيسية
        'home.welcome': 'بەخێربێیت بۆ VelaSweets',
        'home.subtitle': 'خۆشترین شیرینییە ڕۆژهەڵاتی و ڕۆژئاواییەکان',
        'home.featured': 'بەرهەمە تایبەتەکان',
        'home.view_all': 'بینینی هەموو',
        
        // المنتجات
        'products.title': 'بەرهەمەکانمان',
        'products.search': 'گەڕان لە بەرهەمەکان...',
        'products.filter': 'پاڵاوتن',
        'products.sort': 'ڕیزکردن بەپێی',
        'products.add_to_cart': 'زیادکردن بۆ سەبەتە',
        'products.out_of_stock': 'لە کۆگا نەماوە',
        
        // السلة
        'cart.title': 'سەبەتەی کڕین',
        'cart.empty': 'سەبەتەی کڕین بەتاڵە',
        'cart.subtotal': 'کۆی لاوەکی',
        'cart.shipping': 'گەیاندن',
        'cart.total': 'کۆی گشتی',
        'cart.checkout': 'تەواوکردنی داواکاری',
        'cart.continue_shopping': 'بەردەوامبوون لە کڕین',
        
        // النماذج
        'form.name': 'ناوی تەواو',
        'form.email': 'ئیمەیڵ',
        'form.phone': 'ژمارەی تەلەفۆن',
        'form.password': 'وشەی نهێنی',
        'form.confirm_password': 'دووپاتکردنەوەی وشەی نهێنی',
        'form.province': 'پارێزگا',
        'form.address': 'ناونیشان',
        'form.submit': 'ناردن',
        'form.cancel': 'هەڵوەشاندنەوە',
        
        // الرسائل
        'msg.success': 'سەرکەوتوو بوو!',
        'msg.error': 'هەڵەیەک ڕوویدا!',
        'msg.loading': 'بارکردن...',
        'msg.login_required': 'پێویستە سەرەتا بچیتە ژوورەوە',
        
        // العملة
        'currency.symbol': 'د.ع',
        'currency.name': 'دیناری عێراقی'
    },
    
    en: {
        // الواجهة الرئيسية
        'site.title': 'VelaSweets - Sweets Store',
        'nav.home': 'Home',
        'nav.products': 'Products',
        'nav.about': 'About Us',
        'nav.contact': 'Contact',
        'nav.cart': 'Cart',
        'nav.orders': 'My Orders',
        'nav.profile': 'Profile',
        'nav.settings': 'Settings',
        'nav.login': 'Login',
        'nav.register': 'Register',
        'nav.logout': 'Logout',
        
        // الصفحة الرئيسية
        'home.welcome': 'Welcome to VelaSweets',
        'home.subtitle': 'The finest Eastern and Western sweets',
        'home.featured': 'Featured Products',
        'home.view_all': 'View All',
        
        // المنتجات
        'products.title': 'Our Products',
        'products.search': 'Search products...',
        'products.filter': 'Filter',
        'products.sort': 'Sort by',
        'products.add_to_cart': 'Add to Cart',
        'products.out_of_stock': 'Out of Stock',
        
        // السلة
        'cart.title': 'Shopping Cart',
        'cart.empty': 'Shopping cart is empty',
        'cart.subtotal': 'Subtotal',
        'cart.shipping': 'Shipping',
        'cart.total': 'Total',
        'cart.checkout': 'Checkout',
        'cart.continue_shopping': 'Continue Shopping',
        
        // النماذج
        'form.name': 'Full Name',
        'form.email': 'Email',
        'form.phone': 'Phone Number',
        'form.password': 'Password',
        'form.confirm_password': 'Confirm Password',
        'form.province': 'Province',
        'form.address': 'Address',
        'form.submit': 'Submit',
        'form.cancel': 'Cancel',
        
        // الرسائل
        'msg.success': 'Success!',
        'msg.error': 'Error occurred!',
        'msg.loading': 'Loading...',
        'msg.login_required': 'Please login first',
        
        // العملة
        'currency.symbol': 'IQD',
        'currency.name': 'Iraqi Dinar'
    }
};

/**
 * الحصول على الترجمة
 */
function t(key, params = {}) {
    const translation = translations[currentLanguage]?.[key] || translations[DEFAULT_LANGUAGE]?.[key] || key;
    
    // استبدال المتغيرات في النص
    let result = translation;
    Object.keys(params).forEach(param => {
        result = result.replace(`{${param}}`, params[param]);
    });
    
    return result;
}

/**
 * تغيير اللغة
 */
function changeLanguage(languageCode) {
    if (!SUPPORTED_LANGUAGES[languageCode]) {
        console.error('اللغة غير مدعومة:', languageCode);
        return;
    }
    
    currentLanguage = languageCode;
    const language = SUPPORTED_LANGUAGES[languageCode];
    
    // تحديث اتجاه الصفحة
    document.documentElement.setAttribute('lang', language.code);
    document.documentElement.setAttribute('dir', language.direction);
    
    // تحديث النصوص في الصفحة
    updatePageTexts();
    
    // حفظ اللغة المختارة
    localStorage.setItem('selected_language', languageCode);
    
    console.log('تم تغيير اللغة إلى:', language.name);
}

/**
 * تحديث النصوص في الصفحة
 */
function updatePageTexts() {
    // تحديث العناصر التي تحتوي على data-translate
    const elements = document.querySelectorAll('[data-translate]');
    elements.forEach(element => {
        const key = element.getAttribute('data-translate');
        element.textContent = t(key);
    });
    
    // تحديث العناصر التي تحتوي على data-translate-placeholder
    const placeholderElements = document.querySelectorAll('[data-translate-placeholder]');
    placeholderElements.forEach(element => {
        const key = element.getAttribute('data-translate-placeholder');
        element.setAttribute('placeholder', t(key));
    });
    
    // تحديث عنوان الصفحة
    const titleElement = document.querySelector('title');
    if (titleElement) {
        titleElement.textContent = t('site.title');
    }
}

/**
 * تهيئة نظام اللغات
 */
function initializeLanguage() {
    // تحميل اللغة المحفوظة
    const savedLanguage = localStorage.getItem('selected_language') || DEFAULT_LANGUAGE;
    
    // تطبيق اللغة
    changeLanguage(savedLanguage);
    
    // تحديث قائمة اختيار اللغة
    updateLanguageSelector();
}

/**
 * تحديث قائمة اختيار اللغة
 */
function updateLanguageSelector() {
    const languageSelectors = document.querySelectorAll('.language-selector, #languageSelect');
    
    languageSelectors.forEach(selector => {
        // مسح الخيارات الحالية
        selector.innerHTML = '';
        
        // إضافة خيارات اللغات
        Object.values(SUPPORTED_LANGUAGES).forEach(language => {
            const option = document.createElement('option');
            option.value = language.code;
            option.textContent = `${language.flag} ${language.name}`;
            option.selected = language.code === currentLanguage;
            selector.appendChild(option);
        });
        
        // إضافة مستمع الحدث
        selector.addEventListener('change', function() {
            changeLanguage(this.value);
        });
    });
}

/**
 * إنشاء مبدل اللغة
 */
function createLanguageSwitcher() {
    const switcher = document.createElement('div');
    switcher.className = 'language-switcher';
    switcher.style.cssText = `
        position: fixed;
        top: 20px;
        left: 20px;
        z-index: 1000;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        padding: 5px;
    `;
    
    const select = document.createElement('select');
    select.className = 'form-select form-select-sm language-selector';
    select.style.border = 'none';
    
    switcher.appendChild(select);
    document.body.appendChild(switcher);
    
    updateLanguageSelector();
    
    return switcher;
}

/**
 * الحصول على اللغة الحالية
 */
function getCurrentLanguage() {
    return currentLanguage;
}

/**
 * الحصول على معلومات اللغة الحالية
 */
function getCurrentLanguageInfo() {
    return SUPPORTED_LANGUAGES[currentLanguage];
}

/**
 * تحديث تنسيق الأرقام حسب اللغة
 */
function formatNumber(number, options = {}) {
    const locale = currentLanguage === 'ar' ? 'ar-IQ' : 
                   currentLanguage === 'ku' ? 'ku-IQ' : 'en-US';
    
    return new Intl.NumberFormat(locale, options).format(number);
}

/**
 * تحديث تنسيق التاريخ حسب اللغة
 */
function formatDate(date, options = {}) {
    const locale = currentLanguage === 'ar' ? 'ar-IQ' : 
                   currentLanguage === 'ku' ? 'ku-IQ' : 'en-US';
    
    return new Intl.DateTimeFormat(locale, options).format(new Date(date));
}

// تصدير الدوال للاستخدام العام
window.t = t;
window.changeLanguage = changeLanguage;
window.updatePageTexts = updatePageTexts;
window.initializeLanguage = initializeLanguage;
window.updateLanguageSelector = updateLanguageSelector;
window.createLanguageSwitcher = createLanguageSwitcher;
window.getCurrentLanguage = getCurrentLanguage;
window.getCurrentLanguageInfo = getCurrentLanguageInfo;
window.formatNumber = formatNumber;
window.formatDate = formatDate;
window.SUPPORTED_LANGUAGES = SUPPORTED_LANGUAGES;

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeLanguage();
});
