/**
 * قاعدة بيانات الطلبات لمتجر VelaSweets
 * يحتوي على بيانات طلبات العملاء
 */

// مصفوفة الطلبات
let orders = [
    {
        id: 1,
        customerId: 1,
        orderNumber: "VS-2023-001",
        items: [
            { productId: 1, name: "كيك الشوكولاتة", quantity: 2, price: 15.99, total: 31.98 },
            { productId: 3, name: "كنافة", quantity: 1, price: 10.99, total: 10.99 }
        ],
        status: "completed",
        paymentMethod: "credit_card",
        paymentStatus: "paid",
        subtotal: 42.97,
        tax: 2.15,
        shippingCost: 5.00,
        discount: 0,
        total: 50.12,
        createdAt: "2023-11-15T14:30:00",
        updatedAt: "2023-11-15T15:00:00",
        shippingAddress: {
            fullName: "محمد أحمد",
            phone: "0123456789",
            address: "شارع الرشيد، بغداد",
            city: "بغداد",
            zipCode: "10001",
            country: "العراق"
        },
        notes: "توصيل عند الباب"
    },
    {
        id: 2,
        customerId: 2,
        orderNumber: "VS-2023-002",
        items: [
            { productId: 2, name: "بقلاوة", quantity: 3, price: 12.99, total: 38.97 }
        ],
        status: "completed",
        paymentMethod: "cash_on_delivery",
        paymentStatus: "paid",
        subtotal: 38.97,
        tax: 1.95,
        shippingCost: 5.00,
        discount: 0,
        total: 45.92,
        createdAt: "2023-11-16T10:15:00",
        updatedAt: "2023-11-16T11:00:00",
        shippingAddress: {
            fullName: "فاطمة علي",
            phone: "0987654321",
            address: "شارع الخليج، بصرة",
            city: "بصرة",
            zipCode: "60001",
            country: "العراق"
        },
        notes: ""
    },
    {
        id: 3,
        customerId: 1,
        orderNumber: "VS-2023-003",
        items: [
            { productId: 4, name: "كوكيز", quantity: 4, price: 8.99, total: 35.96 },
            { productId: 1, name: "كيك الشوكولاتة", quantity: 1, price: 15.99, total: 15.99 }
        ],
        status: "processing",
        paymentMethod: "credit_card",
        paymentStatus: "paid",
        subtotal: 51.95,
        tax: 2.60,
        shippingCost: 5.00,
        discount: 5.00,
        total: 54.55,
        createdAt: "2023-11-18T09:30:00",
        updatedAt: "2023-11-18T09:45:00",
        shippingAddress: {
            fullName: "محمد أحمد",
            phone: "0123456789",
            address: "شارع الرشيد، بغداد",
            city: "بغداد",
            zipCode: "10001",
            country: "العراق"
        },
        notes: "تغليف كهدية"
    },
    {
        id: 4,
        customerId: 2,
        orderNumber: "VS-2023-004",
        items: [
            { productId: 3, name: "كنافة", quantity: 2, price: 10.99, total: 21.98 }
        ],
        status: "pending",
        paymentMethod: "cash_on_delivery",
        paymentStatus: "pending",
        subtotal: 21.98,
        tax: 1.10,
        shippingCost: 5.00,
        discount: 0,
        total: 28.08,
        createdAt: "2023-11-19T16:45:00",
        updatedAt: "2023-11-19T16:45:00",
        shippingAddress: {
            fullName: "فاطمة علي",
            phone: "0987654321",
            address: "شارع الخليج، بصرة",
            city: "بصرة",
            zipCode: "60001",
            country: "العراق"
        },
        notes: ""
    },
    {
        id: 5,
        customerId: 1,
        orderNumber: "VS-2023-005",
        items: [
            { productId: 2, name: "بقلاوة", quantity: 2, price: 12.99, total: 25.98 },
            { productId: 4, name: "كوكيز", quantity: 3, price: 8.99, total: 26.97 }
        ],
        status: "shipped",
        paymentMethod: "credit_card",
        paymentStatus: "paid",
        subtotal: 52.95,
        tax: 2.65,
        shippingCost: 5.00,
        discount: 0,
        total: 60.60,
        createdAt: "2023-11-20T11:20:00",
        updatedAt: "2023-11-20T12:30:00",
        shippingAddress: {
            fullName: "محمد أحمد",
            phone: "0123456789",
            address: "شارع الرشيد، بغداد",
            city: "بغداد",
            zipCode: "10001",
            country: "العراق"
        },
        notes: ""
    },
    {
        id: 6,
        customerId: 3,
        orderNumber: "VS-2023-006",
        items: [
            { productId: 1, name: "كيك الشوكولاتة", quantity: 1, price: 15.99, total: 15.99 },
            { productId: 2, name: "بقلاوة", quantity: 1, price: 12.99, total: 12.99 },
            { productId: 3, name: "كنافة", quantity: 1, price: 10.99, total: 10.99 }
        ],
        status: "completed",
        paymentMethod: "credit_card",
        paymentStatus: "paid",
        subtotal: 39.97,
        tax: 2.00,
        shippingCost: 5.00,
        discount: 0,
        total: 46.97,
        createdAt: "2023-11-21T13:10:00",
        updatedAt: "2023-11-21T14:30:00",
        shippingAddress: {
            fullName: "أحمد محمود",
            phone: "0555666777",
            address: "شارع الزيتون، أربيل",
            city: "أربيل",
            zipCode: "44001",
            country: "العراق"
        },
        notes: ""
    }
];

/**
 * دالة للحصول على جميع الطلبات
 * @param {Object} filters - معايير التصفية (اختياري)
 * @returns {Array} - مصفوفة الطلبات
 */
function getAllOrders(filters = {}) {
    let filteredOrders = [...orders];
    
    // تطبيق التصفية حسب معرف العميل
    if (filters.customerId) {
        filteredOrders = filteredOrders.filter(order => order.customerId === filters.customerId);
    }
    
    // تطبيق التصفية حسب حالة الطلب
    if (filters.status) {
        filteredOrders = filteredOrders.filter(order => order.status === filters.status);
    }
    
    // تطبيق التصفية حسب التاريخ
    if (filters.startDate && filters.endDate) {
        filteredOrders = filteredOrders.filter(order => {
            const orderDate = new Date(order.createdAt);
            const startDate = new Date(filters.startDate);
            const endDate = new Date(filters.endDate);
            return orderDate >= startDate && orderDate <= endDate;
        });
    }
    
    // تطبيق الترتيب
    if (filters.sort) {
        switch (filters.sort) {
            case 'date_desc':
                filteredOrders.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
                break;
            case 'date_asc':
                filteredOrders.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
                break;
            case 'total_desc':
                filteredOrders.sort((a, b) => b.total - a.total);
                break;
            case 'total_asc':
                filteredOrders.sort((a, b) => a.total - b.total);
                break;
        }
    } else {
        // الترتيب الافتراضي حسب التاريخ (الأحدث أولاً)
        filteredOrders.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    }
    
    return filteredOrders;
}

/**
 * دالة للحصول على طلب بواسطة المعرف
 * @param {number} id - معرف الطلب
 * @returns {Object|null} - كائن الطلب إذا تم العثور عليه، وإلا null
 */
function getOrderById(id) {
    return orders.find(order => order.id === id) || null;
}

/**
 * دالة للحصول على طلب بواسطة رقم الطلب
 * @param {string} orderNumber - رقم الطلب
 * @returns {Object|null} - كائن الطلب إذا تم العثور عليه، وإلا null
 */
function getOrderByNumber(orderNumber) {
    return orders.find(order => order.orderNumber === orderNumber) || null;
}

/**
 * دالة للحصول على طلبات عميل
 * @param {number} customerId - معرف العميل
 * @returns {Array} - مصفوفة طلبات العميل
 */
function getCustomerOrders(customerId) {
    return orders.filter(order => order.customerId === customerId);
}

/**
 * دالة لإنشاء طلب جديد
 * @param {Object} orderData - بيانات الطلب الجديد
 * @returns {Object} - نتيجة عملية إنشاء الطلب
 */
function createOrder(orderData) {
    // التحقق من وجود معرف العميل
    if (!orderData.customerId) {
        return {
            success: false,
            message: "معرف العميل مطلوب"
        };
    }
    
    // التحقق من وجود عناصر الطلب
    if (!orderData.items || !Array.isArray(orderData.items) || orderData.items.length === 0) {
        return {
            success: false,
            message: "يجب أن يحتوي الطلب على عنصر واحد على الأقل"
        };
    }
    
    // إنشاء معرف جديد
    const newId = orders.length > 0 ? Math.max(...orders.map(o => o.id)) + 1 : 1;
    
    // إنشاء رقم طلب جديد
    const year = new Date().getFullYear();
    const orderCount = orders.filter(o => o.orderNumber.includes(`VS-${year}`)).length + 1;
    const newOrderNumber = `VS-${year}-${orderCount.toString().padStart(3, '0')}`;
    
    // حساب المجموع الفرعي
    const subtotal = orderData.items.reduce((sum, item) => sum + item.total, 0);
    
    // حساب الضريبة (5% افتراضياً)
    const tax = parseFloat((subtotal * 0.05).toFixed(2));
    
    // حساب التكلفة الإجمالية
    const total = parseFloat((
        subtotal + 
        tax + 
        (orderData.shippingCost || 5.00) - 
        (orderData.discount || 0)
    ).toFixed(2));
    
    // إنشاء كائن الطلب الجديد
    const newOrder = {
        id: newId,
        customerId: orderData.customerId,
        orderNumber: newOrderNumber,
        items: orderData.items,
        status: "pending", // الحالة الافتراضية
        paymentMethod: orderData.paymentMethod || "cash_on_delivery",
        paymentStatus: orderData.paymentMethod === "credit_card" ? "paid" : "pending",
        subtotal: subtotal,
        tax: tax,
        shippingCost: orderData.shippingCost || 5.00,
        discount: orderData.discount || 0,
        total: total,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        shippingAddress: orderData.shippingAddress,
        notes: orderData.notes || ""
    };
    
    // إضافة الطلب إلى المصفوفة
    orders.push(newOrder);
    
    // حفظ البيانات في التخزين المحلي
    saveOrdersData();
    
    // تحديث بيانات العميل (إضافة الطلب إلى قائمة طلباته)
    if (typeof updateCustomerOrders === 'function') {
        updateCustomerOrders(orderData.customerId, newId);
    }
    
    // إرجاع نتيجة العملية
    return {
        success: true,
        message: "تم إنشاء الطلب بنجاح",
        orderId: newId,
        orderNumber: newOrderNumber
    };
}

/**
 * دالة لتحديث حالة طلب
 * @param {number} id - معرف الطلب
 * @param {string} status - الحالة الجديدة
 * @returns {Object} - نتيجة عملية التحديث
 */
function updateOrderStatus(id, status) {
    const orderIndex = orders.findIndex(o => o.id === id);
    if (orderIndex === -1) {
        return {
            success: false,
            message: "لم يتم العثور على الطلب"
        };
    }
    
    // التحقق من صحة الحالة
    const validStatuses = ["pending", "processing", "shipped", "completed", "cancelled", "refunded"];
    if (!validStatuses.includes(status)) {
        return {
            success: false,
            message: "حالة الطلب غير صالحة"
        };
    }
    
    // تحديث الحالة
    orders[orderIndex].status = status;
    orders[orderIndex].updatedAt = new Date().toISOString();
    
    // حفظ البيانات في التخزين المحلي
    saveOrdersData();
    
    return {
        success: true,
        message: "تم تحديث حالة الطلب بنجاح"
    };
}

/**
 * دالة لتحديث حالة الدفع
 * @param {number} id - معرف الطلب
 * @param {string} paymentStatus - حالة الدفع الجديدة
 * @returns {Object} - نتيجة عملية التحديث
 */
function updatePaymentStatus(id, paymentStatus) {
    const orderIndex = orders.findIndex(o => o.id === id);
    if (orderIndex === -1) {
        return {
            success: false,
            message: "لم يتم العثور على الطلب"
        };
    }
    
    // التحقق من صحة حالة الدفع
    const validPaymentStatuses = ["pending", "paid", "refunded", "failed"];
    if (!validPaymentStatuses.includes(paymentStatus)) {
        return {
            success: false,
            message: "حالة الدفع غير صالحة"
        };
    }
    
    // تحديث حالة الدفع
    orders[orderIndex].paymentStatus = paymentStatus;
    orders[orderIndex].updatedAt = new Date().toISOString();
    
    // حفظ البيانات في التخزين المحلي
    saveOrdersData();
    
    return {
        success: true,
        message: "تم تحديث حالة الدفع بنجاح"
    };
}

/**
 * دالة لإلغاء طلب
 * @param {number} id - معرف الطلب
 * @param {string} reason - سبب الإلغاء
 * @returns {Object} - نتيجة عملية الإلغاء
 */
function cancelOrder(id, reason) {
    const orderIndex = orders.findIndex(o => o.id === id);
    if (orderIndex === -1) {
        return {
            success: false,
            message: "لم يتم العثور على الطلب"
        };
    }
    
    // التحقق من إمكانية إلغاء الطلب
    const order = orders[orderIndex];
    if (order.status === "shipped" || order.status === "completed") {
        return {
            success: false,
            message: "لا يمكن إلغاء طلب تم شحنه أو إكماله"
        };
    }
    
    // تحديث حالة الطلب
    order.status = "cancelled";
    order.updatedAt = new Date().toISOString();
    order.cancellationReason = reason || "لم يتم تحديد سبب";
    
    // حفظ البيانات في التخزين المحلي
    saveOrdersData();
    
    return {
        success: true,
        message: "تم إلغاء الطلب بنجاح"
    };
}

/**
 * دالة لحساب إحصائيات الطلبات
 * @param {Object} filters - معايير التصفية (اختياري)
 * @returns {Object} - إحصائيات الطلبات
 */
function getOrderStats(filters = {}) {
    let filteredOrders = getAllOrders(filters);
    
    // حساب إجمالي المبيعات
    const totalSales = filteredOrders.reduce((sum, order) => sum + order.total, 0);
    
    // حساب عدد الطلبات حسب الحالة
    const ordersByStatus = {
        pending: filteredOrders.filter(o => o.status === "pending").length,
        processing: filteredOrders.filter(o => o.status === "processing").length,
        shipped: filteredOrders.filter(o => o.status === "shipped").length,
        completed: filteredOrders.filter(o => o.status === "completed").length,
        cancelled: filteredOrders.filter(o => o.status === "cancelled").length,
        refunded: filteredOrders.filter(o => o.status === "refunded").length
    };
    
    // حساب متوسط قيمة الطلب
    const averageOrderValue = filteredOrders.length > 0 
        ? parseFloat((totalSales / filteredOrders.length).toFixed(2))
        : 0;
    
    // حساب المنتجات الأكثر مبيعاً
    const productSales = {};
    filteredOrders.forEach(order => {
        order.items.forEach(item => {
            if (productSales[item.productId]) {
                productSales[item.productId].quantity += item.quantity;
                productSales[item.productId].total += item.total;
            } else {
                productSales[item.productId] = {
                    name: item.name,
                    quantity: item.quantity,
                    total: item.total
                };
            }
        });
    });
    
    // تحويل المنتجات إلى مصفوفة وترتيبها
    const topProducts = Object.keys(productSales).map(productId => ({
        productId: parseInt(productId),
        name: productSales[productId].name,
        quantity: productSales[productId].quantity,
        total: productSales[productId].total
    })).sort((a, b) => b.quantity - a.quantity);
    
    return {
        totalSales,
        totalOrders: filteredOrders.length,
        averageOrderValue,
        ordersByStatus,
        topProducts: topProducts.slice(0, 5) // أعلى 5 منتجات
    };
}

/**
 * دالة لحفظ بيانات الطلبات في التخزين المحلي
 */
function saveOrdersData() {
    // تحويل البيانات إلى نص JSON
    const ordersJSON = JSON.stringify(orders);
    
    // حفظ البيانات في التخزين المحلي
    localStorage.setItem('velasweets_orders', ordersJSON);
}

/**
 * دالة لاسترجاع بيانات الطلبات من التخزين المحلي
 */
function loadOrdersData() {
    // استرجاع البيانات من التخزين المحلي
    const ordersJSON = localStorage.getItem('velasweets_orders');
    
    // إذا وجدت بيانات، قم بتحديث المصفوفة
    if (ordersJSON) {
        try {
            orders = JSON.parse(ordersJSON);
        } catch (error) {
            console.error('خطأ في تحليل بيانات الطلبات:', error);
        }
    }
}

/**
 * دالة لتحديث قائمة طلبات العميل (تستخدم مع database/customers.js)
 * @param {number} customerId - معرف العميل
 * @param {number} orderId - معرف الطلب
 */
function updateCustomerOrders(customerId, orderId) {
    // إذا كانت customers.js محملة، قم بتحديث بيانات العميل
    if (typeof getCustomerById === 'function' && typeof updateCustomer === 'function') {
        const customer = getCustomerById(customerId);
        if (customer) {
            const orders = customer.orders || [];
            if (!orders.includes(orderId)) {
                orders.push(orderId);
                updateCustomer(customerId, { orders });
            }
        }
    }
}

// تحميل بيانات الطلبات عند تشغيل التطبيق
loadOrdersData();

// تصدير الدوال
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        orders,
        getAllOrders,
        getOrderById,
        getOrderByNumber,
        getCustomerOrders,
        createOrder,
        updateOrderStatus,
        updatePaymentStatus,
        cancelOrder,
        getOrderStats
    };
} 