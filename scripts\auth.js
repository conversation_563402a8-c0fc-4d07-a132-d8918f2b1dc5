/**
 * نظام المصادقة لمتجر VelaSweets
 * يحتوي على دوال إدارة الجلسات وتوكنات JWT
 */

/**
 * دالة للتحقق من صحة بيانات تسجيل الدخول للموظفين
 * @param {string} email - البريد الإلكتروني للمستخدم
 * @param {string} password - كلمة المرور
 * @returns {Promise<Object|null>} - كائن المستخدم إذا كانت البيانات صحيحة، وإلا null
 */
async function login(email, password) {
    try {
        // في تطبيق حقيقي، هنا سيتم إرسال طلب إلى الخادم للتحقق
        // ولكن في هذا النموذج سنستخدم قاعدة البيانات المحلية مباشرة
        
        // البحث عن المستخدم في قاعدة البيانات
        let user = null;
        
        // طريقة 1: استخدام دالة المصادقة إذا كانت متاحة
        if (typeof authenticateUser === 'function') {
            user = authenticateUser(email, password);
        } 
        // طريقة 2: الوصول مباشرة إلى قائمة المستخدمين
        else if (typeof users !== 'undefined' && Array.isArray(users)) {
            user = users.find(u => u.email === email && u.password === password);
        }
        // طريقة 3: استخدام قائمة المستخدمين المحددة
        else {
            // قائمة ثابتة للمستخدمين في حال تعذر الوصول إلى القائمة من الملف الخارجي
            const defaultUsers = [
                {
                    id: 1,
                    name: "المدير",
                    email: "<EMAIL>",
                    password: "Admin123!",
                    role: "admin",
                    permissions: ["all"]
                },
                {
                    id: 2,
                    name: "موظف1",
                    email: "<EMAIL>",
                    password: "2025",
                    role: "employee",
                    permissions: ["view_orders", "edit_orders", "view_products"]
                },
                {
                    id: 3,
                    name: "موظف 2",
                    email: "<EMAIL>",
                    password: "2025",
                    role: "employee",
                    permissions: ["view_orders", "view_products", "edit_products"]
                },
                {
                    id: 4,
                    name: "موظف 3",
                    email: "<EMAIL>",
                    password: "2025",
                    role: "employee",
                    permissions: ["view_customers", "view_orders", "view_statistics"]
                }
            ];
            
            user = defaultUsers.find(u => u.email === email && u.password === password);
        }
        
        if (!user) {
            return null;
        }
        
        // إنشاء توكن JWT
        const token = generateJWT(user, document.getElementById('rememberMe')?.checked || false);
        
        // حفظ معلومات المستخدم وتوكن المصادقة
        saveAuthData(token, user);
        
        return user;
    } catch (error) {
        return null;
    }
}

/**
 * دالة للتحقق من صحة بيانات تسجيل دخول العملاء
 * @param {string} email - البريد الإلكتروني للعميل
 * @param {string} password - كلمة المرور
 * @returns {Promise<Object|null>} - كائن العميل إذا كانت البيانات صحيحة، وإلا null
 */
async function customerLogin(email, password) {
    try {
        // البحث عن العميل في قاعدة البيانات
        const customer = typeof authenticateCustomer === 'function' 
            ? authenticateCustomer(email, password) 
            : null;
        
        if (!customer) {
            return null;
        }
        
        // إنشاء توكن JWT
        const token = generateJWT(customer, document.getElementById('rememberMe')?.checked || false);
        
        // حفظ معلومات العميل وتوكن المصادقة
        saveCustomerAuthData(token, customer);
        
        return customer;
    } catch (error) {
        return null;
    }
}

/**
 * دالة لتسجيل الخروج وحذف بيانات الجلسة
 * @param {boolean} isCustomer - هل المستخدم عميل؟
 */
function logout(isCustomer = false) {
    if (isCustomer) {
        // حذف بيانات المصادقة للعميل من التخزين المحلي
        localStorage.removeItem('customer_token');
        localStorage.removeItem('customer_data');
        localStorage.removeItem('customer_remember_me');
        
        // إعادة توجيه العميل إلى صفحة تسجيل الدخول
        window.location.href = 'login.html';
    } else {
        // حذف بيانات المصادقة للموظف من التخزين المحلي
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_data');
        localStorage.removeItem('remember_me');
        
        // إعادة توجيه الموظف إلى صفحة تسجيل الدخول
        window.location.href = 'Administrationregistration.html';
    }
}

/**
 * دالة لإنشاء توكن JWT
 * @param {Object} user - بيانات المستخدم
 * @param {boolean} rememberMe - هل يجب تذكر المستخدم؟
 * @returns {string} - توكن JWT
 */
function generateJWT(user, rememberMe) {
    // إنشاء الرأس والحمولة
    const header = {
        alg: 'HS256',
        typ: 'JWT'
    };
    
    const payload = {
        sub: user.id,
        name: user.fullName || user.name,
        email: user.email,
        role: user.role,
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + (rememberMe ? 30 * 24 * 60 * 60 : 24 * 60 * 60) // 30 يوم أو 1 يوم
    };
    
    // تحويل الرأس والحمولة إلى سلاسل مشفرة بـ Base64
    const encodedHeader = btoa(JSON.stringify(header));
    const encodedPayload = btoa(JSON.stringify(payload));
    
    // التوقيع (في الإنتاج، يتم ذلك باستخدام مفتاح سري آمن)
    const signature = btoa(`${encodedHeader}.${encodedPayload}`);
    
    // إنشاء التوكن الكامل
    return `${encodedHeader}.${encodedPayload}.${signature}`;
}

/**
 * دالة لتحليل توكن JWT
 * @param {string} token - توكن JWT
 * @returns {Object|null} - كائن حمولة التوكن، أو null في حالة الخطأ
 */
function parseJwt(token) {
    try {
        const base64Payload = token.split('.')[1];
        const payload = atob(base64Payload);
        return JSON.parse(payload);
    } catch (error) {
        return null;
    }
}

/**
 * دالة لحفظ بيانات المصادقة للموظفين
 * @param {string} token - توكن JWT
 * @param {Object} user - بيانات المستخدم
 */
function saveAuthData(token, user) {
    // حفظ التوكن
    localStorage.setItem('auth_token', token);
    
    // حفظ بيانات المستخدم (بدون كلمة المرور)
    const userData = {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        permissions: user.permissions || []
    };
    
    localStorage.setItem('user_data', JSON.stringify(userData));
}

/**
 * دالة لحفظ بيانات المصادقة للعملاء
 * @param {string} token - توكن JWT
 * @param {Object} customer - بيانات العميل
 */
function saveCustomerAuthData(token, customer) {
    // حفظ التوكن
    localStorage.setItem('customer_token', token);
    
    // حفظ بيانات العميل (بدون كلمة المرور)
    localStorage.setItem('customer_data', JSON.stringify(customer));
}

/**
 * دالة للتحقق من صلاحية توكن الموظف
 * @returns {Object|null} - بيانات المستخدم إذا كان التوكن صالحًا، وإلا null
 */
function validateToken() {
    const token = localStorage.getItem('auth_token');
    
    if (!token) {
        return null;
    }
    
    try {
        const payload = parseJwt(token);
        const currentTime = Math.floor(Date.now() / 1000);
        
        // التحقق من انتهاء صلاحية التوكن
        if (payload.exp < currentTime) {
            // حذف التوكن المنتهي
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user_data');
            return null;
        }
        
        // إرجاع بيانات المستخدم من التخزين المحلي
        const userData = JSON.parse(localStorage.getItem('user_data') || '{}');
        return userData;
    } catch (error) {
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_data');
        return null;
    }
}

/**
 * دالة للتحقق من صلاحية توكن العميل
 * @returns {Object|null} - بيانات العميل إذا كان التوكن صالحًا، وإلا null
 */
function validateCustomerToken() {
    const token = localStorage.getItem('customer_token');
    
    if (!token) {
        return null;
    }
    
    try {
        const payload = parseJwt(token);
        const currentTime = Math.floor(Date.now() / 1000);
        
        // التحقق من انتهاء صلاحية التوكن
        if (payload.exp < currentTime) {
            // حذف التوكن المنتهي
            localStorage.removeItem('customer_token');
            localStorage.removeItem('customer_data');
            return null;
        }
        
        // إرجاع بيانات العميل من التخزين المحلي
        const customerData = JSON.parse(localStorage.getItem('customer_data') || '{}');
        return customerData;
    } catch (error) {
        localStorage.removeItem('customer_token');
        localStorage.removeItem('customer_data');
        return null;
    }
}

/**
 * دالة للتحقق من الصلاحيات
 * @param {string} permission - الصلاحية المطلوبة
 * @returns {boolean} - هل يملك المستخدم الصلاحية؟
 */
function checkPermission(permission) {
    const userData = validateToken();
    
    if (!userData) {
        return false;
    }
    
    // المدير لديه جميع الصلاحيات
    if (userData.role === 'admin' || userData.permissions?.includes('all')) {
        return true;
    }
    
    // التحقق من وجود الصلاحية المحددة
    return userData.permissions?.includes(permission) || false;
}

/**
 * دالة للتحقق مما إذا كان المستخدم مسجل الدخول
 * @param {boolean} isCustomer - هل المستخدم عميل؟
 * @returns {boolean} - هل المستخدم مسجل الدخول؟
 */
function isAuthenticated(isCustomer = false) {
    return isCustomer ? validateCustomerToken() !== null : validateToken() !== null;
}

/**
 * دالة للحصول على بيانات المستخدم الحالي
 * @param {boolean} isCustomer - هل المستخدم عميل؟
 * @returns {Object|null} - بيانات المستخدم إذا كان مسجل الدخول، وإلا null
 */
function getCurrentUser(isCustomer = false) {
    return isCustomer ? validateCustomerToken() : validateToken();
}

/**
 * دالة لحماية الصفحات التي تتطلب تسجيل دخول الموظفين
 * يجب استدعاؤها في بداية كل صفحة محمية
 * @param {string} redirectUrl - المسار للتوجيه في حالة عدم تسجيل الدخول
 * @param {string|Array} requiredPermission - الصلاحية أو الصلاحيات المطلوبة (اختياري)
 */
function protectPage(redirectUrl = 'Administrationregistration.html', requiredPermission = null) {
    const user = validateToken();
    
    // التحقق من تسجيل الدخول
    if (!user) {
        window.location.href = redirectUrl;
        return;
    }
    
    // التحقق من الصلاحيات إذا كانت مطلوبة
    if (requiredPermission) {
        let hasRequired = false;
        
        if (Array.isArray(requiredPermission)) {
            // يجب أن يمتلك إحدى الصلاحيات على الأقل
            hasRequired = requiredPermission.some(perm => checkPermission(perm));
        } else {
            // يجب أن يمتلك هذه الصلاحية
            hasRequired = checkPermission(requiredPermission);
        }
        
        if (!hasRequired) {
            // إعادة توجيه إلى صفحة غير مصرح
            window.location.href = 'unauthorized.html';
            return;
        }
    }
}

/**
 * دالة لحماية الصفحات التي تتطلب تسجيل دخول العملاء
 * يجب استدعاؤها في بداية كل صفحة محمية
 * @param {string} redirectUrl - المسار للتوجيه في حالة عدم تسجيل الدخول
 */
function protectCustomerPage(redirectUrl = 'login.html') {
    const customer = validateCustomerToken();
    
    // التحقق من تسجيل الدخول
    if (!customer) {
        window.location.href = redirectUrl;
        return;
    }
}

// تنفيذ التحقق التلقائي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // هذه الدالة تحقق من حالة تسجيل الدخول في الصفحات التي لا تحتاج حماية
    // مثل صفحة تسجيل الدخول نفسها
    function checkAuthState() {
        // الحصول على مسار الصفحة الحالية
        const currentPath = window.location.pathname.split('/').pop();
        
        // قائمة الصفحات العامة التي لا تتطلب تسجيل الدخول للموظفين
        const adminPublicPages = ['Administrationregistration.html', 'register-admin.html', 'forgot-password-admin.html'];
        
        // قائمة الصفحات العامة التي لا تتطلب تسجيل دخول العملاء
        const customerPublicPages = ['login.html', 'register.html', 'forgot-password.html'];
        
        // التحقق مما إذا كانت الصفحة الحالية عامة للموظفين
        const isAdminPublicPage = adminPublicPages.some(page => currentPath.includes(page));
        
        // التحقق مما إذا كانت الصفحة الحالية عامة للعملاء
        const isCustomerPublicPage = customerPublicPages.some(page => currentPath.includes(page));
        
        // إذا كان الموظف مسجل الدخول وهو على صفحة عامة للموظفين، توجيهه إلى لوحة التحكم
        if (isAuthenticated(false) && isAdminPublicPage) {
            window.location.href = 'dashboard.html';
            return;
        }
        
        // إذا كان العميل مسجل الدخول وهو على صفحة عامة للعملاء، توجيهه إلى الصفحة الرئيسية
        if (isAuthenticated(true) && isCustomerPublicPage) {
            window.location.href = 'index.html';
            return;
        }
    }
    
    // تنفيذ التحقق
    checkAuthState();
});
