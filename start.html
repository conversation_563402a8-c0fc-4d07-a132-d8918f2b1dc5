<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مرحباً بك في VelaSweets</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #5a287d 0%, #f8a5c2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .welcome-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 600px;
            width: 90%;
        }
        
        .logo {
            font-size: 3rem;
            font-weight: 700;
            color: #5a287d;
            margin-bottom: 20px;
        }
        
        .subtitle {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 30px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature-item {
            padding: 20px;
            border: 2px solid #f0f0f0;
            border-radius: 10px;
            transition: all 0.3s;
        }
        
        .feature-item:hover {
            border-color: #5a287d;
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 2rem;
            color: #5a287d;
            margin-bottom: 10px;
        }
        
        .btn-custom {
            background: linear-gradient(135deg, #5a287d 0%, #f8a5c2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            transition: all 0.3s;
        }
        
        .btn-custom:hover {
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(90, 40, 125, 0.3);
        }
        
        .demo-accounts {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
            text-align: right;
        }
        
        .demo-title {
            font-weight: 600;
            color: #5a287d;
            margin-bottom: 15px;
        }
        
        .account-item {
            margin-bottom: 10px;
            font-size: 0.9rem;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #5a287d;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="welcome-card">
        <h1 class="logo">🍰 VelaSweets</h1>
        <p class="subtitle">متجر الحلويات الإلكتروني المتكامل</p>
        
        <div class="stats">
            <div class="stat-item">
                <div class="stat-number" id="productsCount">20+</div>
                <div class="stat-label">منتج</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="customersCount">4</div>
                <div class="stat-label">عميل</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="ordersCount">3</div>
                <div class="stat-label">طلب</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">100%</div>
                <div class="stat-label">جاهز</div>
            </div>
        </div>
        
        <div class="feature-grid">
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="bi bi-shop"></i>
                </div>
                <h5>متجر متكامل</h5>
                <p>تصفح وشراء الحلويات بسهولة</p>
            </div>
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="bi bi-speedometer2"></i>
                </div>
                <h5>لوحة تحكم</h5>
                <p>إدارة شاملة للمنتجات والطلبات</p>
            </div>
        </div>
        
        <div class="text-center">
            <a href="index.html" class="btn-custom">
                <i class="bi bi-house me-2"></i>
                دخول المتجر
            </a>
            <a href="admin-dashboard.html" class="btn-custom">
                <i class="bi bi-gear me-2"></i>
                لوحة الإدارة
            </a>
        </div>
        
        <div class="demo-accounts">
            <div class="demo-title">
                <i class="bi bi-key me-2"></i>
                حسابات تجريبية
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <strong>للعملاء:</strong>
                    <div class="account-item">📧 <EMAIL></div>
                    <div class="account-item">🔑 123456</div>
                    <div class="account-item">📧 <EMAIL></div>
                    <div class="account-item">🔑 123456</div>
                </div>
                <div class="col-md-6">
                    <strong>للإدارة:</strong>
                    <div class="account-item">📧 <EMAIL></div>
                    <div class="account-item">🔑 admin123</div>
                </div>
            </div>
            
            <div class="mt-3">
                <small class="text-muted">
                    <i class="bi bi-info-circle me-1"></i>
                    يمكنك أيضاً إنشاء حساب جديد من صفحة التسجيل
                </small>
            </div>
        </div>
        
        <div class="mt-4">
            <small class="text-muted">
                <i class="bi bi-github me-1"></i>
                مشروع مفتوح المصدر - تم تطويره بـ HTML, CSS, JavaScript
            </small>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="init-data.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // تحديث الإحصائيات
            updateStats();
            
            // إضافة تأثيرات بصرية
            animateStats();
        });
        
        function updateStats() {
            // تحديث عدد المنتجات
            const products = JSON.parse(localStorage.getItem('velasweets_products') || '[]');
            if (products.length > 0) {
                document.getElementById('productsCount').textContent = products.length;
            }
            
            // تحديث عدد العملاء
            const customers = JSON.parse(localStorage.getItem('customers') || '[]');
            if (customers.length > 0) {
                document.getElementById('customersCount').textContent = customers.length;
            }
            
            // تحديث عدد الطلبات
            const orders = JSON.parse(localStorage.getItem('customer_orders') || '[]');
            if (orders.length > 0) {
                document.getElementById('ordersCount').textContent = orders.length;
            }
        }
        
        function animateStats() {
            const statNumbers = document.querySelectorAll('.stat-number');
            
            statNumbers.forEach((stat, index) => {
                setTimeout(() => {
                    stat.style.transform = 'scale(1.2)';
                    stat.style.color = '#f8a5c2';
                    
                    setTimeout(() => {
                        stat.style.transform = 'scale(1)';
                        stat.style.color = '#5a287d';
                    }, 300);
                }, index * 200);
            });
        }
        
        // تحديث الإحصائيات كل 5 ثوان
        setInterval(updateStats, 5000);
    </script>
</body>
</html>
