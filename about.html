<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>من نحن - متجر VelaSweets</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap');
        
        :root {
            --primary-color: #5a287d;
            --secondary-color: #f8a5c2;
            --tertiary-color: #b07d28;
            --text-color: #333;
            --bg-color: #f8f9fa;
            --card-bg: #fff;
            --border-color: #dee2e6;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
        }
        
        .navbar-custom {
            background-color: white;
            box-shadow: 0 2px 10px var(--shadow-color);
            padding: 15px 0;
        }
        
        .navbar-brand {
            font-family: 'Playfair Display', serif;
            color: var(--primary-color);
            font-size: 1.8rem;
            font-weight: 600;
        }
        
        .nav-link {
            color: var(--text-color);
            font-weight: 500;
            margin: 0 10px;
            transition: all 0.3s;
        }
        
        .nav-link:hover, .nav-link.active {
            color: var(--primary-color);
        }
        
        .hero-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 80px 0;
            text-align: center;
        }
        
        .hero-title {
            font-family: 'Playfair Display', serif;
            font-size: 3rem;
            margin-bottom: 20px;
        }
        
        .hero-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .about-section {
            padding: 80px 0;
        }
        
        .section-title {
            font-family: 'Playfair Display', serif;
            color: var(--primary-color);
            margin-bottom: 30px;
            text-align: center;
            font-size: 2.5rem;
        }
        
        .about-content {
            background-color: var(--card-bg);
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px var(--shadow-color);
            margin-bottom: 50px;
        }
        
        .about-text {
            font-size: 1.1rem;
            line-height: 1.8;
            text-align: justify;
            margin-bottom: 30px;
        }
        
        .values-section {
            padding: 60px 0;
            background-color: white;
        }
        
        .value-card {
            background-color: var(--card-bg);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 5px 20px var(--shadow-color);
            transition: all 0.3s;
            margin-bottom: 30px;
            height: 100%;
        }
        
        .value-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px var(--shadow-color);
        }
        
        .value-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2rem;
            color: white;
        }
        
        .value-title {
            font-family: 'Playfair Display', serif;
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 1.5rem;
        }
        
        .value-description {
            color: #666;
            line-height: 1.6;
        }
        
        .stats-section {
            padding: 60px 0;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
        }
        
        .stat-item {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .stat-number {
            font-family: 'Playfair Display', serif;
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .team-section {
            padding: 80px 0;
        }
        
        .team-member {
            background-color: var(--card-bg);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 5px 20px var(--shadow-color);
            transition: all 0.3s;
            margin-bottom: 30px;
        }
        
        .team-member:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px var(--shadow-color);
        }
        
        .member-photo {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: white;
            font-weight: bold;
        }
        
        .member-name {
            font-family: 'Playfair Display', serif;
            color: var(--primary-color);
            margin-bottom: 10px;
            font-size: 1.3rem;
        }
        
        .member-role {
            color: var(--tertiary-color);
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .member-description {
            color: #666;
            line-height: 1.6;
        }
        
        .cart-icon {
            position: relative;
        }
        
        .cart-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background-color: var(--secondary-color);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .user-welcome {
            display: none;
        }
        
        .user-welcome.logged-in {
            display: block;
        }
        
        .footer {
            background-color: var(--primary-color);
            color: white;
            padding: 40px 0 20px;
            margin-top: 60px;
        }
        
        .footer-title {
            font-family: 'Playfair Display', serif;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }
        
        .footer-links {
            list-style: none;
            padding: 0;
        }
        
        .footer-links li {
            margin-bottom: 8px;
        }
        
        .footer-links a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .footer-links a:hover {
            color: white;
            padding-right: 5px;
        }
        
        .footer-bottom {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 20px;
            margin-top: 30px;
            text-align: center;
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
        }
        
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }
            
            .section-title {
                font-size: 2rem;
            }
            
            .about-content {
                padding: 25px;
            }
            
            .value-card {
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="index.html">VelaSweets</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.html">المنتجات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="about.html">من نحن</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.html">اتصل بنا</a>
                    </li>
                </ul>
                <div class="d-flex align-items-center">
                    <div class="user-welcome ms-3" id="userWelcome">
                        <span>مرحباً، <strong id="userName">زائر</strong></span>
                    </div>
                    <a href="cart.html" class="nav-link ms-3 cart-icon">
                        <i class="bi bi-cart3 fs-5"></i>
                        <span class="cart-badge" id="cartBadge">0</span>
                    </a>
                    <div id="authButtons">
                        <a href="login.html" class="btn btn-outline-primary ms-2">تسجيل الدخول</a>
                        <a href="register.html" class="btn btn-primary">إنشاء حساب</a>
                    </div>
                    <div id="userDropdown" style="display:none;">
                        <div class="dropdown">
                            <button class="btn btn-outline-primary dropdown-toggle" type="button" id="userMenuButton" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle me-1"></i>
                                حسابي
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="profile.html">الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="orders.html">طلباتي</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" id="logoutBtn">تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <h1 class="hero-title">من نحن</h1>
            <p class="hero-subtitle">تعرف على قصة VelaSweets ورحلتنا في عالم الحلويات</p>
        </div>
    </section>
    
    <!-- About Section -->
    <section class="about-section">
        <div class="container">
            <h2 class="section-title">قصتنا</h2>
            <div class="about-content">
                <p class="about-text">
                    بدأت قصة VelaSweets من حلم بسيط: تقديم أشهى الحلويات التقليدية والعصرية لعملائنا الكرام. 
                    منذ تأسيسنا، نحن ملتزمون بتقديم منتجات عالية الجودة مصنوعة من أجود المكونات الطبيعية.
                </p>
                <p class="about-text">
                    نفخر بكوننا جزءاً من التراث الحلواني العريق، حيث نجمع بين الأصالة والحداثة في كل قطعة حلوى نقدمها. 
                    فريقنا من الطهاة المهرة يعمل بشغف لضمان أن كل منتج يحمل طعماً استثنائياً يترك ذكرى جميلة في قلوب عملائنا.
                </p>
                <p class="about-text">
                    رؤيتنا هي أن نكون الخيار الأول لمحبي الحلويات في العراق والمنطقة، ونسعى دائماً لتطوير منتجاتنا 
                    وخدماتنا لنلبي توقعات عملائنا ونتجاوزها.
                </p>
            </div>
        </div>
    </section>
    
    <!-- Values Section -->
    <section class="values-section">
        <div class="container">
            <h2 class="section-title">قيمنا</h2>
            <div class="row">
                <div class="col-md-4">
                    <div class="value-card">
                        <div class="value-icon" style="background-color: var(--primary-color);">
                            <i class="bi bi-heart-fill"></i>
                        </div>
                        <h3 class="value-title">الجودة</h3>
                        <p class="value-description">
                            نستخدم أجود المكونات الطبيعية ونتبع أعلى معايير الجودة في كل مرحلة من مراحل الإنتاج
                        </p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="value-card">
                        <div class="value-icon" style="background-color: var(--secondary-color);">
                            <i class="bi bi-people-fill"></i>
                        </div>
                        <h3 class="value-title">خدمة العملاء</h3>
                        <p class="value-description">
                            رضا عملائنا هو أولويتنا القصوى، ونسعى دائماً لتقديم تجربة تسوق مميزة ومريحة
                        </p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="value-card">
                        <div class="value-icon" style="background-color: var(--tertiary-color);">
                            <i class="bi bi-award-fill"></i>
                        </div>
                        <h3 class="value-title">التميز</h3>
                        <p class="value-description">
                            نسعى للتميز في كل ما نقدمه، من الطعم الرائع إلى التقديم الأنيق والخدمة المتميزة
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Stats Section -->
    <section class="stats-section">
        <div class="container">
            <div class="row">
                <div class="col-md-3 col-sm-6">
                    <div class="stat-item">
                        <div class="stat-number">5000+</div>
                        <div class="stat-label">عميل سعيد</div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="stat-item">
                        <div class="stat-number">50+</div>
                        <div class="stat-label">نوع حلوى</div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="stat-item">
                        <div class="stat-number">3</div>
                        <div class="stat-label">سنوات خبرة</div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="stat-item">
                        <div class="stat-number">24/7</div>
                        <div class="stat-label">خدمة العملاء</div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Team Section -->
    <section class="team-section">
        <div class="container">
            <h2 class="section-title">فريقنا</h2>
            <div class="row">
                <div class="col-md-4">
                    <div class="team-member">
                        <div class="member-photo">أ</div>
                        <h3 class="member-name">أحمد محمد</h3>
                        <p class="member-role">المدير العام</p>
                        <p class="member-description">
                            يقود فريق VelaSweets برؤية واضحة وشغف كبير لتقديم أفضل الحلويات
                        </p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="team-member">
                        <div class="member-photo">ف</div>
                        <h3 class="member-name">فاطمة علي</h3>
                        <p class="member-role">رئيسة الطهاة</p>
                        <p class="member-description">
                            خبيرة في فن الحلويات التقليدية والعصرية مع أكثر من 10 سنوات خبرة
                        </p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="team-member">
                        <div class="member-photo">م</div>
                        <h3 class="member-name">محمد حسن</h3>
                        <p class="member-role">مدير خدمة العملاء</p>
                        <p class="member-description">
                            يضمن حصول كل عميل على أفضل تجربة تسوق ممكنة مع خدمة متميزة
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h3 class="footer-title">VelaSweets</h3>
                    <p>متجر متخصص في تقديم أشهى الحلويات الشرقية والغربية المصنوعة من أجود الخامات.</p>
                </div>
                <div class="col-md-3">
                    <h4 class="footer-title">روابط سريعة</h4>
                    <ul class="footer-links">
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="products.html">المنتجات</a></li>
                        <li><a href="about.html">من نحن</a></li>
                        <li><a href="contact.html">اتصل بنا</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h4 class="footer-title">خدمة العملاء</h4>
                    <ul class="footer-links">
                        <li><a href="#">الأسئلة الشائعة</a></li>
                        <li><a href="#">سياسة الخصوصية</a></li>
                        <li><a href="#">شروط الاستخدام</a></li>
                        <li><a href="#">سياسة الإرجاع</a></li>
                    </ul>
                </div>
                <div class="col-md-2">
                    <h4 class="footer-title">تواصل معنا</h4>
                    <ul class="footer-links">
                        <li><i class="bi bi-geo-alt me-2"></i> بغداد، العراق</li>
                        <li><i class="bi bi-telephone me-2"></i> +964 123 456 789</li>
                        <li><i class="bi bi-envelope me-2"></i> <EMAIL></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>جميع الحقوق محفوظة &copy; 2025 VelaSweets</p>
            </div>
        </div>
    </footer>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="scripts/auth.js"></script>
    <script>
        let cart = JSON.parse(localStorage.getItem('cart')) || [];

        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من حالة تسجيل الدخول
            checkLoggedIn();

            // تحديث عداد السلة
            updateCartBadge();

            // إعداد مستمع حدث تسجيل الخروج
            const logoutBtn = document.getElementById('logoutBtn');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    localStorage.removeItem('customer_token');
                    localStorage.removeItem('customer_data');
                    window.location.reload();
                });
            }

            // تحريك الأرقام في قسم الإحصائيات
            animateStats();
        });

        function checkLoggedIn() {
            const token = localStorage.getItem('customer_token');
            const authButtons = document.getElementById('authButtons');
            const userDropdown = document.getElementById('userDropdown');
            const userWelcome = document.getElementById('userWelcome');
            const userName = document.getElementById('userName');

            if (!token) {
                if (authButtons) authButtons.style.display = 'block';
                if (userDropdown) userDropdown.style.display = 'none';
                if (userWelcome) userWelcome.classList.remove('logged-in');
                return;
            }

            try {
                const payload = parseJwt(token);
                const currentTime = Math.floor(Date.now() / 1000);

                if (!payload || payload.exp < currentTime) {
                    localStorage.removeItem('customer_token');
                    localStorage.removeItem('customer_data');

                    if (authButtons) authButtons.style.display = 'block';
                    if (userDropdown) userDropdown.style.display = 'none';
                    if (userWelcome) userWelcome.classList.remove('logged-in');
                    return;
                }

                if (authButtons) authButtons.style.display = 'none';
                if (userDropdown) userDropdown.style.display = 'block';

                const userData = JSON.parse(localStorage.getItem('customer_data') || '{}');
                if (userName) {
                    userName.textContent = userData.fullName || userData.name || 'العميل';
                    if (userWelcome) userWelcome.classList.add('logged-in');
                }

            } catch (error) {
                localStorage.removeItem('customer_token');
                localStorage.removeItem('customer_data');

                if (authButtons) authButtons.style.display = 'block';
                if (userDropdown) userDropdown.style.display = 'none';
                if (userWelcome) userWelcome.classList.remove('logged-in');
            }
        }

        function parseJwt(token) {
            try {
                if (!token || typeof token !== 'string') {
                    return null;
                }

                const parts = token.split('.');
                if (parts.length !== 3) {
                    return null;
                }

                const base64Payload = parts[1];
                let payload;
                try {
                    payload = atob(base64Payload);
                } catch (e) {
                    payload = decodeURIComponent(atob(base64Payload));
                }

                return JSON.parse(payload);
            } catch (error) {
                console.error('خطأ في تحليل التوكن:', error);
                return null;
            }
        }

        function updateCartBadge() {
            const badge = document.getElementById('cartBadge');
            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
            if (badge) {
                badge.textContent = totalItems;
            }
        }

        function animateStats() {
            const statNumbers = document.querySelectorAll('.stat-number');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const target = entry.target;
                        const finalValue = target.textContent;

                        // استخراج الرقم من النص
                        const numericValue = parseInt(finalValue.replace(/[^0-9]/g, ''));

                        if (!isNaN(numericValue)) {
                            animateNumber(target, 0, numericValue, finalValue);
                        }

                        observer.unobserve(target);
                    }
                });
            }, { threshold: 0.5 });

            statNumbers.forEach(stat => {
                observer.observe(stat);
            });
        }

        function animateNumber(element, start, end, finalText) {
            const duration = 2000; // مدة الحركة بالميلي ثانية
            const startTime = performance.now();

            function updateNumber(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // استخدام دالة easing للحصول على حركة أكثر سلاسة
                const easeOutQuart = 1 - Math.pow(1 - progress, 4);
                const currentValue = Math.floor(start + (end - start) * easeOutQuart);

                // تحديث النص مع الحفاظ على التنسيق الأصلي
                if (finalText.includes('+')) {
                    element.textContent = currentValue + '+';
                } else if (finalText.includes('/')) {
                    element.textContent = finalText; // للنصوص مثل "24/7"
                } else {
                    element.textContent = currentValue;
                }

                if (progress < 1) {
                    requestAnimationFrame(updateNumber);
                } else {
                    element.textContent = finalText; // التأكد من عرض القيمة النهائية الصحيحة
                }
            }

            requestAnimationFrame(updateNumber);
        }
    </script>
