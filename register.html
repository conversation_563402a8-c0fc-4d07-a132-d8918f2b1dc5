<!DOCTYPE html>
<html lang="ar" dir="rtl" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب - متجر VelaSweets</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap');
        
        :root {
            --primary-color: #5a287d;
            --secondary-color: #f8a5c2;
            --tertiary-color: #b07d28;
            --text-color: #333;
            --text-light: #fff;
            --bg-color: #f8f9fa;
            --card-bg: #fff;
            --border-color: #dee2e6;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }
        
        [data-theme="dark"] {
            --primary-color: #7e3ca3;
            --secondary-color: #ff9aba;
            --tertiary-color: #d9a440;
            --text-color: #e0e0e0;
            --text-light: #fff;
            --bg-color: #121212;
            --card-bg: #1e1e1e;
            --border-color: #2d2d2d;
            --shadow-color: rgba(0, 0, 0, 0.5);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .register-container {
            max-width: 500px;
            width: 90%;
            background-color: var(--card-bg);
            border-radius: 15px;
            box-shadow: 0 10px 30px var(--shadow-color);
            overflow: hidden;
            padding: 30px;
        }
        
        .register-logo {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .register-logo h1 {
            font-family: 'Playfair Display', serif;
            color: var(--primary-color);
            font-size: 2.5rem;
            margin-bottom: 5px;
        }
        
        .register-logo p {
            color: var(--text-color);
            opacity: 0.8;
        }
        
        .register-form h3 {
            color: var(--primary-color);
            margin-bottom: 20px;
            font-weight: 600;
            text-align: center;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: var(--bg-color);
            color: var(--text-color);
            transition: all 0.3s;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(90, 40, 125, 0.25);
            outline: none;
        }
        
        .password-field {
            position: relative;
        }
        
        .password-toggle {
            position: absolute;
            top: 50%;
            left: 15px;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--text-color);
            cursor: pointer;
            z-index: 5;
        }
        
        .password-requirements {
            font-size: 0.8rem;
            color: var(--text-color);
            opacity: 0.8;
            margin-top: 5px;
        }
        
        .form-check {
            margin: 15px 0;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            color: white;
            padding: 12px;
            border-radius: 8px;
            width: 100%;
            font-weight: 600;
            transition: all 0.3s;
            margin-top: 10px;
        }
        
        .btn-primary:hover {
            opacity: 0.9;
            transform: translateY(-2px);
        }
        
        .register-footer {
            text-align: center;
            margin-top: 20px;
            font-size: 0.9rem;
        }
        
        .theme-toggle {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
            cursor: pointer;
        }
        
        .theme-toggle i {
            margin-left: 8px;
        }
        
        .login-link {
            text-align: center;
            margin-top: 15px;
        }
        
        .login-link a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }
        
        .login-link a:hover {
            text-decoration: underline;
        }
        
        .password-strength {
            height: 5px;
            margin-top: 10px;
            border-radius: 3px;
            background-color: #e9ecef;
        }
        
        .password-strength-meter {
            height: 100%;
            border-radius: 3px;
            transition: width 0.3s ease;
        }
        
        .strength-weak {
            background-color: #dc3545;
            width: 25%;
        }
        
        .strength-medium {
            background-color: #ffc107;
            width: 50%;
        }
        
        .strength-good {
            background-color: #28a745;
            width: 75%;
        }
        
        .strength-strong {
            background-color: #198754;
            width: 100%;
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-logo">
            <h1>VelaSweets</h1>
            <p>متجر الحلويات الأفضل</p>
        </div>
        
        <div class="register-form">
            <h3>إنشاء حساب جديد</h3>
            
            <div class="alert alert-danger d-none" id="registerError"></div>
            <div class="alert alert-success d-none" id="registerSuccess"></div>
            
            <form id="registerForm">
                <div class="form-group">
                    <label for="fullName">الاسم الكامل</label>
                    <input type="text" id="fullName" class="form-control" placeholder="أدخل الاسم الكامل" required>
                </div>
                
                <div class="form-group">
                    <label for="email">البريد الإلكتروني</label>
                    <input type="email" id="email" class="form-control" placeholder="أدخل البريد الإلكتروني" required>
                </div>
                
                <div class="form-group">
                    <label for="phone">رقم الهاتف</label>
                    <input type="tel" id="phone" class="form-control" placeholder="07xxxxxxxxx" pattern="07[0-9]{9}" title="يجب أن يبدأ الرقم بـ 07 ويتكون من 11 رقماً فقط" required>
                    <small class="form-text text-muted">يجب أن يبدأ بـ 07 ويتكون من 11 رقماً</small>
                </div>
                
                <div class="form-group">
                    <label for="province">المحافظة</label>
                    <select id="province" class="form-control" required>
                        <option value="" disabled selected>اختر المحافظة</option>
                        <option value="بغداد">بغداد</option>
                        <option value="البصرة">البصرة</option>
                        <option value="نينوى">نينوى</option>
                        <option value="أربيل">أربيل</option>
                        <option value="النجف">النجف</option>
                        <option value="ذي قار">ذي قار</option>
                        <option value="كركوك">كركوك</option>
                        <option value="الأنبار">الأنبار</option>
                        <option value="ديالى">ديالى</option>
                        <option value="المثنى">المثنى</option>
                        <option value="القادسية">القادسية</option>
                        <option value="ميسان">ميسان</option>
                        <option value="واسط">واسط</option>
                        <option value="صلاح الدين">صلاح الدين</option>
                        <option value="دهوك">دهوك</option>
                        <option value="السليمانية">السليمانية</option>
                        <option value="بابل">بابل</option>
                        <option value="كربلاء">كربلاء</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="address">العنوان التفصيلي</label>
                    <textarea id="address" class="form-control" placeholder="أدخل العنوان التفصيلي" rows="2"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <div class="password-field">
                        <input type="password" id="password" class="form-control" placeholder="أدخل كلمة المرور" required>
                        <button type="button" class="password-toggle" id="togglePassword">
                            <i class="far fa-eye"></i>
                        </button>
                    </div>
                    <div class="password-strength">
                        <div class="password-strength-meter" id="passwordStrengthMeter"></div>
                    </div>
                    <div class="password-requirements">
                        كلمة المرور يجب أن تحتوي على 8 أحرف على الأقل، وتتضمن حرف كبير، حرف صغير، رقم وعلامة خاصة.
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="confirmPassword">تأكيد كلمة المرور</label>
                    <div class="password-field">
                        <input type="password" id="confirmPassword" class="form-control" placeholder="أعد إدخال كلمة المرور" required>
                        <button type="button" class="password-toggle" id="toggleConfirmPassword">
                            <i class="far fa-eye"></i>
                        </button>
                    </div>
                </div>
                
                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="agreeTerms" required>
                    <label class="form-check-label" for="agreeTerms">أوافق على <a href="#">الشروط والأحكام</a> و <a href="#">سياسة الخصوصية</a></label>
                </div>
                
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-person-plus ms-2"></i>
                    إنشاء حساب
                </button>
            </form>
            
            <div class="login-link">
                <p>لديك حساب بالفعل؟ <a href="login.html">تسجيل الدخول</a></p>
            </div>
            
            <div class="theme-toggle" id="themeToggle">
                <i class="bi bi-moon"></i>
                <span>تبديل الوضع الداكن</span>
            </div>
        </div>
        
        <div class="register-footer">
            جميع الحقوق محفوظة &copy; 2025 VelaSweets
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="scripts/customers.js"></script>
    <script src="scripts/customer-auth.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // مستمع الحدث لنموذج إنشاء الحساب
            const registerForm = document.getElementById('registerForm');
            if (registerForm) {
                registerForm.addEventListener('submit', handleRegister);
            }

            // التحقق الفوري من رقم الهاتف أثناء الكتابة
            const phoneInput = document.getElementById('phone');
            if (phoneInput) {
                phoneInput.addEventListener('input', function(e) {
                    // السماح بالأرقام فقط
                    e.target.value = e.target.value.replace(/[^\d]/g, '');

                    // التحقق من التنسيق
                    const phone = e.target.value;
                    const phoneError = document.getElementById('phoneError');

                    if (phone.length > 0) {
                        if (!phone.startsWith('07')) {
                            if (!phoneError) {
                                const errorSpan = document.createElement('small');
                                errorSpan.id = 'phoneError';
                                errorSpan.className = 'text-danger';
                                errorSpan.textContent = 'رقم الهاتف يجب أن يبدأ بـ 07';
                                e.target.parentNode.appendChild(errorSpan);
                            } else {
                                phoneError.textContent = 'رقم الهاتف يجب أن يبدأ بـ 07';
                            }
                        } else if (phone.length !== 11) {
                            if (!phoneError) {
                                const errorSpan = document.createElement('small');
                                errorSpan.id = 'phoneError';
                                errorSpan.className = 'text-danger';
                                errorSpan.textContent = 'رقم الهاتف يجب أن يتكون من 11 رقماً';
                                e.target.parentNode.appendChild(errorSpan);
                            } else {
                                phoneError.textContent = 'رقم الهاتف يجب أن يتكون من 11 رقماً';
                            }
                        } else {
                            if (phoneError) {
                                phoneError.remove();
                            }
                        }
                    } else {
                        if (phoneError) {
                            phoneError.remove();
                        }
                    }
                });

                // منع لصق النصوص غير الرقمية
                phoneInput.addEventListener('paste', function(e) {
                    e.preventDefault();
                    const paste = (e.clipboardData || window.clipboardData).getData('text');
                    const numbersOnly = paste.replace(/[^\d]/g, '');
                    e.target.value = numbersOnly;
                    e.target.dispatchEvent(new Event('input'));
                });
            }
            
            // مستمع الحدث لحقل كلمة المرور لعرض قوة كلمة المرور
            const passwordInput = document.getElementById('password');
            if (passwordInput) {
                passwordInput.addEventListener('input', checkPasswordStrength);
            }
            
            // مستمع الحدث لزر إظهار/إخفاء كلمة المرور
            const togglePassword = document.getElementById('togglePassword');
            if (togglePassword) {
                togglePassword.addEventListener('click', function() {
                    const passwordInput = document.getElementById('password');
                    const icon = this.querySelector('i');
                    
                    if (passwordInput.type === 'password') {
                        passwordInput.type = 'text';
                        icon.classList.remove('fa-eye');
                        icon.classList.add('fa-eye-slash');
                    } else {
                        passwordInput.type = 'password';
                        icon.classList.remove('fa-eye-slash');
                        icon.classList.add('fa-eye');
                    }
                });
            }
            
            // مستمع الحدث لزر إظهار/إخفاء تأكيد كلمة المرور
            const toggleConfirmPassword = document.getElementById('toggleConfirmPassword');
            if (toggleConfirmPassword) {
                toggleConfirmPassword.addEventListener('click', function() {
                    const confirmPasswordInput = document.getElementById('confirmPassword');
                    const icon = this.querySelector('i');
                    
                    if (confirmPasswordInput.type === 'password') {
                        confirmPasswordInput.type = 'text';
                        icon.classList.remove('fa-eye');
                        icon.classList.add('fa-eye-slash');
                    } else {
                        confirmPasswordInput.type = 'password';
                        icon.classList.remove('fa-eye-slash');
                        icon.classList.add('fa-eye');
                    }
                });
            }
            
            // مستمع الحدث لتبديل الوضع الداكن
            const themeToggle = document.getElementById('themeToggle');
            if (themeToggle) {
                themeToggle.addEventListener('click', function() {
                    const htmlElement = document.documentElement;
                    const currentTheme = htmlElement.getAttribute('data-theme');
                    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                    
                    htmlElement.setAttribute('data-theme', newTheme);
                    localStorage.setItem('theme', newTheme);
                    
                    // تحديث أيقونة التبديل
                    const icon = this.querySelector('i');
                    const text = this.querySelector('span');
                    
                    if (newTheme === 'dark') {
                        icon.classList.remove('bi-moon');
                        icon.classList.add('bi-sun');
                        text.textContent = 'تبديل الوضع الفاتح';
                    } else {
                        icon.classList.remove('bi-sun');
                        icon.classList.add('bi-moon');
                        text.textContent = 'تبديل الوضع الداكن';
                    }
                });
            }
            
            // تحقق من الوضع المحفوظ
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme) {
                document.documentElement.setAttribute('data-theme', savedTheme);
                
                if (savedTheme === 'dark' && themeToggle) {
                    const icon = themeToggle.querySelector('i');
                    const text = themeToggle.querySelector('span');
                    
                    icon.classList.remove('bi-moon');
                    icon.classList.add('bi-sun');
                    text.textContent = 'تبديل الوضع الفاتح';
                }
            }
            
            // التحقق من وجود توكن تسجيل دخول سابق
            checkLoggedIn();
        });
        
        // فحص قوة كلمة المرور
        function checkPasswordStrength() {
            const password = document.getElementById('password').value;
            const meter = document.getElementById('passwordStrengthMeter');
            
            // إزالة الأصناف السابقة
            meter.classList.remove('strength-weak', 'strength-medium', 'strength-good', 'strength-strong');
            
            // التحقق من قوة كلمة المرور
            if (password.length === 0) {
                meter.style.width = '0';
                return;
            }
            
            // التحقق من طول كلمة المرور
            const lengthScore = Math.min(password.length / 8, 1) * 25;
            
            // التحقق من وجود أحرف كبيرة
            const uppercaseScore = /[A-Z]/.test(password) ? 25 : 0;
            
            // التحقق من وجود أرقام
            const numberScore = /[0-9]/.test(password) ? 25 : 0;
            
            // التحقق من وجود رموز خاصة
            const specialScore = /[^A-Za-z0-9]/.test(password) ? 25 : 0;
            
            // حساب الدرجة الإجمالية
            const totalScore = lengthScore + uppercaseScore + numberScore + specialScore;
            
            // تعيين الصنف المناسب
            if (totalScore < 30) {
                meter.classList.add('strength-weak');
            } else if (totalScore < 60) {
                meter.classList.add('strength-medium');
            } else if (totalScore < 90) {
                meter.classList.add('strength-good');
            } else {
                meter.classList.add('strength-strong');
            }
        }
        
        // التحقق مما إذا كان المستخدم مسجل دخوله بالفعل
        function checkLoggedIn() {
            const token = localStorage.getItem('customer_token');
            
            // إذا لم يكن هناك توكن، فلا داعي للتحقق أكثر
            if (!token) return;
            
            // التحقق من صلاحية التوكن
            try {
                const payload = parseJwt(token);
                const currentTime = Math.floor(Date.now() / 1000);
                
                // التحقق من انتهاء صلاحية التوكن
                if (payload.exp < currentTime) {
                    // حذف التوكن المنتهي
                    localStorage.removeItem('customer_token');
                    localStorage.removeItem('customer_data');
                    return;
                }
                
                // توجيه المستخدم إلى الصفحة الرئيسية
                window.location.href = 'index.html';
            } catch (error) {
                console.error('خطأ في التحقق من التوكن:', error);
                localStorage.removeItem('customer_token');
                localStorage.removeItem('customer_data');
            }
        }
        
        // تحليل توكن JWT
        function parseJwt(token) {
            try {
                const base64Payload = token.split('.')[1];
                const payload = atob(base64Payload);
                return JSON.parse(payload);
            } catch (error) {
                console.error('خطأ في تحليل التوكن:', error);
                return null;
            }
        }
        
        // معالجة إنشاء الحساب
        function handleRegister(event) {
            event.preventDefault();
            
            // الحصول على قيم الحقول
            const fullName = document.getElementById('fullName').value;
            const email = document.getElementById('email').value;
            const phone = document.getElementById('phone').value;
            const province = document.getElementById('province').value;
            const address = document.getElementById('address').value;
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const agreeTerms = document.getElementById('agreeTerms').checked;
            
            // عرض رسائل الخطأ
            const errorDiv = document.getElementById('registerError');
            const registerBtn = document.getElementById('registerBtn');
            const registerSpinner = document.getElementById('registerSpinner');
            
            // التحقق من الموافقة على الشروط والأحكام
            if (!agreeTerms) {
                errorDiv.textContent = 'يجب الموافقة على الشروط والأحكام';
                errorDiv.classList.remove('d-none');
                return;
            }
            
            // التحقق من تطابق كلمتي المرور
            if (password !== confirmPassword) {
                errorDiv.textContent = 'كلمتا المرور غير متطابقتين';
                errorDiv.classList.remove('d-none');
                return;
            }
            
            // التحقق من رقم الهاتف - يجب أن يحتوي على أرقام فقط
            if (!/^\d+$/.test(phone)) {
                errorDiv.textContent = 'رقم الهاتف يجب أن يحتوي على أرقام فقط';
                errorDiv.classList.remove('d-none');
                registerSpinner.classList.add('d-none');
                registerBtn.removeAttribute('disabled');
                return;
            }

            // التحقق من تنسيق رقم الهاتف العراقي
            const phoneRegex = /^07\d{9}$/;
            if (!phoneRegex.test(phone)) {
                errorDiv.textContent = 'رقم الهاتف غير صحيح، يجب أن يبدأ بـ 07 ويتكون من 11 رقماً بالضبط';
                errorDiv.classList.remove('d-none');
                registerSpinner.classList.add('d-none');
                registerBtn.removeAttribute('disabled');
                return;
            }
            
            // إظهار مؤشر التحميل
            registerSpinner.classList.remove('d-none');
            registerBtn.setAttribute('disabled', 'disabled');
            
            // إخفاء رسالة الخطأ السابقة إن وجدت
            errorDiv.classList.add('d-none');
            
            // إنشاء كائن بيانات العميل
            const customerData = {
                fullName: fullName,
                email: email,
                phone: phone,
                province: province,
                address: address,
                password: password
            };
            
            // استخدام نظام المصادقة الجديد
            const result = registerCustomer({
                fullName: fullName,
                email: email,
                phone: phone,
                province: province,
                address: address,
                password: password
            });

            if (result.success) {
                // إنشاء توكن للعميل الجديد
                const token = createCustomerToken(result.customer);

                // حفظ بيانات الجلسة
                localStorage.setItem('customer_token', token);
                localStorage.setItem('customer_data', JSON.stringify({
                    id: result.customer.id,
                    fullName: result.customer.fullName,
                    email: result.customer.email,
                    phone: result.customer.phone,
                    province: result.customer.province,
                    address: result.customer.address,
                    role: result.customer.role
                }));

                // عرض رسالة نجاح
                errorDiv.textContent = result.message + '! مرحباً بك في VelaSweets';
                errorDiv.classList.remove('d-none');
                errorDiv.classList.remove('alert-danger');
                errorDiv.classList.add('alert-success');

                // إعادة التوجيه
                setTimeout(function() {
                    const urlParams = new URLSearchParams(window.location.search);
                    const redirectUrl = urlParams.get('redirect') || 'index.html';
                    window.location.href = redirectUrl;
                }, 2000);

            } else {
                // عرض رسالة الخطأ
                errorDiv.textContent = result.message;
                errorDiv.classList.remove('d-none');
                registerSpinner.classList.add('d-none');
                registerBtn.removeAttribute('disabled');
            }
        }
        
        // التحقق من صحة البريد الإلكتروني
        function validateEmail(email) {
            const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
            return re.test(String(email).toLowerCase());
        }
    </script>
</body>
</html> 