<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>متجر VelaSweets - أشهى الحلويات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap');
        
        :root {
            --primary-color: #5a287d;
            --secondary-color: #f8a5c2;
            --tertiary-color: #b07d28;
            --text-color: #333;
            --bg-color: #f8f9fa;
            --card-bg: #fff;
            --border-color: #dee2e6;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
        }
        
        .hero-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 80px 0;
            position: relative;
            overflow: hidden;
        }
        
        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('https://images.unsplash.com/photo-1514517521153-1be72277b32f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8c3dlZXRzfGVufDB8fDB8fHww&auto=format&fit=crop&w=1000&q=60') center center/cover;
            opacity: 0.2;
            z-index: 0;
        }
        
        .hero-content {
            position: relative;
            z-index: 1;
            text-align: center;
        }
        
        .hero-title {
            font-family: 'Playfair Display', serif;
            font-size: 3.5rem;
            margin-bottom: 20px;
        }
        
        .hero-subtitle {
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .btn-custom {
            background-color: var(--tertiary-color);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 30px;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .btn-custom:hover {
            background-color: white;
            color: var(--primary-color);
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        
        .featured-section {
            padding: 80px 0;
        }
        
        .section-title {
            font-family: 'Playfair Display', serif;
            color: var(--primary-color);
            margin-bottom: 50px;
            position: relative;
            display: inline-block;
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 50%;
            height: 3px;
            background-color: var(--secondary-color);
        }
        
        .product-card {
            background-color: var(--card-bg);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px var(--shadow-color);
            transition: all 0.3s;
            margin-bottom: 30px;
        }
        
        .product-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px var(--shadow-color);
        }
        
        .product-img {
            height: 200px;
            background-size: cover;
            background-position: center;
        }
        
        .product-body {
            padding: 20px;
        }
        
        .product-title {
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--primary-color);
        }
        
        .product-price {
            font-weight: 700;
            color: var(--tertiary-color);
            margin-bottom: 15px;
        }
        
        .navbar-custom {
            background-color: white;
            box-shadow: 0 2px 10px var(--shadow-color);
            padding: 15px 0;
        }
        
        .navbar-brand {
            font-family: 'Playfair Display', serif;
            color: var(--primary-color);
            font-size: 1.8rem;
            font-weight: 600;
        }
        
        .nav-link {
            color: var(--text-color);
            font-weight: 500;
            margin: 0 10px;
            transition: all 0.3s;
        }
        
        .nav-link:hover, .nav-link.active {
            color: var(--primary-color);
        }
        
        .navbar-toggler {
            border: none;
            outline: none;
        }
        
        .cart-icon {
            position: relative;
        }
        
        .cart-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background-color: var(--secondary-color);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .footer {
            background-color: var(--primary-color);
            color: white;
            padding: 50px 0 20px;
        }
        
        .footer-title {
            font-family: 'Playfair Display', serif;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }
        
        .footer-links {
            list-style: none;
            padding: 0;
        }
        
        .footer-links li {
            margin-bottom: 10px;
        }
        
        .footer-links a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .footer-links a:hover {
            color: white;
            padding-right: 5px;
        }
        
        .social-links {
            margin-top: 20px;
        }
        
        .social-links a {
            display: inline-block;
            width: 36px;
            height: 36px;
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 36px;
            margin-left: 10px;
            transition: all 0.3s;
        }
        
        .social-links a:hover {
            background-color: white;
            color: var(--primary-color);
            transform: translateY(-3px);
        }
        
        .footer-bottom {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 20px;
            margin-top: 40px;
            text-align: center;
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
        }
        
        .user-welcome {
            display: none;
        }
        
        .user-welcome.logged-in {
            display: block;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="index.html">VelaSweets</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.html" data-translate="nav.home">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.html" data-translate="nav.products">المنتجات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html" data-translate="nav.about">من نحن</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.html" data-translate="nav.contact">اتصل بنا</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="languageDropdown" role="button" data-bs-toggle="dropdown">
                            🌐 العربية
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="changeLanguage('ar')">🇮🇶 العربية</a></li>
                            <li><a class="dropdown-item" href="#" onclick="changeLanguage('ku')">🏴 کوردی</a></li>
                            <li><a class="dropdown-item" href="#" onclick="changeLanguage('en')">🇺🇸 English</a></li>
                        </ul>
                    </li>
                </ul>
                <div class="d-flex align-items-center">
                    <div class="user-welcome ms-3" id="userWelcome">
                        <span>مرحباً، <strong id="userName">زائر</strong></span>
                    </div>
                    <a href="cart.html" class="nav-link ms-3 cart-icon">
                        <i class="bi bi-cart3 fs-5"></i>
                        <span class="cart-badge">0</span>
                    </a>
                    <div id="authButtons">
                        <a href="login.html" class="btn btn-outline-primary ms-2" data-translate="nav.login">تسجيل الدخول</a>
                        <a href="register.html" class="btn btn-primary" data-translate="nav.register">إنشاء حساب</a>
                    </div>
                    <div id="userDropdown" style="display:none;">
                        <div class="dropdown">
                            <button class="btn btn-outline-primary dropdown-toggle" type="button" id="userMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-person-circle me-1"></i>
                                حسابي
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userMenuButton">
                                <li><a class="dropdown-item" href="profile.html" data-translate="nav.profile">الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="orders.html" data-translate="nav.orders">طلباتي</a></li>
                                <li><a class="dropdown-item" href="settings.html" data-translate="nav.settings">الإعدادات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" id="logoutBtn" data-translate="nav.logout">تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title" data-translate="home.welcome">أشهى الحلويات التقليدية</h1>
                <p class="hero-subtitle" data-translate="home.subtitle">استمتع بأفضل الحلويات الشرقية والغربية المصنوعة بحب وإتقان</p>
                <a href="products.html" class="btn btn-custom" data-translate="home.shop_now">تسوق الآن</a>
            </div>
        </div>
    </section>
    
    <!-- Featured Products -->
    <section class="featured-section">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="section-title" data-translate="home.featured">منتجاتنا المميزة</h2>
            </div>
            
            <div class="row">
                <div class="col-md-3 col-sm-6">
                    <div class="product-card">
                        <div class="product-img" style="background-image: url('https://images.unsplash.com/photo-1578985545062-69928b1d9587?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80');"></div>
                        <div class="product-body">
                            <h5 class="product-title">كيك الشوكولاتة</h5>
                            <p class="product-price">21,000 د.ع</p>
                            <button class="btn btn-primary w-100" data-translate="products.add_to_cart">أضف إلى السلة</button>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3 col-sm-6">
                    <div class="product-card">
                        <div class="product-img" style="background-image: url('https://images.unsplash.com/photo-1519915028121-7d3463d5b1ff?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80');"></div>
                        <div class="product-body">
                            <h5 class="product-title">بقلاوة</h5>
                            <p class="product-price">17,000 د.ع</p>
                            <button class="btn btn-primary w-100" data-translate="products.add_to_cart">أضف إلى السلة</button>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3 col-sm-6">
                    <div class="product-card">
                        <div class="product-img" style="background-image: url('https://images.unsplash.com/photo-1587314168485-3236d6710101?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80');"></div>
                        <div class="product-body">
                            <h5 class="product-title">كنافة</h5>
                            <p class="product-price">14,500 د.ع</p>
                            <button class="btn btn-primary w-100" data-translate="products.add_to_cart">أضف إلى السلة</button>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3 col-sm-6">
                    <div class="product-card">
                        <div class="product-img" style="background-image: url('https://images.unsplash.com/photo-1609541436483-f4d8304da3fb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80');"></div>
                        <div class="product-body">
                            <h5 class="product-title">كوكيز</h5>
                            <p class="product-price">12,000 د.ع</p>
                            <button class="btn btn-primary w-100" data-translate="products.add_to_cart">أضف إلى السلة</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-5">
                <a href="products.html" class="btn btn-outline-primary btn-lg">عرض جميع المنتجات</a>
            </div>
        </div>
    </section>
    
    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h3 class="footer-title">VelaSweets</h3>
                    <p>متجر متخصص في تقديم أشهى الحلويات الشرقية والغربية المصنوعة من أجود الخامات وبأيدي خبراء الحلويات.</p>
                    <div class="social-links">
                        <a href="#"><i class="bi bi-facebook"></i></a>
                        <a href="#"><i class="bi bi-instagram"></i></a>
                        <a href="#"><i class="bi bi-twitter"></i></a>
                        <a href="#"><i class="bi bi-whatsapp"></i></a>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <h4 class="footer-title">روابط سريعة</h4>
                    <ul class="footer-links">
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="products.html">المنتجات</a></li>
                        <li><a href="about.html">من نحن</a></li>
                        <li><a href="contact.html">اتصل بنا</a></li>
                    </ul>
                </div>
                
                <div class="col-md-3">
                    <h4 class="footer-title">خدمة العملاء</h4>
                    <ul class="footer-links">
                        <li><a href="#">الأسئلة الشائعة</a></li>
                        <li><a href="#">سياسة الخصوصية</a></li>
                        <li><a href="#">شروط الاستخدام</a></li>
                        <li><a href="#">سياسة الإرجاع</a></li>
                    </ul>
                </div>
                
                <div class="col-md-2">
                    <h4 class="footer-title">تواصل معنا</h4>
                    <ul class="footer-links">
                        <li><i class="bi bi-geo-alt me-2"></i> بغداد، العراق</li>
                        <li><i class="bi bi-telephone me-2"></i> +964 123 456 789</li>
                        <li><i class="bi bi-envelope me-2"></i> <EMAIL></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>جميع الحقوق محفوظة &copy; 2025 VelaSweets</p>
            </div>
        </div>
    </footer>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="database/products.js"></script>
    <script src="scripts/customer-auth.js"></script>
    <script src="scripts/currency.js"></script>
    <script src="scripts/language.js"></script>
    <script src="init-data.js"></script>
    <script src="final-setup.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من حالة تسجيل الدخول
            checkLoggedIn();
            
            // تسجيل الخروج
            const logoutBtn = document.getElementById('logoutBtn');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // حذف بيانات الجلسة
                    localStorage.removeItem('customer_token');
                    localStorage.removeItem('customer_data');
                    
                    // إعادة تحميل الصفحة
                    window.location.reload();
                });
            }
        });
        
        // تحليل توكن JWT
        function parseJwt(token) {
            try {
                if (!token || typeof token !== 'string') {
                    return null;
                }

                const parts = token.split('.');
                if (parts.length !== 3) {
                    return null;
                }

                const base64Payload = parts[1];
                // فك ترميز Base64 ثم فك ترميز URI إذا لزم الأمر
                let payload;
                try {
                    payload = atob(base64Payload);
                } catch (e) {
                    // محاولة فك ترميز URI أولاً ثم Base64
                    payload = decodeURIComponent(atob(base64Payload));
                }

                return JSON.parse(payload);
            } catch (error) {
                console.error('خطأ في تحليل التوكن:', error);
                return null;
            }
        }
        
        // التحقق من حالة تسجيل الدخول
        function checkLoggedIn() {
            const authButtons = document.getElementById('authButtons');
            const userDropdown = document.getElementById('userDropdown');
            const userWelcome = document.getElementById('userWelcome');
            const userName = document.getElementById('userName');

            // استخدام النظام الجديد للتحقق من العميل الحالي
            const currentCustomer = getCurrentCustomer();

            if (!currentCustomer) {
                // المستخدم غير مسجل الدخول
                if (authButtons) authButtons.style.display = 'block';
                if (userDropdown) userDropdown.style.display = 'none';
                if (userWelcome) userWelcome.classList.remove('logged-in');
                return;
            }

            // المستخدم مسجل الدخول
            if (authButtons) authButtons.style.display = 'none';
            if (userDropdown) userDropdown.style.display = 'block';

            // عرض اسم المستخدم
            if (userName) {
                userName.textContent = currentCustomer.fullName || 'العميل';
                if (userWelcome) userWelcome.classList.add('logged-in');
            }
        }

        // تحميل المنتجات المميزة
        function loadFeaturedProducts() {
            let featuredProducts = [];

            if (typeof getFeaturedProducts === 'function') {
                featuredProducts = getFeaturedProducts(6);
            } else if (typeof window.products !== 'undefined' && Array.isArray(window.products)) {
                featuredProducts = window.products
                    .filter(product => product.isFeatured && product.isAvailable && product.stock > 0)
                    .slice(0, 6);
            } else {
                // منتجات افتراضية
                featuredProducts = [
                    {
                        id: 1,
                        name: "كيك الشوكولاتة",
                        price: 15.99,
                        imageUrl: "https://images.unsplash.com/photo-1578985545062-69928b1d9587?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
                        description: "كيك شوكولاتة لذيذ مع كريمة الفانيليا"
                    },
                    {
                        id: 2,
                        name: "بقلاوة",
                        price: 12.99,
                        imageUrl: "https://images.unsplash.com/photo-1519915028121-7d3463d5b1ff?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
                        description: "بقلاوة تقليدية محشوة بالمكسرات والعسل"
                    },
                    {
                        id: 3,
                        name: "كنافة",
                        price: 10.99,
                        imageUrl: "https://images.unsplash.com/photo-1587314168485-3236d6710101?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
                        description: "كنافة طازجة بالجبن والقطر"
                    }
                ];
            }

            const container = document.getElementById('featuredProducts');
            if (!container) return;

            container.innerHTML = '';

            featuredProducts.forEach(product => {
                const productCard = createProductCard(product);
                container.appendChild(productCard);
            });
        }

        // إنشاء بطاقة منتج
        function createProductCard(product) {
            const col = document.createElement('div');
            col.className = 'col-md-4 col-sm-6 mb-4';

            const price = product.salePrice || product.price;
            const originalPrice = product.salePrice ? product.price : null;

            col.innerHTML = `
                <div class="product-card">
                    <div class="product-img" style="background-image: url('${product.imageUrl}');"></div>
                    <div class="product-body">
                        <h5 class="product-title">${product.name}</h5>
                        <p class="product-description">${product.description}</p>
                        <div class="product-price">
                            <span class="current-price">${formatCurrency(price)}</span>
                            ${originalPrice ? `<span class="original-price">${formatCurrency(originalPrice)}</span>` : ''}
                        </div>
                        <button class="btn btn-custom" onclick="addToCart(${product.id})" data-translate="products.add_to_cart">
                            <i class="bi bi-cart-plus me-2"></i>أضف إلى السلة
                        </button>
                    </div>
                </div>
            `;

            return col;
        }

        // إضافة منتج إلى السلة
        function addToCart(productId) {
            let cart = JSON.parse(localStorage.getItem('cart')) || [];

            // البحث عن المنتج
            let product = null;
            if (typeof getProductById === 'function') {
                product = getProductById(productId);
            } else if (typeof window.products !== 'undefined') {
                product = window.products.find(p => p.id === productId);
            }

            if (!product) {
                showToast('لم يتم العثور على المنتج', 'error');
                return;
            }

            // التحقق من وجود المنتج في السلة
            const existingItem = cart.find(item => item.id === productId);
            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                cart.push({
                    id: product.id,
                    name: product.name,
                    price: product.salePrice || product.price,
                    image: product.imageUrl,
                    quantity: 1
                });
            }

            localStorage.setItem('cart', JSON.stringify(cart));
            updateCartBadge();
            showToast('تم إضافة المنتج إلى السلة بنجاح!');
        }

        // تحديث عداد السلة
        function updateCartBadge() {
            const cart = JSON.parse(localStorage.getItem('cart')) || [];
            const badge = document.getElementById('cartBadge');
            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
            if (badge) {
                badge.textContent = totalItems;
            }
        }

        // إظهار رسالة توست
        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = 'toast-notification';
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#28a745' : '#dc3545'};
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 9999;
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            toast.textContent = message;

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 100);

            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            checkLoggedIn();
            loadFeaturedProducts();
            updateCartBadge();

            // إعداد مستمع حدث تسجيل الخروج
            const logoutBtn = document.getElementById('logoutBtn');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                        logoutCustomer();
                    }
                });
            }
        });
    </script>
</body>
</html>