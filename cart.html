<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سلة التسوق - متجر VelaSweets</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap');
        
        :root {
            --primary-color: #5a287d;
            --secondary-color: #f8a5c2;
            --tertiary-color: #b07d28;
            --text-color: #333;
            --bg-color: #f8f9fa;
            --card-bg: #fff;
            --border-color: #dee2e6;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --success-color: #28a745;
            --danger-color: #dc3545;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
        }
        
        .navbar-custom {
            background-color: white;
            box-shadow: 0 2px 10px var(--shadow-color);
            padding: 15px 0;
        }
        
        .navbar-brand {
            font-family: 'Playfair Display', serif;
            color: var(--primary-color);
            font-size: 1.8rem;
            font-weight: 600;
        }
        
        .nav-link {
            color: var(--text-color);
            font-weight: 500;
            margin: 0 10px;
            transition: all 0.3s;
        }
        
        .nav-link:hover, .nav-link.active {
            color: var(--primary-color);
        }
        
        .cart-section {
            padding: 60px 0;
            min-height: 70vh;
        }
        
        .page-title {
            font-family: 'Playfair Display', serif;
            color: var(--primary-color);
            margin-bottom: 40px;
            text-align: center;
        }
        
        .cart-item {
            background-color: var(--card-bg);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px var(--shadow-color);
            transition: all 0.3s;
        }
        
        .cart-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px var(--shadow-color);
        }
        
        .item-image {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            object-fit: cover;
        }
        
        .item-name {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 5px;
        }
        
        .item-price {
            font-weight: 700;
            color: var(--tertiary-color);
            font-size: 1.1rem;
        }
        
        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .quantity-btn {
            width: 35px;
            height: 35px;
            border: 1px solid var(--border-color);
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .quantity-btn:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .quantity-input {
            width: 60px;
            text-align: center;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            padding: 5px;
        }
        
        .remove-btn {
            background: var(--danger-color);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .remove-btn:hover {
            background: #c82333;
            transform: scale(1.05);
        }
        
        .cart-summary {
            background-color: var(--card-bg);
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px var(--shadow-color);
            position: sticky;
            top: 20px;
        }
        
        .summary-title {
            font-family: 'Playfair Display', serif;
            color: var(--primary-color);
            margin-bottom: 20px;
            font-size: 1.5rem;
        }
        
        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .summary-row:last-child {
            border-bottom: none;
            font-weight: 700;
            font-size: 1.2rem;
            color: var(--primary-color);
        }
        
        .checkout-btn {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 10px;
            width: 100%;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s;
            margin-top: 20px;
        }
        
        .checkout-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(90, 40, 125, 0.3);
        }
        
        .checkout-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .empty-cart {
            text-align: center;
            padding: 60px 20px;
        }
        
        .empty-cart-icon {
            font-size: 4rem;
            color: var(--border-color);
            margin-bottom: 20px;
        }
        
        .empty-cart h3 {
            color: var(--text-color);
            margin-bottom: 15px;
        }
        
        .empty-cart p {
            color: #6c757d;
            margin-bottom: 30px;
        }
        
        .continue-shopping {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .continue-shopping:hover {
            background-color: var(--tertiary-color);
            color: white;
            transform: translateY(-2px);
        }
        
        .cart-icon {
            position: relative;
        }
        
        .cart-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background-color: var(--secondary-color);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .user-welcome {
            display: none;
        }
        
        .user-welcome.logged-in {
            display: block;
        }
        
        .footer {
            background-color: var(--primary-color);
            color: white;
            padding: 40px 0 20px;
            margin-top: 60px;
        }
        
        .footer-title {
            font-family: 'Playfair Display', serif;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }
        
        .footer-links {
            list-style: none;
            padding: 0;
        }
        
        .footer-links li {
            margin-bottom: 8px;
        }
        
        .footer-links a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .footer-links a:hover {
            color: white;
            padding-right: 5px;
        }
        
        .footer-bottom {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 20px;
            margin-top: 30px;
            text-align: center;
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
        }
        
        @media (max-width: 768px) {
            .cart-item {
                padding: 15px;
            }
            
            .item-image {
                width: 60px;
                height: 60px;
            }
            
            .cart-summary {
                margin-top: 30px;
                position: static;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="index.html">VelaSweets</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.html">المنتجات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html">من نحن</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.html">اتصل بنا</a>
                    </li>
                </ul>
                <div class="d-flex align-items-center">
                    <div class="user-welcome ms-3" id="userWelcome">
                        <span>مرحباً، <strong id="userName">زائر</strong></span>
                    </div>
                    <a href="cart.html" class="nav-link ms-3 cart-icon">
                        <i class="bi bi-cart3 fs-5"></i>
                        <span class="cart-badge" id="cartBadge">0</span>
                    </a>
                    <div id="authButtons">
                        <a href="login.html" class="btn btn-outline-primary ms-2">تسجيل الدخول</a>
                        <a href="register.html" class="btn btn-primary">إنشاء حساب</a>
                    </div>
                    <div id="userDropdown" style="display:none;">
                        <div class="dropdown">
                            <button class="btn btn-outline-primary dropdown-toggle" type="button" id="userMenuButton" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle me-1"></i>
                                حسابي
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="profile.html">الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="orders.html">طلباتي</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" id="logoutBtn">تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- Cart Section -->
    <section class="cart-section">
        <div class="container">
            <h1 class="page-title">سلة التسوق</h1>
            
            <div id="cartContent">
                <!-- Cart content will be loaded here -->
            </div>
        </div>
    </section>
    
    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h3 class="footer-title">VelaSweets</h3>
                    <p>متجر متخصص في تقديم أشهى الحلويات الشرقية والغربية المصنوعة من أجود الخامات.</p>
                </div>
                <div class="col-md-3">
                    <h4 class="footer-title">روابط سريعة</h4>
                    <ul class="footer-links">
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="products.html">المنتجات</a></li>
                        <li><a href="about.html">من نحن</a></li>
                        <li><a href="contact.html">اتصل بنا</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h4 class="footer-title">خدمة العملاء</h4>
                    <ul class="footer-links">
                        <li><a href="#">الأسئلة الشائعة</a></li>
                        <li><a href="#">سياسة الخصوصية</a></li>
                        <li><a href="#">شروط الاستخدام</a></li>
                        <li><a href="#">سياسة الإرجاع</a></li>
                    </ul>
                </div>
                <div class="col-md-2">
                    <h4 class="footer-title">تواصل معنا</h4>
                    <ul class="footer-links">
                        <li><i class="bi bi-geo-alt me-2"></i> بغداد، العراق</li>
                        <li><i class="bi bi-telephone me-2"></i> +964 123 456 789</li>
                        <li><i class="bi bi-envelope me-2"></i> <EMAIL></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>جميع الحقوق محفوظة &copy; 2025 VelaSweets</p>
            </div>
        </div>
    </footer>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="scripts/auth.js"></script>
    <script>
        let cart = JSON.parse(localStorage.getItem('cart')) || [];

        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من حالة تسجيل الدخول
            checkLoggedIn();

            // تحميل محتوى السلة
            loadCartContent();

            // تحديث عداد السلة
            updateCartBadge();

            // إعداد مستمع حدث تسجيل الخروج
            const logoutBtn = document.getElementById('logoutBtn');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    localStorage.removeItem('customer_token');
                    localStorage.removeItem('customer_data');
                    window.location.reload();
                });
            }
        });

        function checkLoggedIn() {
            const token = localStorage.getItem('customer_token');
            const authButtons = document.getElementById('authButtons');
            const userDropdown = document.getElementById('userDropdown');
            const userWelcome = document.getElementById('userWelcome');
            const userName = document.getElementById('userName');

            if (!token) {
                if (authButtons) authButtons.style.display = 'block';
                if (userDropdown) userDropdown.style.display = 'none';
                if (userWelcome) userWelcome.classList.remove('logged-in');
                return;
            }

            try {
                const payload = parseJwt(token);
                const currentTime = Math.floor(Date.now() / 1000);

                if (!payload || payload.exp < currentTime) {
                    localStorage.removeItem('customer_token');
                    localStorage.removeItem('customer_data');

                    if (authButtons) authButtons.style.display = 'block';
                    if (userDropdown) userDropdown.style.display = 'none';
                    if (userWelcome) userWelcome.classList.remove('logged-in');
                    return;
                }

                if (authButtons) authButtons.style.display = 'none';
                if (userDropdown) userDropdown.style.display = 'block';

                const userData = JSON.parse(localStorage.getItem('customer_data') || '{}');
                if (userName) {
                    userName.textContent = userData.fullName || userData.name || 'العميل';
                    if (userWelcome) userWelcome.classList.add('logged-in');
                }

            } catch (error) {
                localStorage.removeItem('customer_token');
                localStorage.removeItem('customer_data');

                if (authButtons) authButtons.style.display = 'block';
                if (userDropdown) userDropdown.style.display = 'none';
                if (userWelcome) userWelcome.classList.remove('logged-in');
            }
        }

        function parseJwt(token) {
            try {
                if (!token || typeof token !== 'string') {
                    return null;
                }

                const parts = token.split('.');
                if (parts.length !== 3) {
                    return null;
                }

                const base64Payload = parts[1];
                let payload;
                try {
                    payload = atob(base64Payload);
                } catch (e) {
                    payload = decodeURIComponent(atob(base64Payload));
                }

                return JSON.parse(payload);
            } catch (error) {
                console.error('خطأ في تحليل التوكن:', error);
                return null;
            }
        }

        function loadCartContent() {
            const cartContent = document.getElementById('cartContent');

            if (cart.length === 0) {
                cartContent.innerHTML = `
                    <div class="empty-cart">
                        <i class="bi bi-cart-x empty-cart-icon"></i>
                        <h3>سلة التسوق فارغة</h3>
                        <p>لم تقم بإضافة أي منتجات إلى سلة التسوق بعد</p>
                        <a href="products.html" class="continue-shopping">
                            <i class="bi bi-arrow-right me-2"></i>
                            تصفح المنتجات
                        </a>
                    </div>
                `;
                return;
            }

            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const shipping = subtotal > 50 ? 0 : 5; // شحن مجاني للطلبات أكثر من $50
            const total = subtotal + shipping;

            cartContent.innerHTML = `
                <div class="row">
                    <div class="col-lg-8">
                        <div id="cartItems">
                            ${cart.map(item => createCartItemHTML(item)).join('')}
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="cart-summary">
                            <h3 class="summary-title">ملخص الطلب</h3>
                            <div class="summary-row">
                                <span>المجموع الفرعي:</span>
                                <span>$${subtotal.toFixed(2)}</span>
                            </div>
                            <div class="summary-row">
                                <span>الشحن:</span>
                                <span>${shipping === 0 ? 'مجاني' : '$' + shipping.toFixed(2)}</span>
                            </div>
                            <div class="summary-row">
                                <span>المجموع الكلي:</span>
                                <span>$${total.toFixed(2)}</span>
                            </div>
                            <button class="checkout-btn" onclick="proceedToCheckout()" ${!isLoggedIn() ? 'disabled' : ''}>
                                <i class="bi bi-credit-card me-2"></i>
                                ${!isLoggedIn() ? 'يجب تسجيل الدخول أولاً' : 'إتمام الطلب'}
                            </button>
                            ${!isLoggedIn() ? '<p class="text-center mt-2 text-muted"><small>يجب تسجيل الدخول لإتمام عملية الشراء</small></p>' : ''}
                        </div>
                    </div>
                </div>
            `;
        }

        function createCartItemHTML(item) {
            return `
                <div class="cart-item" data-id="${item.id}">
                    <div class="row align-items-center">
                        <div class="col-md-2">
                            <img src="${item.image}" alt="${item.name}" class="item-image">
                        </div>
                        <div class="col-md-4">
                            <h5 class="item-name">${item.name}</h5>
                            <p class="item-price">$${item.price}</p>
                        </div>
                        <div class="col-md-3">
                            <div class="quantity-controls">
                                <button class="quantity-btn" onclick="updateQuantity(${item.id}, ${item.quantity - 1})">
                                    <i class="bi bi-dash"></i>
                                </button>
                                <input type="number" class="quantity-input" value="${item.quantity}" min="1"
                                       onchange="updateQuantity(${item.id}, this.value)">
                                <button class="quantity-btn" onclick="updateQuantity(${item.id}, ${item.quantity + 1})">
                                    <i class="bi bi-plus"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <strong>$${(item.price * item.quantity).toFixed(2)}</strong>
                        </div>
                        <div class="col-md-1">
                            <button class="remove-btn" onclick="removeFromCart(${item.id})">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }

        function updateQuantity(productId, newQuantity) {
            newQuantity = parseInt(newQuantity);

            if (newQuantity < 1) {
                removeFromCart(productId);
                return;
            }

            const itemIndex = cart.findIndex(item => item.id === productId);
            if (itemIndex !== -1) {
                cart[itemIndex].quantity = newQuantity;
                localStorage.setItem('cart', JSON.stringify(cart));
                loadCartContent();
                updateCartBadge();
                showToast('تم تحديث الكمية بنجاح');
            }
        }

        function removeFromCart(productId) {
            cart = cart.filter(item => item.id !== productId);
            localStorage.setItem('cart', JSON.stringify(cart));
            loadCartContent();
            updateCartBadge();
            showToast('تم حذف المنتج من السلة');
        }

        function updateCartBadge() {
            const badge = document.getElementById('cartBadge');
            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
            if (badge) {
                badge.textContent = totalItems;
            }
        }

        function isLoggedIn() {
            const token = localStorage.getItem('customer_token');
            if (!token) return false;

            try {
                const payload = parseJwt(token);
                const currentTime = Math.floor(Date.now() / 1000);
                return payload && payload.exp >= currentTime;
            } catch (error) {
                return false;
            }
        }

        function proceedToCheckout() {
            if (!isLoggedIn()) {
                window.location.href = 'login.html?redirect=cart.html';
                return;
            }

            if (cart.length === 0) {
                showToast('السلة فارغة!', 'error');
                return;
            }

            // إنشاء طلب جديد
            const order = {
                id: Date.now(),
                items: [...cart],
                total: cart.reduce((sum, item) => sum + (item.price * item.quantity), 0),
                shipping: cart.reduce((sum, item) => sum + (item.price * item.quantity), 0) > 50 ? 0 : 5,
                status: 'pending',
                date: new Date().toISOString(),
                customerData: JSON.parse(localStorage.getItem('customer_data') || '{}')
            };

            // حفظ الطلب
            const orders = JSON.parse(localStorage.getItem('customer_orders') || '[]');
            orders.push(order);
            localStorage.setItem('customer_orders', JSON.stringify(orders));

            // مسح السلة
            cart = [];
            localStorage.setItem('cart', JSON.stringify(cart));

            // إظهار رسالة نجاح وإعادة توجيه
            showToast('تم إرسال طلبك بنجاح! سيتم التواصل معك قريباً', 'success');

            setTimeout(() => {
                window.location.href = 'orders.html';
            }, 2000);
        }

        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = 'toast-notification';
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? 'var(--success-color)' : 'var(--danger-color)'};
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 9999;
                transform: translateX(100%);
                transition: transform 0.3s ease;
                max-width: 300px;
            `;
            toast.textContent = message;

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 100);

            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }
    </script>
