/**
 * قاعدة بيانات المنتجات لمتجر VelaSweets
 * يحتوي على بيانات منتجات المتجر
 */

// مصفوفة المنتجات
let products = [
    {
        id: 1,
        name: "كيك الشوكولاتة",
        description: "كيك شوكولاتة لذيذ محشو بكريمة الشوكولاتة الفاخرة",
        price: 21100,
        salePrice: null,
        category: "كيك",
        tags: ["شوكولاتة", "كيك", "حلويات"],
        imageUrl: "https://images.unsplash.com/photo-1578985545062-69928b1d9587?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
        galleryImages: [
            "https://images.unsplash.com/photo-1578985545062-69928b1d9587?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
            "https://images.unsplash.com/photo-1606313564200-e75d5e30476c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80"
        ],
        stock: 20,
        isAvailable: true,
        isFeatured: true,
        rating: 4.8,
        reviewCount: 125,
        attributes: {
            weight: "1 كيلوغرام",
            size: "8 أشخاص",
            ingredients: "دقيق، سكر، زبدة، بيض، شوكولاتة، فانيليا"
        },
        createdAt: "2023-10-01T08:00:00",
        updatedAt: "2023-11-15T10:30:00"
    },
    {
        id: 2,
        name: "بقلاوة",
        description: "بقلاوة تركية تقليدية محشوة بالمكسرات والعسل",
        price: 17100,
        salePrice: 14500,
        category: "حلويات شرقية",
        tags: ["بقلاوة", "مكسرات", "حلويات شرقية"],
        imageUrl: "https://images.unsplash.com/photo-1519915028121-7d3463d5b1ff?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
        galleryImages: [
            "https://images.unsplash.com/photo-1519915028121-7d3463d5b1ff?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80"
        ],
        stock: 50,
        isAvailable: true,
        isFeatured: true,
        rating: 4.9,
        reviewCount: 200,
        attributes: {
            weight: "500 غرام",
            size: "علبة 12 قطعة",
            ingredients: "عجين فيلو، مكسرات، زبدة، سكر، ماء ورد، عسل"
        },
        createdAt: "2023-10-05T09:15:00",
        updatedAt: "2023-11-16T11:20:00"
    },
    {
        id: 3,
        name: "كنافة",
        description: "كنافة ناعمة بالجبن محشوة بالقشطة الطازجة ومغطاة بالقطر",
        price: 14500,
        salePrice: null,
        category: "حلويات شرقية",
        tags: ["كنافة", "جبن", "قشطة", "حلويات شرقية"],
        imageUrl: "https://images.unsplash.com/photo-1587314168485-3236d6710101?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
        galleryImages: [
            "https://images.unsplash.com/photo-1587314168485-3236d6710101?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80"
        ],
        stock: 30,
        isAvailable: true,
        isFeatured: true,
        rating: 4.7,
        reviewCount: 180,
        attributes: {
            weight: "1 كيلوغرام",
            size: "صينية 8 أشخاص",
            ingredients: "عجين كنافة، جبن، قشطة، سمن، قطر"
        },
        createdAt: "2023-10-10T10:30:00",
        updatedAt: "2023-11-17T09:45:00"
    },
    {
        id: 4,
        name: "كوكيز",
        description: "كوكيز بقطع الشوكولاتة الطازجة",
        price: 11900,
        salePrice: null,
        category: "بسكويت",
        tags: ["كوكيز", "شوكولاتة", "بسكويت"],
        imageUrl: "https://images.unsplash.com/photo-1609541436483-f4d8304da3fb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
        galleryImages: [
            "https://images.unsplash.com/photo-1609541436483-f4d8304da3fb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80"
        ],
        stock: 100,
        isAvailable: true,
        isFeatured: true,
        rating: 4.6,
        reviewCount: 150,
        attributes: {
            weight: "250 غرام",
            size: "12 قطعة",
            ingredients: "دقيق، سكر، زبدة، بيض، شوكولاتة، فانيليا"
        },
        createdAt: "2023-10-15T11:45:00",
        updatedAt: "2023-11-18T14:30:00"
    },
    {
        id: 5,
        name: "تشيز كيك",
        description: "تشيز كيك كريمي مع صلصة التوت الطازجة",
        price: 25100,
        salePrice: 22400,
        category: "كيك",
        tags: ["تشيز كيك", "توت", "كيك"],
        imageUrl: "https://images.unsplash.com/photo-1533134242443-d4fd215305ad?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
        galleryImages: [
            "https://images.unsplash.com/photo-1533134242443-d4fd215305ad?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80"
        ],
        stock: 15,
        isAvailable: true,
        isFeatured: false,
        rating: 4.9,
        reviewCount: 90,
        attributes: {
            weight: "1 كيلوغرام",
            size: "8 أشخاص",
            ingredients: "جبنة كريمية، بسكويت، سكر، زبدة، توت"
        },
        createdAt: "2023-10-20T09:00:00",
        updatedAt: "2023-11-19T10:15:00"
    },
    {
        id: 6,
        name: "كريب",
        description: "كريب محشو بالشوكولاتة والموز والفراولة",
        price: 13200,
        salePrice: null,
        category: "حلويات غربية",
        tags: ["كريب", "شوكولاتة", "فواكه"],
        imageUrl: "https://images.unsplash.com/photo-1600456899121-68eda5705257?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
        galleryImages: [
            "https://images.unsplash.com/photo-1600456899121-68eda5705257?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80"
        ],
        stock: 0, // نفذت الكمية
        isAvailable: false,
        isFeatured: false,
        rating: 4.5,
        reviewCount: 75,
        attributes: {
            size: "قطعتان",
            ingredients: "دقيق، بيض، حليب، شوكولاتة، موز، فراولة"
        },
        createdAt: "2023-10-25T12:30:00",
        updatedAt: "2023-11-20T15:45:00"
    }
];

/**
 * دالة للحصول على جميع المنتجات
 * @param {Object} filters - معايير التصفية (اختياري)
 * @returns {Array} - مصفوفة المنتجات
 */
function getAllProducts(filters = {}) {
    let filteredProducts = [...products];
    
    // تطبيق التصفية حسب الفئة
    if (filters.category) {
        filteredProducts = filteredProducts.filter(product => product.category === filters.category);
    }
    
    // تطبيق التصفية حسب الكلمات الدلالية
    if (filters.tag) {
        filteredProducts = filteredProducts.filter(product => product.tags.includes(filters.tag));
    }
    
    // تطبيق التصفية حسب التوفر
    if (filters.availability === true) {
        filteredProducts = filteredProducts.filter(product => product.isAvailable && product.stock > 0);
    }
    
    // تطبيق التصفية حسب المنتجات المميزة
    if (filters.featured === true) {
        filteredProducts = filteredProducts.filter(product => product.isFeatured);
    }
    
    // تطبيق البحث بالنص
    if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        filteredProducts = filteredProducts.filter(product => 
            product.name.toLowerCase().includes(searchTerm) || 
            product.description.toLowerCase().includes(searchTerm) ||
            product.tags.some(tag => tag.toLowerCase().includes(searchTerm))
        );
    }
    
    // تطبيق التصفية حسب نطاق السعر
    if (filters.minPrice !== undefined || filters.maxPrice !== undefined) {
        filteredProducts = filteredProducts.filter(product => {
            const currentPrice = product.salePrice || product.price;
            return (filters.minPrice === undefined || currentPrice >= filters.minPrice) &&
                   (filters.maxPrice === undefined || currentPrice <= filters.maxPrice);
        });
    }
    
    // تطبيق الترتيب
    if (filters.sort) {
        switch (filters.sort) {
            case 'price_asc':
                filteredProducts.sort((a, b) => (a.salePrice || a.price) - (b.salePrice || b.price));
                break;
            case 'price_desc':
                filteredProducts.sort((a, b) => (b.salePrice || b.price) - (a.salePrice || a.price));
                break;
            case 'name_asc':
                filteredProducts.sort((a, b) => a.name.localeCompare(b.name));
                break;
            case 'name_desc':
                filteredProducts.sort((a, b) => b.name.localeCompare(a.name));
                break;
            case 'newest':
                filteredProducts.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
                break;
            case 'rating':
                filteredProducts.sort((a, b) => b.rating - a.rating);
                break;
        }
    }
    
    // تطبيق الصفحات
    if (filters.page !== undefined && filters.limit !== undefined) {
        const startIndex = (filters.page - 1) * filters.limit;
        const endIndex = startIndex + filters.limit;
        filteredProducts = filteredProducts.slice(startIndex, endIndex);
    }
    
    return filteredProducts;
}

/**
 * دالة للحصول على منتج بواسطة المعرف
 * @param {number} id - معرف المنتج
 * @returns {Object|null} - كائن المنتج إذا تم العثور عليه، وإلا null
 */
function getProductById(id) {
    return products.find(product => product.id === id) || null;
}

/**
 * دالة للحصول على المنتجات المميزة
 * @param {number} limit - عدد المنتجات المطلوبة
 * @returns {Array} - مصفوفة المنتجات المميزة
 */
function getFeaturedProducts(limit = 4) {
    return products
        .filter(product => product.isFeatured && product.isAvailable && product.stock > 0)
        .sort((a, b) => b.rating - a.rating)
        .slice(0, limit);
}

/**
 * دالة للحصول على المنتجات ذات الخصومات
 * @param {number} limit - عدد المنتجات المطلوبة
 * @returns {Array} - مصفوفة المنتجات ذات الخصومات
 */
function getSaleProducts(limit = 4) {
    return products
        .filter(product => product.salePrice !== null && product.isAvailable && product.stock > 0)
        .sort((a, b) => {
            const discountA = ((product.price - product.salePrice) / product.price) * 100;
            const discountB = ((product.price - product.salePrice) / product.price) * 100;
            return discountB - discountA;
        })
        .slice(0, limit);
}

/**
 * دالة للحصول على فئات المنتجات
 * @returns {Array} - مصفوفة الفئات الفريدة
 */
function getCategories() {
    const categories = [...new Set(products.map(product => product.category))];
    return categories;
}

/**
 * دالة للحصول على الكلمات الدلالية
 * @returns {Array} - مصفوفة الكلمات الدلالية الفريدة
 */
function getTags() {
    const allTags = products.flatMap(product => product.tags);
    const uniqueTags = [...new Set(allTags)];
    return uniqueTags;
}

/**
 * دالة لإضافة منتج جديد
 * @param {Object} productData - بيانات المنتج الجديد
 * @returns {Object} - نتيجة عملية الإضافة
 */
function addProduct(productData) {
    // التحقق من وجود الحقول المطلوبة
    if (!productData.name || !productData.price || !productData.category) {
        return {
            success: false,
            message: "الاسم والسعر والفئة مطلوبة"
        };
    }
    
    // إنشاء معرف جديد
    const newId = products.length > 0 ? Math.max(...products.map(p => p.id)) + 1 : 1;
    
    // إنشاء كائن المنتج الجديد
    const newProduct = {
        id: newId,
        name: productData.name,
        description: productData.description || "",
        price: parseFloat(productData.price),
        salePrice: productData.salePrice ? parseFloat(productData.salePrice) : null,
        category: productData.category,
        tags: productData.tags || [],
        imageUrl: productData.imageUrl || "",
        galleryImages: productData.galleryImages || [],
        stock: productData.stock !== undefined ? parseInt(productData.stock) : 0,
        isAvailable: productData.isAvailable !== undefined ? productData.isAvailable : true,
        isFeatured: productData.isFeatured !== undefined ? productData.isFeatured : false,
        rating: 0,
        reviewCount: 0,
        attributes: productData.attributes || {},
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };
    
    // إضافة المنتج إلى المصفوفة
    products.push(newProduct);
    
    // حفظ البيانات في التخزين المحلي
    saveProductsData();
    
    return {
        success: true,
        message: "تمت إضافة المنتج بنجاح",
        productId: newId
    };
}

/**
 * دالة لتحديث منتج
 * @param {number} id - معرف المنتج
 * @param {Object} updatedData - البيانات المحدثة
 * @returns {Object} - نتيجة عملية التحديث
 */
function updateProduct(id, updatedData) {
    const productIndex = products.findIndex(p => p.id === id);
    if (productIndex === -1) {
        return {
            success: false,
            message: "لم يتم العثور على المنتج"
        };
    }
    
    // تحديث البيانات
    const product = products[productIndex];
    
    // معالجة الحقول الرقمية
    if (updatedData.price !== undefined) {
        updatedData.price = parseFloat(updatedData.price);
    }
    
    if (updatedData.salePrice !== undefined) {
        updatedData.salePrice = updatedData.salePrice ? parseFloat(updatedData.salePrice) : null;
    }
    
    if (updatedData.stock !== undefined) {
        updatedData.stock = parseInt(updatedData.stock);
    }
    
    // تحديث المنتج
    products[productIndex] = {
        ...product,
        ...updatedData,
        updatedAt: new Date().toISOString()
    };
    
    // حفظ البيانات في التخزين المحلي
    saveProductsData();
    
    return {
        success: true,
        message: "تم تحديث المنتج بنجاح"
    };
}

/**
 * دالة لحذف منتج
 * @param {number} id - معرف المنتج
 * @returns {Object} - نتيجة عملية الحذف
 */
function deleteProduct(id) {
    const productIndex = products.findIndex(p => p.id === id);
    if (productIndex === -1) {
        return {
            success: false,
            message: "لم يتم العثور على المنتج"
        };
    }
    
    // حذف المنتج
    products.splice(productIndex, 1);
    
    // حفظ البيانات في التخزين المحلي
    saveProductsData();
    
    return {
        success: true,
        message: "تم حذف المنتج بنجاح"
    };
}

/**
 * دالة لتحديث مخزون المنتج
 * @param {number} id - معرف المنتج
 * @param {number} quantity - الكمية المطلوب خصمها أو إضافتها
 * @param {boolean} isAddition - هل الكمية للإضافة (true) أم للخصم (false)
 * @returns {Object} - نتيجة عملية تحديث المخزون
 */
function updateStock(id, quantity, isAddition = false) {
    const productIndex = products.findIndex(p => p.id === id);
    if (productIndex === -1) {
        return {
            success: false,
            message: "لم يتم العثور على المنتج"
        };
    }
    
    const product = products[productIndex];
    
    // تحديث المخزون
    if (isAddition) {
        product.stock += quantity;
    } else {
        if (product.stock < quantity) {
            return {
                success: false,
                message: "الكمية المطلوبة غير متوفرة في المخزون"
            };
        }
        product.stock -= quantity;
    }
    
    // تحديث حالة التوفر
    product.isAvailable = product.stock > 0;
    
    // تحديث وقت التحديث
    product.updatedAt = new Date().toISOString();
    
    // حفظ البيانات في التخزين المحلي
    saveProductsData();
    
    return {
        success: true,
        message: `تم تحديث المخزون بنجاح. المخزون الحالي: ${product.stock}`,
        newStock: product.stock
    };
}

/**
 * دالة لإضافة تقييم لمنتج
 * @param {number} id - معرف المنتج
 * @param {number} rating - التقييم (1-5)
 * @returns {Object} - نتيجة عملية إضافة التقييم
 */
function addRating(id, rating) {
    const productIndex = products.findIndex(p => p.id === id);
    if (productIndex === -1) {
        return {
            success: false,
            message: "لم يتم العثور على المنتج"
        };
    }
    
    const product = products[productIndex];
    
    // التحقق من صحة التقييم
    if (rating < 1 || rating > 5) {
        return {
            success: false,
            message: "التقييم يجب أن يكون بين 1 و 5"
        };
    }
    
    // حساب التقييم الجديد
    const totalRating = product.rating * product.reviewCount + rating;
    const newReviewCount = product.reviewCount + 1;
    const newRating = parseFloat((totalRating / newReviewCount).toFixed(1));
    
    // تحديث التقييم
    product.rating = newRating;
    product.reviewCount = newReviewCount;
    product.updatedAt = new Date().toISOString();
    
    // حفظ البيانات في التخزين المحلي
    saveProductsData();
    
    return {
        success: true,
        message: "تم إضافة التقييم بنجاح",
        newRating: newRating,
        reviewCount: newReviewCount
    };
}

/**
 * دالة لحفظ بيانات المنتجات في التخزين المحلي
 */
function saveProductsData() {
    // تحويل البيانات إلى نص JSON
    const productsJSON = JSON.stringify(products);
    
    // حفظ البيانات في التخزين المحلي
    localStorage.setItem('velasweets_products', productsJSON);
}

/**
 * دالة لاسترجاع بيانات المنتجات من التخزين المحلي
 */
function loadProductsData() {
    // استرجاع البيانات من التخزين المحلي
    const productsJSON = localStorage.getItem('velasweets_products');
    
    // إذا وجدت بيانات، قم بتحديث المصفوفة
    if (productsJSON) {
        try {
            products = JSON.parse(productsJSON);
        } catch (error) {
            console.error('خطأ في تحليل بيانات المنتجات:', error);
        }
    }
}

// تحميل بيانات المنتجات عند تشغيل التطبيق
loadProductsData();

// تصدير الدوال
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        products,
        getAllProducts,
        getProductById,
        getFeaturedProducts,
        getSaleProducts,
        getCategories,
        getTags,
        addProduct,
        updateProduct,
        deleteProduct,
        updateStock,
        addRating
    };
} 