<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المنتجات - متجر VelaSweets</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap');
        
        :root {
            --primary-color: #5a287d;
            --secondary-color: #f8a5c2;
            --tertiary-color: #b07d28;
            --text-color: #333;
            --bg-color: #f8f9fa;
            --card-bg: #fff;
            --border-color: #dee2e6;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
        }
        
        .navbar-custom {
            background-color: white;
            box-shadow: 0 2px 10px var(--shadow-color);
            padding: 15px 0;
        }
        
        .navbar-brand {
            font-family: 'Playfair Display', serif;
            color: var(--primary-color);
            font-size: 1.8rem;
            font-weight: 600;
        }
        
        .nav-link {
            color: var(--text-color);
            font-weight: 500;
            margin: 0 10px;
            transition: all 0.3s;
        }
        
        .nav-link:hover, .nav-link.active {
            color: var(--primary-color);
        }
        
        .hero-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 60px 0;
            text-align: center;
        }
        
        .hero-title {
            font-family: 'Playfair Display', serif;
            font-size: 2.5rem;
            margin-bottom: 15px;
        }
        
        .products-section {
            padding: 60px 0;
        }
        
        .section-title {
            font-family: 'Playfair Display', serif;
            color: var(--primary-color);
            margin-bottom: 40px;
            text-align: center;
        }
        
        .product-card {
            background-color: var(--card-bg);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px var(--shadow-color);
            transition: all 0.3s;
            margin-bottom: 30px;
        }
        
        .product-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px var(--shadow-color);
        }
        
        .product-img {
            height: 200px;
            background-size: cover;
            background-position: center;
        }
        
        .product-body {
            padding: 20px;
        }
        
        .product-title {
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--primary-color);
        }
        
        .product-price {
            font-weight: 700;
            color: var(--tertiary-color);
            margin-bottom: 15px;
            font-size: 1.2rem;
        }
        
        .product-description {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 15px;
        }
        
        .btn-custom {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s;
            width: 100%;
        }
        
        .btn-custom:hover {
            background-color: var(--tertiary-color);
            transform: translateY(-2px);
        }
        
        .filter-section {
            background-color: var(--card-bg);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px var(--shadow-color);
        }
        
        .filter-title {
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--primary-color);
        }
        
        .form-select, .form-control {
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }
        
        .form-select:focus, .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(90, 40, 125, 0.25);
        }
        
        .cart-icon {
            position: relative;
        }
        
        .cart-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background-color: var(--secondary-color);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .user-welcome {
            display: none;
        }
        
        .user-welcome.logged-in {
            display: block;
        }
        
        .footer {
            background-color: var(--primary-color);
            color: white;
            padding: 40px 0 20px;
            margin-top: 60px;
        }
        
        .footer-title {
            font-family: 'Playfair Display', serif;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }
        
        .footer-links {
            list-style: none;
            padding: 0;
        }
        
        .footer-links li {
            margin-bottom: 8px;
        }
        
        .footer-links a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .footer-links a:hover {
            color: white;
            padding-right: 5px;
        }
        
        .footer-bottom {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 20px;
            margin-top: 30px;
            text-align: center;
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="index.html">VelaSweets</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="products.html">المنتجات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html">من نحن</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.html">اتصل بنا</a>
                    </li>
                </ul>
                <div class="d-flex align-items-center">
                    <div class="user-welcome ms-3" id="userWelcome">
                        <span>مرحباً، <strong id="userName">زائر</strong></span>
                    </div>
                    <a href="cart.html" class="nav-link ms-3 cart-icon">
                        <i class="bi bi-cart3 fs-5"></i>
                        <span class="cart-badge" id="cartBadge">0</span>
                    </a>
                    <div id="authButtons">
                        <a href="login.html" class="btn btn-outline-primary ms-2">تسجيل الدخول</a>
                        <a href="register.html" class="btn btn-primary">إنشاء حساب</a>
                    </div>
                    <div id="userDropdown" style="display:none;">
                        <div class="dropdown">
                            <button class="btn btn-outline-primary dropdown-toggle" type="button" id="userMenuButton" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle me-1"></i>
                                حسابي
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="profile.html">الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="orders.html">طلباتي</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" id="logoutBtn">تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <h1 class="hero-title">منتجاتنا المميزة</h1>
            <p>اكتشف مجموعتنا الواسعة من أشهى الحلويات الشرقية والغربية</p>
        </div>
    </section>
    
    <!-- Products Section -->
    <section class="products-section">
        <div class="container">
            <!-- Filters -->
            <div class="filter-section">
                <div class="row">
                    <div class="col-md-3">
                        <h6 class="filter-title">تصنيف حسب الفئة</h6>
                        <select class="form-select" id="categoryFilter">
                            <option value="">جميع الفئات</option>
                            <option value="كيك">كيك</option>
                            <option value="حلويات شرقية">حلويات شرقية</option>
                            <option value="بسكويت">بسكويت وكوكيز</option>
                            <option value="مشروبات">مشروبات</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <h6 class="filter-title">ترتيب حسب السعر</h6>
                        <select class="form-select" id="priceFilter">
                            <option value="">الافتراضي</option>
                            <option value="low-high">من الأقل إلى الأعلى</option>
                            <option value="high-low">من الأعلى إلى الأقل</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <h6 class="filter-title">البحث</h6>
                        <input type="text" class="form-control" id="searchInput" placeholder="ابحث عن منتج...">
                    </div>
                    <div class="col-md-3">
                        <h6 class="filter-title">عرض</h6>
                        <select class="form-select" id="viewFilter">
                            <option value="12">12 منتج</option>
                            <option value="24">24 منتج</option>
                            <option value="48">48 منتج</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- Products Grid -->
            <div class="row" id="productsGrid">
                <!-- Products will be loaded here -->
            </div>
            
            <!-- Pagination -->
            <nav aria-label="صفحات المنتجات" class="mt-4">
                <ul class="pagination justify-content-center" id="pagination">
                    <!-- Pagination will be loaded here -->
                </ul>
            </nav>
        </div>
    </section>
    
    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h3 class="footer-title">VelaSweets</h3>
                    <p>متجر متخصص في تقديم أشهى الحلويات الشرقية والغربية المصنوعة من أجود الخامات.</p>
                </div>
                <div class="col-md-3">
                    <h4 class="footer-title">روابط سريعة</h4>
                    <ul class="footer-links">
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="products.html">المنتجات</a></li>
                        <li><a href="about.html">من نحن</a></li>
                        <li><a href="contact.html">اتصل بنا</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h4 class="footer-title">خدمة العملاء</h4>
                    <ul class="footer-links">
                        <li><a href="#">الأسئلة الشائعة</a></li>
                        <li><a href="#">سياسة الخصوصية</a></li>
                        <li><a href="#">شروط الاستخدام</a></li>
                        <li><a href="#">سياسة الإرجاع</a></li>
                    </ul>
                </div>
                <div class="col-md-2">
                    <h4 class="footer-title">تواصل معنا</h4>
                    <ul class="footer-links">
                        <li><i class="bi bi-geo-alt me-2"></i> بغداد، العراق</li>
                        <li><i class="bi bi-telephone me-2"></i> +964 123 456 789</li>
                        <li><i class="bi bi-envelope me-2"></i> <EMAIL></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>جميع الحقوق محفوظة &copy; 2025 VelaSweets</p>
            </div>
        </div>
    </footer>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="database/products.js"></script>
    <script src="scripts/auth.js"></script>
    <script src="scripts/currency.js"></script>
    <script src="scripts/customer-auth.js"></script>
    <script>
        // بيانات المنتجات الافتراضية
        const defaultProducts = [
            {
                id: 1,
                name: "كيك الشوكولاتة",
                category: "كيك",
                price: 15.99,
                description: "كيك شوكولاتة لذيذ مع كريمة الفانيليا",
                image: "https://images.unsplash.com/photo-1578985545062-69928b1d9587?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
                available: true
            },
            {
                id: 2,
                name: "بقلاوة",
                category: "حلويات شرقية",
                price: 12.99,
                description: "بقلاوة تقليدية محشوة بالمكسرات والعسل",
                image: "https://images.unsplash.com/photo-1519915028121-7d3463d5b1ff?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
                available: true
            },
            {
                id: 3,
                name: "كنافة",
                category: "حلويات شرقية",
                price: 10.99,
                description: "كنافة طازجة بالجبن والقطر",
                image: "https://images.unsplash.com/photo-1587314168485-3236d6710101?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
                available: true
            },
            {
                id: 4,
                name: "كوكيز الشوكولاتة",
                category: "بسكويت",
                price: 8.99,
                description: "كوكيز مقرمش بقطع الشوكولاتة",
                image: "https://images.unsplash.com/photo-1609541436483-f4d8304da3fb?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
                available: true
            },
            {
                id: 5,
                name: "تشيز كيك الفراولة",
                category: "كيك",
                price: 18.99,
                description: "تشيز كيك كريمي بطبقة الفراولة الطازجة",
                image: "https://images.unsplash.com/photo-1565958011703-44f9829ba187?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
                available: true
            },
            {
                id: 6,
                name: "معمول التمر",
                category: "حلويات شرقية",
                price: 14.99,
                description: "معمول طازج محشو بالتمر الفاخر",
                image: "https://images.unsplash.com/photo-1571115764595-644a1f56a55c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
                available: true
            },
            {
                id: 7,
                name: "دونات مزجج",
                category: "بسكويت",
                price: 6.99,
                description: "دونات طري مع طبقة السكر المزجج",
                image: "https://images.unsplash.com/photo-1551024506-0bccd828d307?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
                available: true
            },
            {
                id: 8,
                name: "كيك الفانيليا",
                category: "كيك",
                price: 13.99,
                description: "كيك فانيليا كلاسيكي مع كريمة الزبدة",
                image: "https://images.unsplash.com/photo-1464349095431-e9a21285b5f3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
                available: true
            }
        ];

        let currentProducts = [];
        let filteredProducts = [];
        let currentPage = 1;
        let productsPerPage = 12;
        let cart = JSON.parse(localStorage.getItem('cart')) || [];

        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من حالة تسجيل الدخول
            checkLoggedIn();

            // تحميل المنتجات
            loadProducts();

            // إعداد مستمعي الأحداث
            setupEventListeners();

            // تحديث عداد السلة
            updateCartBadge();
        });

        function checkLoggedIn() {
            const token = localStorage.getItem('customer_token');
            const authButtons = document.getElementById('authButtons');
            const userDropdown = document.getElementById('userDropdown');
            const userWelcome = document.getElementById('userWelcome');
            const userName = document.getElementById('userName');

            if (!token) {
                if (authButtons) authButtons.style.display = 'block';
                if (userDropdown) userDropdown.style.display = 'none';
                if (userWelcome) userWelcome.classList.remove('logged-in');
                return;
            }

            try {
                const payload = parseJwt(token);
                const currentTime = Math.floor(Date.now() / 1000);

                if (!payload || payload.exp < currentTime) {
                    localStorage.removeItem('customer_token');
                    localStorage.removeItem('customer_data');

                    if (authButtons) authButtons.style.display = 'block';
                    if (userDropdown) userDropdown.style.display = 'none';
                    if (userWelcome) userWelcome.classList.remove('logged-in');
                    return;
                }

                if (authButtons) authButtons.style.display = 'none';
                if (userDropdown) userDropdown.style.display = 'block';

                const userData = JSON.parse(localStorage.getItem('customer_data') || '{}');
                if (userName) {
                    userName.textContent = userData.fullName || userData.name || 'العميل';
                    if (userWelcome) userWelcome.classList.add('logged-in');
                }

            } catch (error) {
                localStorage.removeItem('customer_token');
                localStorage.removeItem('customer_data');

                if (authButtons) authButtons.style.display = 'block';
                if (userDropdown) userDropdown.style.display = 'none';
                if (userWelcome) userWelcome.classList.remove('logged-in');
            }
        }

        function parseJwt(token) {
            try {
                if (!token || typeof token !== 'string') {
                    return null;
                }

                const parts = token.split('.');
                if (parts.length !== 3) {
                    return null;
                }

                const base64Payload = parts[1];
                let payload;
                try {
                    payload = atob(base64Payload);
                } catch (e) {
                    payload = decodeURIComponent(atob(base64Payload));
                }

                return JSON.parse(payload);
            } catch (error) {
                console.error('خطأ في تحليل التوكن:', error);
                return null;
            }
        }

        function loadProducts() {
            // محاولة تحميل المنتجات من قاعدة البيانات
            if (typeof getAllProducts === 'function') {
                currentProducts = getAllProducts({ availability: true });
            } else if (typeof window.products !== 'undefined' && Array.isArray(window.products)) {
                currentProducts = window.products.filter(p => p.isAvailable && p.stock > 0);
            } else {
                currentProducts = defaultProducts;
            }

            // تحويل البيانات إلى التنسيق المطلوب
            currentProducts = currentProducts.map(product => ({
                id: product.id,
                name: product.name,
                category: product.category,
                price: product.salePrice || product.price,
                description: product.description,
                image: product.imageUrl || product.image,
                available: product.isAvailable !== undefined ? product.isAvailable : product.available
            }));

            filteredProducts = [...currentProducts];
            displayProducts();
        }

        function setupEventListeners() {
            // مستمعي أحداث الفلاتر
            document.getElementById('categoryFilter').addEventListener('change', filterProducts);
            document.getElementById('priceFilter').addEventListener('change', filterProducts);
            document.getElementById('searchInput').addEventListener('input', filterProducts);
            document.getElementById('viewFilter').addEventListener('change', function() {
                productsPerPage = parseInt(this.value);
                currentPage = 1;
                displayProducts();
            });

            // مستمع حدث تسجيل الخروج
            const logoutBtn = document.getElementById('logoutBtn');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    localStorage.removeItem('customer_token');
                    localStorage.removeItem('customer_data');
                    window.location.reload();
                });
            }
        }

        function filterProducts() {
            const category = document.getElementById('categoryFilter').value;
            const priceSort = document.getElementById('priceFilter').value;
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();

            filteredProducts = currentProducts.filter(product => {
                const matchesCategory = !category || product.category === category;
                const matchesSearch = !searchTerm ||
                    product.name.toLowerCase().includes(searchTerm) ||
                    product.description.toLowerCase().includes(searchTerm);

                return matchesCategory && matchesSearch && product.available;
            });

            // ترتيب حسب السعر
            if (priceSort === 'low-high') {
                filteredProducts.sort((a, b) => a.price - b.price);
            } else if (priceSort === 'high-low') {
                filteredProducts.sort((a, b) => b.price - a.price);
            }

            currentPage = 1;
            displayProducts();
        }

        function displayProducts() {
            const grid = document.getElementById('productsGrid');
            const startIndex = (currentPage - 1) * productsPerPage;
            const endIndex = startIndex + productsPerPage;
            const productsToShow = filteredProducts.slice(startIndex, endIndex);

            grid.innerHTML = '';

            if (productsToShow.length === 0) {
                grid.innerHTML = '<div class="col-12 text-center"><p class="text-muted">لا توجد منتجات متاحة</p></div>';
                return;
            }

            productsToShow.forEach(product => {
                const productCard = createProductCard(product);
                grid.appendChild(productCard);
            });

            updatePagination();
        }

        function createProductCard(product) {
            const col = document.createElement('div');
            col.className = 'col-md-4 col-sm-6';

            col.innerHTML = `
                <div class="product-card">
                    <div class="product-img" style="background-image: url('${product.image}');"></div>
                    <div class="product-body">
                        <h5 class="product-title">${product.name}</h5>
                        <p class="product-description">${product.description}</p>
                        <p class="product-price">${formatCurrency(product.price)}</p>
                        <button class="btn btn-custom" onclick="addToCart(${product.id})">
                            <i class="bi bi-cart-plus me-2"></i>أضف إلى السلة
                        </button>
                    </div>
                </div>
            `;

            return col;
        }

        function addToCart(productId) {
            const product = currentProducts.find(p => p.id === productId);
            if (!product) return;

            const existingItem = cart.find(item => item.id === productId);
            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                cart.push({
                    id: product.id,
                    name: product.name,
                    price: product.price,
                    image: product.image,
                    quantity: 1
                });
            }

            localStorage.setItem('cart', JSON.stringify(cart));
            updateCartBadge();

            // إظهار رسالة نجاح
            showToast('تم إضافة المنتج إلى السلة بنجاح!');
        }

        function updateCartBadge() {
            const badge = document.getElementById('cartBadge');
            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
            if (badge) {
                badge.textContent = totalItems;
            }
        }

        function updatePagination() {
            const pagination = document.getElementById('pagination');
            const totalPages = Math.ceil(filteredProducts.length / productsPerPage);

            pagination.innerHTML = '';

            if (totalPages <= 1) return;

            // زر السابق
            const prevLi = document.createElement('li');
            prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
            prevLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage - 1})">السابق</a>`;
            pagination.appendChild(prevLi);

            // أرقام الصفحات
            for (let i = 1; i <= totalPages; i++) {
                const li = document.createElement('li');
                li.className = `page-item ${i === currentPage ? 'active' : ''}`;
                li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
                pagination.appendChild(li);
            }

            // زر التالي
            const nextLi = document.createElement('li');
            nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
            nextLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage + 1})">التالي</a>`;
            pagination.appendChild(nextLi);
        }

        function changePage(page) {
            const totalPages = Math.ceil(filteredProducts.length / productsPerPage);
            if (page < 1 || page > totalPages) return;

            currentPage = page;
            displayProducts();
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        function showToast(message) {
            // إنشاء عنصر التوست
            const toast = document.createElement('div');
            toast.className = 'toast-notification';
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: var(--primary-color);
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 9999;
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            toast.textContent = message;

            document.body.appendChild(toast);

            // إظهار التوست
            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 100);

            // إخفاء التوست بعد 3 ثوان
            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 3000);
        }
    </script>
