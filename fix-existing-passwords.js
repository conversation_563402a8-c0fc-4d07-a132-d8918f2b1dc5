/**
 * سكربت إصلاح كلمات المرور الموجودة
 * يقوم بتشفير كلمات المرور غير المشفرة في قاعدة البيانات
 */

(function() {
    'use strict';

    // دالة التشفير (نفس الموجودة في customer-auth.js)
    function hashPassword(password) {
        let hash = 0;
        const salt = 'VelaSweets2024';
        const combined = password + salt;
        
        for (let i = 0; i < combined.length; i++) {
            const char = combined.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        
        return Math.abs(hash).toString(16) + salt.length.toString();
    }

    // دالة للتحقق من كون كلمة المرور مشفرة أم لا
    function isPasswordHashed(password) {
        // كلمات المرور المشفرة تحتوي على أرقام وحروف hex وتنتهي بـ 14 (طول salt)
        return /^[a-f0-9]+14$/.test(password);
    }

    // إصلاح كلمات المرور للعملاء
    function fixCustomerPasswords() {
        try {
            const customers = JSON.parse(localStorage.getItem('customers') || '[]');
            let updatedCount = 0;

            customers.forEach(customer => {
                if (customer.password && !isPasswordHashed(customer.password)) {
                    console.log(`تشفير كلمة مرور العميل: ${customer.fullName}`);
                    customer.password = hashPassword(customer.password);
                    updatedCount++;
                }
            });

            if (updatedCount > 0) {
                localStorage.setItem('customers', JSON.stringify(customers));
                console.log(`✅ تم تشفير ${updatedCount} كلمة مرور للعملاء`);
            } else {
                console.log('✅ جميع كلمات مرور العملاء مشفرة بالفعل');
            }

            return updatedCount;
        } catch (error) {
            console.error('❌ خطأ في إصلاح كلمات مرور العملاء:', error);
            return 0;
        }
    }

    // إصلاح أسعار المنتجات للدينار العراقي
    function fixProductPrices() {
        try {
            // التحقق من وجود المنتجات في window.products
            if (typeof window.products !== 'undefined' && Array.isArray(window.products)) {
                let updatedCount = 0;
                const exchangeRate = 1320; // سعر صرف الدولار للدينار العراقي

                window.products.forEach(product => {
                    // إذا كان السعر أقل من 1000، فهو بالدولار ويحتاج تحويل
                    if (product.price < 1000) {
                        const oldPrice = product.price;
                        product.price = Math.round(product.price * exchangeRate);
                        
                        // تحديث سعر التخفيض إن وجد
                        if (product.salePrice && product.salePrice < 1000) {
                            product.salePrice = Math.round(product.salePrice * exchangeRate);
                        }
                        
                        console.log(`تحديث سعر ${product.name}: $${oldPrice} → ${product.price} د.ع`);
                        updatedCount++;
                    }
                });

                if (updatedCount > 0) {
                    console.log(`✅ تم تحديث ${updatedCount} سعر منتج للدينار العراقي`);
                } else {
                    console.log('✅ جميع أسعار المنتجات بالدينار العراقي بالفعل');
                }

                return updatedCount;
            } else {
                console.log('⚠️ لم يتم العثور على قاعدة بيانات المنتجات');
                return 0;
            }
        } catch (error) {
            console.error('❌ خطأ في إصلاح أسعار المنتجات:', error);
            return 0;
        }
    }

    // إصلاح إعدادات النظام
    function fixSystemSettings() {
        try {
            const settings = JSON.parse(localStorage.getItem('system_settings') || '{}');
            let updated = false;

            // تحديث العملة للدينار العراقي
            if (settings.currency !== 'IQD') {
                settings.currency = 'IQD';
                updated = true;
                console.log('تحديث عملة النظام إلى الدينار العراقي');
            }

            // تحديث رسوم الشحن
            if (settings.shippingCost < 1000) {
                settings.shippingCost = 5000; // بالدينار العراقي
                updated = true;
                console.log('تحديث رسوم الشحن إلى 5000 د.ع');
            }

            // تحديث حد الشحن المجاني
            if (settings.freeShippingThreshold < 10000) {
                settings.freeShippingThreshold = 66000; // بالدينار العراقي
                updated = true;
                console.log('تحديث حد الشحن المجاني إلى 66000 د.ع');
            }

            if (updated) {
                settings.lastUpdated = new Date().toISOString();
                localStorage.setItem('system_settings', JSON.stringify(settings));
                console.log('✅ تم تحديث إعدادات النظام');
            } else {
                console.log('✅ إعدادات النظام محدثة بالفعل');
            }

            return updated;
        } catch (error) {
            console.error('❌ خطأ في إصلاح إعدادات النظام:', error);
            return false;
        }
    }

    // تشغيل جميع الإصلاحات
    function runAllFixes() {
        console.log('🔧 بدء عملية الإصلاح الشامل...');
        
        const passwordsFixed = fixCustomerPasswords();
        const pricesFixed = fixProductPrices();
        const settingsFixed = fixSystemSettings();

        console.log('\n📊 ملخص الإصلاحات:');
        console.log(`- كلمات المرور المشفرة: ${passwordsFixed}`);
        console.log(`- أسعار المنتجات المحدثة: ${pricesFixed}`);
        console.log(`- إعدادات النظام: ${settingsFixed ? 'محدثة' : 'لا تحتاج تحديث'}`);
        
        if (passwordsFixed > 0 || pricesFixed > 0 || settingsFixed) {
            console.log('\n✅ تم إكمال الإصلاحات بنجاح!');
            console.log('🔄 يُنصح بإعادة تحميل الصفحة لتطبيق التغييرات');
        } else {
            console.log('\n✅ النظام محدث ولا يحتاج إصلاحات');
        }
    }

    // تشغيل الإصلاحات تلقائياً عند تحميل الملف
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', runAllFixes);
    } else {
        runAllFixes();
    }

    // إتاحة الدوال للاستخدام اليدوي
    window.fixCustomerPasswords = fixCustomerPasswords;
    window.fixProductPrices = fixProductPrices;
    window.fixSystemSettings = fixSystemSettings;
    window.runAllFixes = runAllFixes;

    console.log('🔧 سكربت الإصلاح الشامل جاهز');
    console.log('💡 يمكنك تشغيل الإصلاحات يدوياً باستخدام: runAllFixes()');

})();
