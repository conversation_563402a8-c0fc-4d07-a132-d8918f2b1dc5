# 🍰 VelaSweets - متجر الحلويات الإلكتروني المتكامل

متجر إلكتروني متكامل وحديث لبيع الحلويات الشرقية والغربية، مصمم خصيصاً للسوق العراقي مع دعم شامل للغات المتعددة.

## نظرة عامة
VelaSweets هو متجر إلكتروني متكامل ومتخصص في بيع الحلويات الشرقية والغربية. تم تطويره بعناية فائقة ليوفر تجربة تسوق استثنائية للعملاء مع نظام إدارة شامل ومتقدم.

## ✨ المميزات الجديدة والمحسنة

### 🔐 نظام مصادقة محسن
- **تسجيل آمن**: نظام تسجيل دخول وإنشاء حسابات محسن مع JWT
- **التحقق من البيانات**: فحص دقيق لصحة البيانات المدخلة
- **منع التكرار**: عدم قبول الحسابات المكررة بنفس البريد أو الهاتف
- **أرقام هواتف عراقية**: قبول الأرقام التي تبدأ بـ 07 وتتكون من 11 رقماً فقط

### 💰 نظام العملة الموحد
- **الدينار العراقي (IQD)**: العملة الرسمية الوحيدة في المتجر
- **رسوم شحن ذكية**: 3,000 د.ع للبصرة، 5,000 د.ع لباقي المحافظات
- **تنسيق احترافي**: عرض الأسعار بتنسيق عربي مع الفواصل

### 🌐 دعم ثلاث لغات
- **العربية** (RTL): اللغة الافتراضية
- **الكردية** (RTL): دعم كامل للغة الكردية
- **الإنجليزية** (LTR): واجهة إنجليزية متكاملة
- **تبديل تلقائي**: تغيير اتجاه الصفحة حسب اللغة المختارة

## الميزات الرئيسية

### للعملاء:
- **تصفح المنتجات**: عرض شامل لجميع أنواع الحلويات مع الصور والأوصاف
- **سلة التسوق**: إضافة وإدارة المنتجات في السلة
- **نظام المصادقة**: تسجيل الدخول وإنشاء حسابات جديدة
- **إدارة الطلبات**: تتبع حالة الطلبات وتاريخ الشراء
- **صفحة الاتصال**: إرسال الاستفسارات والشكاوى
- **الملف الشخصي**: إدارة البيانات الشخصية

### للإدارة:
- **لوحة التحكم**: إحصائيات شاملة عن المبيعات والطلبات
- **إدارة المنتجات**: إضافة وتعديل وحذف المنتجات
- **إدارة الطلبات**: متابعة وتحديث حالة الطلبات
- **إدارة العملاء**: عرض بيانات العملاء وإحصائياتهم
- **التقارير**: تقارير مفصلة عن الأداء والمبيعات

## التقنيات المستخدمة

### Frontend:
- **HTML5**: هيكل الصفحات
- **CSS3**: التصميم والتنسيق
- **JavaScript**: التفاعل والوظائف الديناميكية
- **Bootstrap 5**: إطار عمل CSS للتصميم المتجاوب
- **Bootstrap Icons**: مجموعة الأيقونات

### التخزين:
- **LocalStorage**: تخزين البيانات محلياً في المتصفح
- **JSON**: تنسيق البيانات

### المكتبات الإضافية:
- **Chart.js**: الرسوم البيانية في لوحة التحكم

## هيكل المشروع

```
VelaSweets/
├── index.html                    # الصفحة الرئيسية
├── login.html                    # صفحة تسجيل الدخول
├── register.html                 # صفحة إنشاء حساب جديد
├── products.html                 # صفحة المنتجات
├── cart.html                     # سلة التسوق
├── orders.html                   # صفحة الطلبات
├── profile.html                  # الملف الشخصي
├── about.html                    # صفحة من نحن
├── contact.html                  # صفحة اتصل بنا
├── dashboard.html                # لوحة التحكم الرئيسية
├── admin-dashboard.html          # لوحة تحكم الإدارة
├── Administrationregistration.html # تسجيل دخول الإدارة
├── database/
│   ├── products.js               # قاعدة بيانات المنتجات
│   ├── customers.js              # قاعدة بيانات العملاء
│   └── users.js                  # قاعدة بيانات المستخدمين
├── scripts/
│   ├── auth.js                   # نظام المصادقة الأساسي
│   ├── customer-auth.js          # نظام المصادقة المحسن للعملاء
│   ├── currency.js               # نظام العملة والأسعار
│   ├── language.js               # نظام اللغات المتعددة
│   └── customers.js              # وظائف العملاء
├── settings.html                 # صفحة إعدادات العميل
├── test-system.html              # صفحة اختبار النظام الأساسية
├── admin-system-test.html        # 🔧 نظام الاختبار الإداري المتقدم
├── admin-test-guide.md           # دليل استخدام نظام الاختبار الإداري
├── start.html                    # صفحة البداية والمقدمة
├── final-setup.js                # الإعداد النهائي والتحسينات
├── quality-check.md              # تقرير جودة النظام
├── init-data.js                  # البيانات الافتراضية
└── README.md                     # ملف التوثيق
```

## كيفية التشغيل

### 1. تحميل المشروع
```bash
git clone https://github.com/your-username/VelaSweets.git
cd VelaSweets
```

### 2. تشغيل الخادم المحلي
يمكنك استخدام أي خادم ويب محلي مثل:

#### باستخدام Python:
```bash
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000
```

#### باستخدام Node.js:
```bash
npx http-server
```

#### باستخدام PHP:
```bash
php -S localhost:8000
```

### 3. فتح المتصفح
افتح المتصفح وانتقل إلى: `http://localhost:8000`

### 4. اختبار النظام
للتأكد من عمل جميع الوظائف بشكل صحيح:

#### 🔧 للمديرين والمطورين:
- افتح `admin-system-test.html` - **نظام اختبار إداري متقدم**
  - فحص شامل لجميع مكونات النظام (42 اختبار)
  - واجهة إدارية احترافية مع تقارير مفصلة
  - أدوات صيانة وإعادة تهيئة
  - مراقبة مستمرة لحالة النظام

#### 📋 للاختبار العام:
- افتح `test-system.html` لتشغيل اختبارات أساسية
- أو افتح `start.html` للحصول على نظرة عامة ومقدمة

#### 📖 دليل الاستخدام:
- راجع `admin-test-guide.md` للحصول على دليل شامل لنظام الاختبار

## 🔑 حسابات تجريبية

### للعملاء:
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: 123456
- **الهاتف**: 07701234567

- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: 123456
- **الهاتف**: 07801234567

### للإدارة:
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: admin123
- **الهاتف**: 07700000000

### 📱 متطلبات أرقام الهواتف:
- يجب أن يبدأ بـ **07**
- يتكون من **11 رقماً بالضبط**
- أرقام فقط (لا حروف أو رموز)

## الوظائف الرئيسية

### نظام المصادقة:
- تسجيل دخول آمن باستخدام JWT
- إنشاء حسابات جديدة مع التحقق من البيانات
- حماية الصفحات الحساسة
- تسجيل خروج آمن

### إدارة المنتجات:
- عرض المنتجات مع الصور والأوصاف
- تصنيف المنتجات حسب الفئات
- البحث والفلترة
- إضافة المنتجات للسلة

### نظام الطلبات:
- إنشاء طلبات جديدة
- تتبع حالة الطلبات
- حساب تكلفة الشحن
- إدارة الطلبات من لوحة التحكم

### لوحة التحكم:
- إحصائيات المبيعات
- رسوم بيانية تفاعلية
- إدارة العملاء والطلبات
- تقارير مفصلة

## البيانات الافتراضية

يتم تحميل البيانات الافتراضية تلقائياً عند أول زيارة للموقع، وتشمل:
- 4 عملاء تجريبيين (3 عملاء + 1 مدير)
- 20 منتج متنوع
- 3 طلبات تجريبية
- 3 رسائل اتصال
- إعدادات النظام الأساسية

## دوال مساعدة

### إعادة تعيين البيانات:
```javascript
resetAllData(); // حذف جميع البيانات وإعادة التعيين
```

### عرض الإحصائيات:
```javascript
showDataStats(); // عرض إحصائيات البيانات الحالية
```

## المتطلبات

- متصفح ويب حديث يدعم HTML5 و CSS3 و JavaScript ES6
- خادم ويب محلي (للتطوير)
- اتصال بالإنترنت (لتحميل Bootstrap و Chart.js)

## الأمان

- تشفير كلمات المرور (يُنصح بتطبيق تشفير حقيقي في الإنتاج)
- التحقق من صحة البيانات
- حماية الصفحات الحساسة
- إدارة الجلسات بأمان

## التطوير المستقبلي

- **قاعدة بيانات حقيقية**: استبدال LocalStorage بقاعدة بيانات
- **نظام دفع**: إضافة بوابات دفع إلكترونية
- **إشعارات**: نظام إشعارات للطلبات والعروض
- **تطبيق موبايل**: تطوير تطبيق للهواتف الذكية
- **متعدد اللغات**: دعم لغات إضافية
- **تحسين الأداء**: تحسينات على السرعة والأداء

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- إنشاء Issue في GitHub
- التواصل عبر البريد الإلكتروني: <EMAIL>

## الشكر والتقدير

- Bootstrap لإطار العمل الرائع
- Unsplash للصور المجانية
- Chart.js للرسوم البيانية
- Bootstrap Icons للأيقونات

---

**VelaSweets** - حيث تلتقي الحلاوة بالتكنولوجيا 🍰✨
