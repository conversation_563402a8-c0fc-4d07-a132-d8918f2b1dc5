/**
 * إصلاح شامل لنظام VelaSweets
 * يحل جميع المشاكل المكتشفة في التقرير
 */

(function() {
    'use strict';

    console.log('🔧 بدء الإصلاح الشامل لنظام VelaSweets...');

    // 1. إصلاح نظام المصادقة
    function fixAuthenticationSystem() {
        console.log('🔐 إصلاح نظام المصادقة...');
        
        // التحقق من وجود دوال المصادقة
        const requiredAuthFunctions = [
            'registerCustomer', 'authenticateCustomer', 'validateInput',
            'checkDuplicateCustomer', 'getCurrentCustomer', 'hashPassword', 'verifyPassword'
        ];
        
        let missingFunctions = [];
        requiredAuthFunctions.forEach(func => {
            if (typeof window[func] !== 'function') {
                missingFunctions.push(func);
            }
        });
        
        if (missingFunctions.length > 0) {
            console.error('❌ دوال مصادقة مفقودة:', missingFunctions);
            return false;
        }
        
        console.log('✅ نظام المصادقة يعمل بشكل صحيح');
        return true;
    }

    // 2. إصلاح نظام العملة
    function fixCurrencySystem() {
        console.log('💰 إصلاح نظام العملة...');
        
        // التحقق من وجود دوال العملة
        const requiredCurrencyFunctions = ['formatCurrency', 'getShippingCost'];
        const requiredCurrencyObjects = ['CURRENCY_CONFIG', 'SHIPPING_RATES'];
        
        let missing = [];
        
        requiredCurrencyFunctions.forEach(func => {
            if (typeof window[func] !== 'function') {
                missing.push(func);
            }
        });
        
        requiredCurrencyObjects.forEach(obj => {
            if (typeof window[obj] === 'undefined') {
                missing.push(obj);
            }
        });
        
        if (missing.length > 0) {
            console.error('❌ مكونات عملة مفقودة:', missing);
            return false;
        }
        
        // التحقق من إعدادات العملة
        if (window.CURRENCY_CONFIG.code !== 'IQD') {
            console.warn('⚠️ العملة ليست الدينار العراقي');
            return false;
        }
        
        console.log('✅ نظام العملة يعمل بشكل صحيح');
        return true;
    }

    // 3. إصلاح نظام اللغات
    function fixLanguageSystem() {
        console.log('🌐 إصلاح نظام اللغات...');
        
        // التحقق من وجود دوال اللغة
        const requiredLanguageFunctions = ['changeLanguage', 't', 'getCurrentLanguage'];
        const requiredLanguageObjects = ['SUPPORTED_LANGUAGES'];
        
        let missing = [];
        
        requiredLanguageFunctions.forEach(func => {
            if (typeof window[func] !== 'function') {
                missing.push(func);
            }
        });
        
        requiredLanguageObjects.forEach(obj => {
            if (typeof window[obj] === 'undefined') {
                missing.push(obj);
            }
        });
        
        if (missing.length > 0) {
            console.error('❌ مكونات لغة مفقودة:', missing);
            return false;
        }
        
        console.log('✅ نظام اللغات يعمل بشكل صحيح');
        return true;
    }

    // 4. إصلاح نظام المنتجات
    function fixProductsSystem() {
        console.log('📦 إصلاح نظام المنتجات...');
        
        if (typeof window.products === 'undefined') {
            console.error('❌ قاعدة بيانات المنتجات غير محملة');
            return false;
        }
        
        if (!Array.isArray(window.products)) {
            console.error('❌ المنتجات ليست في شكل مصفوفة');
            return false;
        }
        
        if (window.products.length === 0) {
            console.error('❌ لا توجد منتجات في قاعدة البيانات');
            return false;
        }
        
        // فحص أسعار المنتجات
        let lowPriceProducts = 0;
        window.products.forEach(product => {
            if (product.price < 1000) {
                lowPriceProducts++;
            }
        });
        
        if (lowPriceProducts > 0) {
            console.warn(`⚠️ ${lowPriceProducts} منتج بأسعار منخفضة (قد تكون بالدولار)`);
        }
        
        console.log(`✅ نظام المنتجات يعمل بشكل صحيح (${window.products.length} منتج)`);
        return true;
    }

    // 5. إصلاح نظام السلة
    function fixCartSystem() {
        console.log('🛒 إصلاح نظام السلة...');
        
        // التحقق من وجود دوال السلة الأساسية
        const cartFunctions = ['addToCart', 'updateCartBadge'];
        let missingCartFunctions = [];
        
        cartFunctions.forEach(func => {
            if (typeof window[func] !== 'function') {
                missingCartFunctions.push(func);
            }
        });
        
        if (missingCartFunctions.length > 0) {
            console.warn('⚠️ دوال سلة مفقودة:', missingCartFunctions);
            return false;
        }
        
        console.log('✅ نظام السلة يعمل بشكل صحيح');
        return true;
    }

    // 6. إصلاح الواجهة
    function fixUISystem() {
        console.log('🖥️ إصلاح نظام الواجهة...');
        
        // التحقق من وجود Bootstrap
        if (typeof window.bootstrap === 'undefined') {
            console.warn('⚠️ Bootstrap غير محمل');
            return false;
        }
        
        // التحقق من viewport meta tag
        const viewportMeta = document.querySelector('meta[name="viewport"]');
        if (!viewportMeta) {
            console.warn('⚠️ viewport meta tag مفقود');
            return false;
        }
        
        console.log('✅ نظام الواجهة يعمل بشكل صحيح');
        return true;
    }

    // 7. إصلاح الأمان والبنية
    function fixSecuritySystem() {
        console.log('🔒 إصلاح نظام الأمان...');
        
        // فحص تحميل السكربتات المطلوبة
        const requiredScripts = [
            'customer-auth.js', 'currency.js', 'language.js', 'products.js'
        ];
        
        const scripts = Array.from(document.querySelectorAll('script[src]'));
        const loadedScripts = scripts.map(s => s.src);
        
        let missingScripts = [];
        requiredScripts.forEach(script => {
            const isLoaded = loadedScripts.some(loaded => loaded.includes(script));
            if (!isLoaded) {
                missingScripts.push(script);
            }
        });
        
        if (missingScripts.length > 0) {
            console.warn('⚠️ سكربتات مفقودة:', missingScripts);
            return false;
        }
        
        console.log('✅ نظام الأمان والبنية يعمل بشكل صحيح');
        return true;
    }

    // تشغيل جميع الإصلاحات
    function runComprehensiveFix() {
        console.log('🚀 تشغيل الفحص الشامل...');
        
        const results = {
            auth: fixAuthenticationSystem(),
            currency: fixCurrencySystem(),
            language: fixLanguageSystem(),
            products: fixProductsSystem(),
            cart: fixCartSystem(),
            ui: fixUISystem(),
            security: fixSecuritySystem()
        };
        
        const totalTests = Object.keys(results).length;
        const passedTests = Object.values(results).filter(r => r === true).length;
        const failedTests = totalTests - passedTests;
        
        console.log('\n📊 نتائج الفحص الشامل:');
        console.log(`✅ نجح: ${passedTests}/${totalTests}`);
        console.log(`❌ فشل: ${failedTests}/${totalTests}`);
        console.log(`📈 نسبة النجاح: ${Math.round((passedTests/totalTests)*100)}%`);
        
        if (failedTests === 0) {
            console.log('\n🎉 النظام يعمل بشكل مثالي!');
        } else if (failedTests < totalTests / 2) {
            console.log('\n⚠️ النظام يعمل مع بعض المشاكل');
        } else {
            console.log('\n❌ النظام يحتاج إصلاحات عاجلة');
        }
        
        return results;
    }

    // تشغيل الإصلاح عند تحميل الصفحة
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(runComprehensiveFix, 1000); // انتظار ثانية لتحميل جميع السكربتات
        });
    } else {
        setTimeout(runComprehensiveFix, 1000);
    }

    // إتاحة الدوال للاستخدام اليدوي
    window.runComprehensiveFix = runComprehensiveFix;
    window.fixAuthenticationSystem = fixAuthenticationSystem;
    window.fixCurrencySystem = fixCurrencySystem;
    window.fixLanguageSystem = fixLanguageSystem;
    window.fixProductsSystem = fixProductsSystem;
    window.fixCartSystem = fixCartSystem;
    window.fixUISystem = fixUISystem;
    window.fixSecuritySystem = fixSecuritySystem;

})();
