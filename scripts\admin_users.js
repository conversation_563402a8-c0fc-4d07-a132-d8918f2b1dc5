/**
 * إدارة مستخدمي لوحة التحكم لمتجر VelaSweets
 * يوفر واجهة مبسطة للتعامل مع المستخدمين وإدارة عمليات تسجيل الدخول
 */

// قائمة المستخدمين المثبتة مسبقًا
const defaultAdminUsers = [
    {
        id: 1,
        name: "المدير",
        email: "<EMAIL>",
        password: "Admin123!",
        role: "admin",
        permissions: ["all"]
    },
    {
        id: 2,
        name: "موظف1",
        email: "<EMAIL>",
        password: "2025",
        role: "employee",
        permissions: ["view_orders", "edit_orders", "view_products"]
    },
    {
        id: 3,
        name: "موظف 2",
        email: "<EMAIL>",
        password: "2025",
        role: "employee",
        permissions: ["view_orders", "view_products", "edit_products"]
    },
    {
        id: 4,
        name: "موظف 3",
        email: "<EMAIL>",
        password: "2025",
        role: "employee",
        permissions: ["view_customers", "view_orders", "view_statistics"]
    }
];

/**
 * دالة للتحقق من بيانات تسجيل الدخول
 * @param {string} email - البريد الإلكتروني
 * @param {string} password - كلمة المرور
 * @returns {Object|null} - بيانات المستخدم إذا كانت المعلومات صحيحة، وإلا null
 */
function loginAdmin(email, password) {
    console.log("محاولة تسجيل دخول المدير:", email);
    
    // محاولة استخدام قاعدة البيانات الرئيسية إذا كانت متاحة
    if (typeof window.authenticateUser === 'function') {
        console.log("استخدام authenticateUser من قاعدة البيانات");
        return window.authenticateUser(email, password);
    }
    
    // استخدام القائمة المحلية كاحتياطي
    console.log("استخدام القائمة الاحتياطية المحلية للتحقق");
    const user = defaultAdminUsers.find(u => 
        u.email.toLowerCase() === email.toLowerCase() && 
        u.password === password
    );
    
    if (user) {
        console.log("تم العثور على المستخدم:", user.name);
        
        // إرجاع نسخة من بيانات المستخدم بدون كلمة المرور
        const { password, ...userData } = user;
        return userData;
    }
    
    console.log("لم يتم العثور على المستخدم");
    return null;
}

/**
 * دالة لحفظ بيانات المستخدم بعد تسجيل الدخول
 * @param {Object} user - بيانات المستخدم
 * @param {boolean} rememberMe - تذكر المستخدم
 * @returns {string} - توكن المصادقة
 */
function saveAdminSession(user, rememberMe = false) {
    console.log("حفظ جلسة المستخدم:", user.email);
    
    // إنشاء توكن JWT بسيط
    const header = {
        alg: 'HS256',
        typ: 'JWT'
    };
    
    const payload = {
        sub: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + (rememberMe ? 30 * 24 * 60 * 60 : 24 * 60 * 60)
    };
    
    try {
        // تحويل الرأس والحمولة إلى سلاسل مشفرة بـ Base64 مع معالجة الأحرف غير اللاتينية
        const encodedHeader = btoa(encodeURIComponent(JSON.stringify(header)));
        const encodedPayload = btoa(encodeURIComponent(JSON.stringify(payload)));
        
        // التوقيع (في الإنتاج، يتم ذلك باستخدام مفتاح سري آمن)
        const signature = btoa(encodeURIComponent(`${encodedHeader}.${encodedPayload}`));
        
        // إنشاء التوكن الكامل
        const token = `${encodedHeader}.${encodedPayload}.${signature}`;
        
        // حفظ التوكن وبيانات المستخدم
        localStorage.setItem('auth_token', token);
        localStorage.setItem('user_data', JSON.stringify(user));
        
        // حفظ في الكوكيز أيضًا كبديل
        const maxAge = rememberMe ? 30 * 24 * 60 * 60 : 24 * 60 * 60;
        document.cookie = `user_logged_in=true; path=/; max-age=${maxAge}`;
        document.cookie = `admin_user=${user.email}; path=/; max-age=${maxAge}`;
        document.cookie = `admin_role=${user.role}; path=/; max-age=${maxAge}`;
        document.cookie = `auth_active=true; path=/; max-age=${maxAge}`;
        
        console.log("تم حفظ بيانات الجلسة بنجاح");
        return token;
    } catch (error) {
        console.error("خطأ في حفظ بيانات الجلسة:", error);
        throw error;
    }
}

/**
 * دالة للتحقق من حالة تسجيل الدخول
 * @returns {boolean} - هل المستخدم مسجل الدخول؟
 */
function isAdminLoggedIn() {
    try {
        // التحقق من وجود توكن المصادقة في localStorage
        const token = localStorage.getItem('auth_token');
        const userData = localStorage.getItem('user_data');
        
        if (token && userData) {
            console.log("تم العثور على بيانات المستخدم في localStorage");
            return true;
        }
        
        // التحقق من الكوكيز كبديل
        const cookies = document.cookie.split(';').map(cookie => cookie.trim());
        const isLoggedInCookie = cookies.find(cookie => cookie.startsWith('user_logged_in=true'));
        const adminUserCookie = cookies.find(cookie => cookie.startsWith('admin_user='));
        
        if (isLoggedInCookie || adminUserCookie) {
            console.log("تم العثور على بيانات المستخدم في الكوكيز");
            return true;
        }
        
        console.log("المستخدم غير مسجل الدخول");
        return false;
    } catch (error) {
        console.error("خطأ في التحقق من حالة تسجيل الدخول:", error);
        return false;
    }
}

/**
 * دالة لفك توكن JWT
 * @param {string} token - توكن JWT للفك
 * @returns {Object} - كائن يحتوي على بيانات التوكن
 */
function parseAdminJwt(token) {
    try {
        // تقسيم التوكن إلى أجزائه الثلاثة
        const parts = token.split('.');
        if (parts.length !== 3) {
            throw new Error("توكن غير صالح: يجب أن يحتوي على ثلاثة أجزاء");
        }
        
        // فك تشفير الجزء الثاني (الحمولة)
        const base64Payload = parts[1];
        // فك ترميز Base64 ثم فك ترميز URI
        const payload = decodeURIComponent(atob(base64Payload));
        
        // تحويل السلسلة إلى كائن JSON
        return JSON.parse(payload);
    } catch (error) {
        console.error("خطأ في فك التوكن:", error);
        throw new Error("فشل في فك التوكن: " + error.message);
    }
}

/**
 * دالة للتحقق من صلاحية التوكن
 * @returns {Object|null} - بيانات المستخدم إذا كان التوكن صالحًا، وإلا null
 */
function validateAdminToken() {
    try {
        const token = localStorage.getItem('auth_token');
        if (!token) return null;
        
        const payload = parseAdminJwt(token);
        const currentTime = Math.floor(Date.now() / 1000);
        
        // التحقق من انتهاء صلاحية التوكن
        if (payload.exp && payload.exp < currentTime) {
            console.log("التوكن منتهي الصلاحية");
            return null;
        }
        
        return payload;
    } catch (error) {
        console.error("خطأ في التحقق من صلاحية التوكن:", error);
        return null;
    }
}

/**
 * دالة لتسجيل خروج المستخدم
 */
function logoutAdmin() {
    console.log("تنفيذ تسجيل الخروج");
    
    try {
        // حذف بيانات localStorage
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_data');
        
        // حذف الكوكيز
        const expires = 'Thu, 01 Jan 1970 00:00:01 GMT';
        document.cookie = `user_logged_in=; path=/; expires=${expires}`;
        document.cookie = `admin_user=; path=/; expires=${expires}`;
        document.cookie = `admin_role=; path=/; expires=${expires}`;
        document.cookie = `auth_active=; path=/; expires=${expires}`;
        
        console.log("تم تسجيل الخروج بنجاح");
    } catch (error) {
        console.error("خطأ في تسجيل الخروج:", error);
    }
}

// تصدير الدوال إلى النافذة العامة
window.loginAdmin = loginAdmin;
window.saveAdminSession = saveAdminSession;
window.isAdminLoggedIn = isAdminLoggedIn;
window.logoutAdmin = logoutAdmin;
window.defaultAdminUsers = defaultAdminUsers;
window.parseAdminJwt = parseAdminJwt;
window.validateAdminToken = validateAdminToken; 