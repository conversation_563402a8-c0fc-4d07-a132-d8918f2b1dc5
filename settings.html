<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات - VelaSweets</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
        
        :root {
            --primary-color: #5a287d;
            --secondary-color: #f8a5c2;
            --tertiary-color: #b07d28;
            --text-color: #333;
            --bg-color: #f8f9fa;
            --card-bg: #fff;
            --border-color: #dee2e6;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            padding-top: 80px;
        }
        
        .settings-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .settings-card {
            background: var(--card-bg);
            border-radius: 15px;
            box-shadow: 0 5px 15px var(--shadow-color);
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .settings-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .settings-body {
            padding: 30px;
        }
        
        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid var(--border-color);
        }
        
        .setting-item:last-child {
            border-bottom: none;
        }
        
        .setting-info {
            flex: 1;
        }
        
        .setting-title {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .setting-description {
            font-size: 0.9rem;
            color: #666;
        }
        
        .setting-control {
            margin-right: 20px;
        }
        
        .language-selector {
            min-width: 150px;
        }
        
        .currency-display {
            background: var(--primary-color);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: 600;
        }
        
        .btn-custom {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border: none;
            padding: 10px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .btn-custom:hover {
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(90, 40, 125, 0.3);
        }
        
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: var(--primary-color);
        }
        
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        
        .back-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            box-shadow: 0 4px 12px var(--shadow-color);
            transition: all 0.3s;
            z-index: 1000;
        }
        
        .back-btn:hover {
            background: var(--secondary-color);
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <!-- زر العودة -->
    <button class="back-btn" onclick="history.back()">
        <i class="bi bi-arrow-right"></i>
    </button>
    
    <div class="settings-container">
        <!-- رأس الإعدادات -->
        <div class="settings-card">
            <div class="settings-header">
                <h1><i class="bi bi-gear me-2"></i>الإعدادات</h1>
                <p class="mb-0">تخصيص تجربتك في VelaSweets</p>
            </div>
        </div>
        
        <!-- إعدادات اللغة -->
        <div class="settings-card">
            <div class="settings-body">
                <h3 class="mb-4"><i class="bi bi-translate me-2"></i>اللغة والمنطقة</h3>
                
                <div class="setting-item">
                    <div class="setting-info">
                        <div class="setting-title">لغة الواجهة</div>
                        <div class="setting-description">اختر اللغة المفضلة لعرض الموقع</div>
                    </div>
                    <div class="setting-control">
                        <select class="form-select language-selector" id="languageSelect">
                            <option value="ar">العربية</option>
                            <option value="ku">کوردی</option>
                            <option value="en">English</option>
                        </select>
                    </div>
                </div>
                
                <div class="setting-item">
                    <div class="setting-info">
                        <div class="setting-title">العملة</div>
                        <div class="setting-description">العملة المستخدمة في المتجر</div>
                    </div>
                    <div class="setting-control">
                        <span class="currency-display">دينار عراقي (IQD)</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- إعدادات الإشعارات -->
        <div class="settings-card">
            <div class="settings-body">
                <h3 class="mb-4"><i class="bi bi-bell me-2"></i>الإشعارات</h3>
                
                <div class="setting-item">
                    <div class="setting-info">
                        <div class="setting-title">إشعارات الطلبات</div>
                        <div class="setting-description">تلقي إشعارات حول حالة طلباتك</div>
                    </div>
                    <div class="setting-control">
                        <label class="switch">
                            <input type="checkbox" id="orderNotifications" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>
                
                <div class="setting-item">
                    <div class="setting-info">
                        <div class="setting-title">إشعارات العروض</div>
                        <div class="setting-description">تلقي إشعارات حول العروض والخصومات الجديدة</div>
                    </div>
                    <div class="setting-control">
                        <label class="switch">
                            <input type="checkbox" id="promotionNotifications" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- إعدادات الخصوصية -->
        <div class="settings-card">
            <div class="settings-body">
                <h3 class="mb-4"><i class="bi bi-shield-check me-2"></i>الخصوصية والأمان</h3>
                
                <div class="setting-item">
                    <div class="setting-info">
                        <div class="setting-title">حفظ بيانات تسجيل الدخول</div>
                        <div class="setting-description">تذكر بيانات تسجيل الدخول على هذا الجهاز</div>
                    </div>
                    <div class="setting-control">
                        <label class="switch">
                            <input type="checkbox" id="rememberLogin">
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>
                
                <div class="setting-item">
                    <div class="setting-info">
                        <div class="setting-title">مشاركة البيانات</div>
                        <div class="setting-description">السماح بمشاركة البيانات لتحسين الخدمة</div>
                    </div>
                    <div class="setting-control">
                        <label class="switch">
                            <input type="checkbox" id="dataSharing">
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- إعدادات التطبيق -->
        <div class="settings-card">
            <div class="settings-body">
                <h3 class="mb-4"><i class="bi bi-palette me-2"></i>المظهر</h3>
                
                <div class="setting-item">
                    <div class="setting-info">
                        <div class="setting-title">الوضع المظلم</div>
                        <div class="setting-description">تفعيل الوضع المظلم للواجهة</div>
                    </div>
                    <div class="setting-control">
                        <label class="switch">
                            <input type="checkbox" id="darkMode">
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- أزرار الإجراءات -->
        <div class="settings-card">
            <div class="settings-body text-center">
                <button class="btn btn-custom me-3" onclick="saveSettings()">
                    <i class="bi bi-check-lg me-2"></i>حفظ الإعدادات
                </button>
                <button class="btn btn-outline-secondary" onclick="resetSettings()">
                    <i class="bi bi-arrow-clockwise me-2"></i>إعادة تعيين
                </button>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="scripts/customer-auth.js"></script>
    <script src="scripts/language.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من تسجيل الدخول
            const currentCustomer = getCurrentCustomer();
            if (!currentCustomer) {
                alert('يجب تسجيل الدخول أولاً');
                window.location.href = 'login.html?redirect=' + encodeURIComponent(window.location.pathname);
                return;
            }

            // تحميل الإعدادات المحفوظة
            loadSettings();

            // إعداد مستمعي الأحداث
            setupEventListeners();
        });

        function loadSettings() {
            const settings = JSON.parse(localStorage.getItem('user_settings') || '{}');

            // تحميل إعدادات اللغة
            const languageSelect = document.getElementById('languageSelect');
            if (settings.language) {
                languageSelect.value = settings.language;
            }

            // تحميل إعدادات الإشعارات
            document.getElementById('orderNotifications').checked = settings.orderNotifications !== false;
            document.getElementById('promotionNotifications').checked = settings.promotionNotifications !== false;

            // تحميل إعدادات الخصوصية
            document.getElementById('rememberLogin').checked = settings.rememberLogin === true;
            document.getElementById('dataSharing').checked = settings.dataSharing === true;

            // تحميل إعدادات المظهر
            document.getElementById('darkMode').checked = settings.darkMode === true;

            // تطبيق الوضع المظلم إذا كان مفعلاً
            if (settings.darkMode) {
                document.body.classList.add('dark-mode');
            }
        }

        function setupEventListeners() {
            // مستمع تغيير اللغة
            document.getElementById('languageSelect').addEventListener('change', function() {
                const selectedLanguage = this.value;
                if (typeof changeLanguage === 'function') {
                    changeLanguage(selectedLanguage);
                }
            });

            // مستمع الوضع المظلم
            document.getElementById('darkMode').addEventListener('change', function() {
                if (this.checked) {
                    document.body.classList.add('dark-mode');
                } else {
                    document.body.classList.remove('dark-mode');
                }
            });
        }

        function saveSettings() {
            const settings = {
                language: document.getElementById('languageSelect').value,
                orderNotifications: document.getElementById('orderNotifications').checked,
                promotionNotifications: document.getElementById('promotionNotifications').checked,
                rememberLogin: document.getElementById('rememberLogin').checked,
                dataSharing: document.getElementById('dataSharing').checked,
                darkMode: document.getElementById('darkMode').checked,
                lastUpdated: new Date().toISOString()
            };

            localStorage.setItem('user_settings', JSON.stringify(settings));

            // عرض رسالة نجاح
            showToast('تم حفظ الإعدادات بنجاح!', 'success');
        }

        function resetSettings() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
                localStorage.removeItem('user_settings');

                // إعادة تحميل الصفحة لتطبيق الإعدادات الافتراضية
                window.location.reload();
            }
        }

        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = 'toast-notification';
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                left: 20px;
                background: ${type === 'success' ? '#28a745' : '#dc3545'};
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 9999;
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            `;
            toast.textContent = message;

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 100);

            setTimeout(() => {
                toast.style.transform = 'translateX(-100%)';
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
