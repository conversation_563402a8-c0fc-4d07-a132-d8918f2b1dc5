/**
 * إدارة العملاء لمتجر VelaSweets
 * هذا الملف يتعامل مع تسجيل العملاء الجدد والتحقق من بيانات تسجيل الدخول
 */

// قائمة العملاء المسجلين في الذاكرة
let registeredCustomers = [];

/**
 * دالة لتسجيل عميل جديد
 * @param {Object} customerData - بيانات العميل المراد تسجيله
 * @returns {Object} - نتيجة عملية التسجيل
 */
function registerCustomer(customerData) {
    console.log("تسجيل عميل جديد:", customerData.email);
    
    try {
        // تحقق من وجود ملف قاعدة البيانات
        if (typeof window.isEmailExists === 'function') {
            // استخدام دالة قاعدة البيانات للتسجيل
            console.log("استخدام قاعدة البيانات للتسجيل");
            return window.registerCustomer(customerData);
        } else {
            console.log("استخدام التسجيل المحلي");
            // التحقق من البريد الإلكتروني
            if (isEmailExistsLocally(customerData.email)) {
                console.log("البريد الإلكتروني مستخدم بالفعل:", customerData.email);
                return {
                    success: false,
                    message: "البريد الإلكتروني مستخدم بالفعل"
                };
            }
            
            // إنشاء معرف جديد
            const newId = registeredCustomers.length > 0 
                ? Math.max(...registeredCustomers.map(c => c.id)) + 1 
                : 1;
            
            // تنسيق بيانات العميل الجديد
            const newCustomer = {
                id: newId,
                fullName: customerData.fullName,
                email: customerData.email.toLowerCase(),
                phone: customerData.phone,
                password: customerData.password,
                createdAt: new Date().toISOString(),
                lastLogin: null,
                status: "active"
            };
            
            // إضافة العميل إلى القائمة
            registeredCustomers.push(newCustomer);
            
            // حفظ بيانات العملاء في التخزين المحلي
            saveCustomersToLocalStorage();
            
            console.log("تم تسجيل العميل بنجاح:", newCustomer.email);
            
            return {
                success: true,
                message: "تم تسجيل العميل بنجاح",
                customerId: newId
            };
        }
    } catch (error) {
        console.error("خطأ في تسجيل العميل:", error);
        return {
            success: false,
            message: "حدث خطأ أثناء تسجيل العميل"
        };
    }
}

/**
 * دالة للتحقق من بيانات تسجيل الدخول للعميل
 * @param {string} email - البريد الإلكتروني
 * @param {string} password - كلمة المرور
 * @returns {Object|null} - بيانات العميل إذا كانت المعلومات صحيحة، وإلا null
 */
function authenticateCustomer(email, password) {
    console.log("محاولة تسجيل دخول العميل:", email);
    
    try {
        // تحقق من وجود ملف قاعدة البيانات
        if (typeof window.authenticateCustomer === 'function' && window.authenticateCustomer !== authenticateCustomer) {
            // استخدام دالة قاعدة البيانات للتحقق
            console.log("استخدام قاعدة البيانات للتحقق");
            return window.authenticateCustomer(email, password);
        } else {
            console.log("استخدام التحقق المحلي");
            
            // تحميل بيانات العملاء من التخزين المحلي
            loadCustomersFromLocalStorage();
            
            // البحث عن العميل
            const customer = registeredCustomers.find(
                c => c.email.toLowerCase() === email.toLowerCase() && c.password === password
            );
            
            if (customer) {
                console.log("تم تسجيل دخول العميل بنجاح:", customer.email);
                
                // تحديث وقت آخر تسجيل دخول
                customer.lastLogin = new Date().toISOString();
                saveCustomersToLocalStorage();
                
                // إرجاع نسخة من بيانات العميل بدون كلمة المرور
                const { password, ...customerData } = customer;
                return customerData;
            }
            
            console.log("فشل تسجيل الدخول: بيانات غير صحيحة");
            return null;
        }
    } catch (error) {
        console.error("خطأ في التحقق من بيانات العميل:", error);
        return null;
    }
}

/**
 * دالة للتحقق محليًا من وجود عميل بالبريد الإلكتروني
 * @param {string} email - البريد الإلكتروني للعميل
 * @returns {boolean} - هل يوجد عميل بهذا البريد الإلكتروني؟
 */
function isEmailExistsLocally(email) {
    // تحميل بيانات العملاء من التخزين المحلي
    loadCustomersFromLocalStorage();
    
    return registeredCustomers.some(
        customer => customer.email.toLowerCase() === email.toLowerCase()
    );
}

/**
 * دالة لحفظ بيانات العملاء في التخزين المحلي
 */
function saveCustomersToLocalStorage() {
    try {
        localStorage.setItem('registered_customers', JSON.stringify(registeredCustomers));
        console.log("تم حفظ بيانات العملاء في التخزين المحلي");
    } catch (error) {
        console.error("خطأ في حفظ بيانات العملاء:", error);
    }
}

/**
 * دالة لتحميل بيانات العملاء من التخزين المحلي
 */
function loadCustomersFromLocalStorage() {
    try {
        const storedData = localStorage.getItem('registered_customers');
        if (storedData) {
            registeredCustomers = JSON.parse(storedData);
            console.log(`تم تحميل ${registeredCustomers.length} عميل من التخزين المحلي`);
        }
    } catch (error) {
        console.error("خطأ في تحميل بيانات العملاء:", error);
        registeredCustomers = [];
    }
}

// تحميل بيانات العملاء عند تضمين الملف
loadCustomersFromLocalStorage();

// تصدير الدوال إلى النافذة العامة
window.registerCustomer = registerCustomer;
window.authenticateCustomer = authenticateCustomer;
window.isEmailExistsLocally = isEmailExistsLocally; 