<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم الإدارة - VelaSweets</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
        
        :root {
            --primary-color: #5a287d;
            --secondary-color: #f8a5c2;
            --tertiary-color: #b07d28;
            --text-color: #333;
            --bg-color: #f8f9fa;
            --card-bg: #fff;
            --border-color: #dee2e6;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
        }
        
        .admin-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        
        .admin-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .admin-subtitle {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: var(--card-bg);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px var(--shadow-color);
            transition: all 0.3s;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px var(--shadow-color);
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--primary-color);
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 15px;
        }
        
        .stat-title {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 5px;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        
        .stat-change {
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .stat-change.positive {
            color: var(--success-color);
        }
        
        .stat-change.negative {
            color: var(--danger-color);
        }
        
        .quick-actions {
            background: var(--card-bg);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px var(--shadow-color);
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .action-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .action-btn {
            background: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: 10px;
            padding: 20px;
            text-decoration: none;
            color: var(--text-color);
            transition: all 0.3s;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }
        
        .action-btn:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px var(--shadow-color);
        }
        
        .action-icon {
            font-size: 2rem;
            color: var(--primary-color);
        }
        
        .action-text {
            font-weight: 600;
            text-align: center;
        }
        
        .recent-activity {
            background: var(--card-bg);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px var(--shadow-color);
        }
        
        .activity-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px 0;
            border-bottom: 1px solid var(--border-color);
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            color: white;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-title {
            font-weight: 600;
            margin-bottom: 2px;
        }
        
        .activity-time {
            font-size: 0.8rem;
            color: #666;
        }
        
        .logout-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: var(--danger-color);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 10px 20px;
            font-weight: 600;
            transition: all 0.3s;
            z-index: 1000;
        }
        
        .logout-btn:hover {
            background: #c82333;
            transform: scale(1.05);
        }
        
        .back-to-site {
            position: fixed;
            top: 20px;
            left: 150px;
            background: var(--info-color);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 10px 20px;
            font-weight: 600;
            transition: all 0.3s;
            z-index: 1000;
            text-decoration: none;
        }
        
        .back-to-site:hover {
            background: #138496;
            color: white;
            transform: scale(1.05);
        }
        
        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .action-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .logout-btn, .back-to-site {
                position: static;
                margin: 10px;
                display: inline-block;
            }
        }
    </style>
</head>
<body>
    <!-- Header Buttons -->
    <button class="logout-btn" id="logoutBtn">
        <i class="bi bi-box-arrow-left me-2"></i>تسجيل الخروج
    </button>
    <a href="index.html" class="back-to-site">
        <i class="bi bi-house me-2"></i>العودة للموقع
    </a>
    
    <!-- Admin Header -->
    <div class="admin-header">
        <div class="container">
            <h1 class="admin-title">لوحة تحكم الإدارة</h1>
            <p class="admin-subtitle">مرحباً بك في نظام إدارة متجر VelaSweets</p>
        </div>
    </div>
    
    <div class="container">
        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon" style="background: var(--success-color);">
                    <i class="bi bi-currency-dollar"></i>
                </div>
                <div class="stat-title">إجمالي المبيعات</div>
                <div class="stat-value" id="totalSales">$0</div>
                <div class="stat-change positive">
                    <i class="bi bi-arrow-up"></i>
                    <span>+12% من الشهر الماضي</span>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon" style="background: var(--info-color);">
                    <i class="bi bi-cart-check"></i>
                </div>
                <div class="stat-title">إجمالي الطلبات</div>
                <div class="stat-value" id="totalOrders">0</div>
                <div class="stat-change positive">
                    <i class="bi bi-arrow-up"></i>
                    <span>+8% من الشهر الماضي</span>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon" style="background: var(--warning-color);">
                    <i class="bi bi-people"></i>
                </div>
                <div class="stat-title">إجمالي العملاء</div>
                <div class="stat-value" id="totalCustomers">0</div>
                <div class="stat-change positive">
                    <i class="bi bi-arrow-up"></i>
                    <span>+15% من الشهر الماضي</span>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon" style="background: var(--primary-color);">
                    <i class="bi bi-box-seam"></i>
                </div>
                <div class="stat-title">إجمالي المنتجات</div>
                <div class="stat-value" id="totalProducts">0</div>
                <div class="stat-change positive">
                    <i class="bi bi-arrow-up"></i>
                    <span>+5% من الشهر الماضي</span>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="quick-actions">
            <h2 class="section-title">
                <i class="bi bi-lightning-charge"></i>
                إجراءات سريعة
            </h2>
            <div class="action-grid">
                <a href="dashboard.html" class="action-btn">
                    <i class="bi bi-speedometer2 action-icon"></i>
                    <span class="action-text">لوحة التحكم الرئيسية</span>
                </a>
                <a href="products.html" class="action-btn">
                    <i class="bi bi-plus-circle action-icon"></i>
                    <span class="action-text">إضافة منتج جديد</span>
                </a>
                <a href="orders.html" class="action-btn">
                    <i class="bi bi-list-check action-icon"></i>
                    <span class="action-text">إدارة الطلبات</span>
                </a>
                <a href="#" class="action-btn" onclick="viewCustomers()">
                    <i class="bi bi-people action-icon"></i>
                    <span class="action-text">إدارة العملاء</span>
                </a>
                <a href="#" class="action-btn" onclick="viewReports()">
                    <i class="bi bi-bar-chart action-icon"></i>
                    <span class="action-text">التقارير والإحصائيات</span>
                </a>
                <a href="#" class="action-btn" onclick="viewSettings()">
                    <i class="bi bi-gear action-icon"></i>
                    <span class="action-text">إعدادات النظام</span>
                </a>
            </div>
        </div>
        
        <!-- Recent Activity -->
        <div class="recent-activity">
            <h2 class="section-title">
                <i class="bi bi-clock-history"></i>
                النشاط الأخير
            </h2>
            <div id="recentActivity">
                <!-- Activity items will be loaded here -->
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="database/products.js"></script>
    <script src="database/customers.js"></script>
    <script src="scripts/auth.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من صلاحيات الإدارة
            checkAdminAccess();

            // تحميل الإحصائيات
            loadStatistics();

            // تحميل النشاط الأخير
            loadRecentActivity();

            // إعداد مستمع حدث تسجيل الخروج
            document.getElementById('logoutBtn').addEventListener('click', handleLogout);
        });

        function checkAdminAccess() {
            // التحقق من تسجيل الدخول كمدير
            const token = localStorage.getItem('auth_token') || localStorage.getItem('customer_token');
            const userData = JSON.parse(localStorage.getItem('user_data') || localStorage.getItem('customer_data') || '{}');

            if (!token || (!userData.role || userData.role !== 'admin')) {
                // إذا لم يكن مديراً، توجيه إلى صفحة تسجيل الدخول
                alert('يجب أن تكون مديراً للوصول إلى هذه الصفحة');
                window.location.href = 'Administrationregistration.html';
                return;
            }
        }

        function loadStatistics() {
            // حساب إحصائيات المنتجات
            let totalProducts = 0;
            if (typeof getAllProducts === 'function') {
                totalProducts = getAllProducts().length;
            } else if (typeof window.products !== 'undefined') {
                totalProducts = window.products.length;
            }
            document.getElementById('totalProducts').textContent = totalProducts;

            // حساب إحصائيات العملاء
            let totalCustomers = 0;
            const customers = JSON.parse(localStorage.getItem('customers') || '[]');
            totalCustomers = customers.length;
            document.getElementById('totalCustomers').textContent = totalCustomers;

            // حساب إحصائيات الطلبات
            let totalOrders = 0;
            let totalSales = 0;

            // جمع الطلبات من جميع العملاء
            customers.forEach(customer => {
                const customerOrders = JSON.parse(localStorage.getItem(`customer_orders_${customer.id}`) || '[]');
                totalOrders += customerOrders.length;

                customerOrders.forEach(order => {
                    if (order.status === 'completed') {
                        totalSales += order.total + (order.shipping || 0);
                    }
                });
            });

            // إضافة الطلبات العامة
            const generalOrders = JSON.parse(localStorage.getItem('customer_orders') || '[]');
            totalOrders += generalOrders.length;

            generalOrders.forEach(order => {
                if (order.status === 'completed') {
                    totalSales += order.total + (order.shipping || 0);
                }
            });

            document.getElementById('totalOrders').textContent = totalOrders;
            document.getElementById('totalSales').textContent = '$' + totalSales.toFixed(2);
        }

        function loadRecentActivity() {
            const activityContainer = document.getElementById('recentActivity');
            const activities = [];

            // إضافة أنشطة افتراضية
            activities.push({
                icon: 'bi-cart-plus',
                iconBg: 'var(--success-color)',
                title: 'طلب جديد تم استلامه',
                time: 'منذ 5 دقائق'
            });

            activities.push({
                icon: 'bi-person-plus',
                iconBg: 'var(--info-color)',
                title: 'عميل جديد انضم للموقع',
                time: 'منذ 15 دقيقة'
            });

            activities.push({
                icon: 'bi-box-seam',
                iconBg: 'var(--warning-color)',
                title: 'تم تحديث مخزون المنتجات',
                time: 'منذ 30 دقيقة'
            });

            activities.push({
                icon: 'bi-star',
                iconBg: 'var(--primary-color)',
                title: 'تقييم جديد للمنتج',
                time: 'منذ ساعة'
            });

            activities.push({
                icon: 'bi-credit-card',
                iconBg: 'var(--success-color)',
                title: 'تم تأكيد دفعة جديدة',
                time: 'منذ ساعتين'
            });

            activityContainer.innerHTML = activities.map(activity => `
                <div class="activity-item">
                    <div class="activity-icon" style="background: ${activity.iconBg};">
                        <i class="${activity.icon}"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">${activity.title}</div>
                        <div class="activity-time">${activity.time}</div>
                    </div>
                </div>
            `).join('');
        }

        function viewCustomers() {
            const customers = JSON.parse(localStorage.getItem('customers') || '[]');

            if (customers.length === 0) {
                alert('لا يوجد عملاء مسجلين حالياً');
                return;
            }

            let customersList = 'قائمة العملاء:\n\n';
            customers.forEach((customer, index) => {
                customersList += `${index + 1}. ${customer.fullName || customer.name} - ${customer.email}\n`;
            });

            alert(customersList);
        }

        function viewReports() {
            const customers = JSON.parse(localStorage.getItem('customers') || '[]');
            const generalOrders = JSON.parse(localStorage.getItem('customer_orders') || '[]');

            let totalOrders = generalOrders.length;
            let completedOrders = generalOrders.filter(order => order.status === 'completed').length;
            let pendingOrders = generalOrders.filter(order => order.status === 'pending').length;
            let totalRevenue = generalOrders
                .filter(order => order.status === 'completed')
                .reduce((sum, order) => sum + order.total + (order.shipping || 0), 0);

            const report = `تقرير المبيعات:

إجمالي الطلبات: ${totalOrders}
الطلبات المكتملة: ${completedOrders}
الطلبات المعلقة: ${pendingOrders}
إجمالي الإيرادات: $${totalRevenue.toFixed(2)}
عدد العملاء: ${customers.length}

معدل إتمام الطلبات: ${totalOrders > 0 ? ((completedOrders / totalOrders) * 100).toFixed(1) : 0}%`;

            alert(report);
        }

        function viewSettings() {
            const settings = `إعدادات النظام:

- إدارة المنتجات
- إدارة الفئات
- إعدادات الدفع
- إعدادات الشحن
- إعدادات الإشعارات
- إعدادات الأمان

هذه الميزات ستكون متاحة في التحديثات القادمة.`;

            alert(settings);
        }

        function handleLogout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                // حذف جميع بيانات الجلسة
                localStorage.removeItem('auth_token');
                localStorage.removeItem('user_data');
                localStorage.removeItem('customer_token');
                localStorage.removeItem('customer_data');

                // حذف الكوكيز
                document.cookie.split(";").forEach(function(c) {
                    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
                });

                // إعادة التوجيه
                window.location.href = 'index.html';
            }
        }

        // تحديث الإحصائيات كل 30 ثانية
        setInterval(loadStatistics, 30000);
    </script>
</body>
</html>
