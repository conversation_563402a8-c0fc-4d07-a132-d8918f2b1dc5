/**
 * قاعدة بيانات المستخدمين لمتجر VelaSweets
 * يحتوي على بيانات المستخدمين المصرح لهم بالدخول إلى لوحة الإدارة
 */

// مصفوفة المستخدمين المسموح لهم بالدخول
let users = [
    {
        id: 1,
        name: "المدير",
        email: "<EMAIL>",
        password: "Admin123!",
        role: "admin",
        permissions: ["all"], // جميع الصلاحيات
        lastLogin: null
    },
    {
        id: 2,
        name: "موظف1",
        email: "<EMAIL>",
        password: "2025",
        role: "employee",
        permissions: ["view_orders", "edit_orders", "view_products"],
        lastLogin: null
    },
    {
        id: 3,
        name: "موظف 2",
        email: "<EMAIL>",
        password: "2025",
        role: "employee",
        permissions: ["view_orders", "view_products", "edit_products"],
        lastLogin: null
    },
    {
        id: 4,
        name: "موظف 3",
        email: "<EMAIL>",
        password: "2025",
        role: "employee",
        permissions: ["view_customers", "view_orders", "view_statistics"],
        lastLogin: null
    }
];

/**
 * دالة للتحقق من صحة بيانات تسجيل الدخول
 * @param {string} email - البريد الإلكتروني للمستخدم
 * @param {string} password - كلمة المرور
 * @returns {Object|null} - كائن المستخدم إذا كانت البيانات صحيحة، وإلا null
 */
function authenticateUser(email, password) {
    console.log("محاولة تسجيل دخول:", email);
    
    const user = users.find(u => u.email === email && u.password === password);
    if (user) {
        console.log("تم العثور على المستخدم:", user.name);
        // تحديث وقت آخر تسجيل دخول
        user.lastLogin = new Date().toISOString();
        
        // حفظ التغييرات
        saveUsersData();
        
        return {
            id: user.id,
            name: user.name,
            email: user.email,
            role: user.role,
            permissions: user.permissions
        };
    }
    console.log("لم يتم العثور على المستخدم بهذه البيانات");
    return null;
}

/**
 * دالة للبحث عن مستخدم بواسطة المعرف
 * @param {number} id - معرف المستخدم
 * @returns {Object|null} - كائن المستخدم إذا تم العثور عليه، وإلا null
 */
function getUserById(id) {
    const user = users.find(u => u.id === id);
    if (user) {
        return {
            id: user.id,
            name: user.name,
            email: user.email,
            role: user.role,
            permissions: user.permissions,
            lastLogin: user.lastLogin
        };
    }
    return null;
}

/**
 * دالة للتحقق من صلاحيات المستخدم
 * @param {number} userId - معرف المستخدم
 * @param {string} permission - الصلاحية المطلوب التحقق منها
 * @returns {boolean} - true إذا كان لدى المستخدم الصلاحية، وإلا false
 */
function hasPermission(userId, permission) {
    const user = getUserById(userId);
    if (!user) return false;
    
    // المدير لديه جميع الصلاحيات
    if (user.role === "admin" || user.permissions.includes("all")) {
        return true;
    }
    
    // التحقق من وجود الصلاحية المحددة
    return user.permissions.includes(permission);
}

/**
 * دالة لإضافة مستخدم جديد
 * @param {Object} userData - بيانات المستخدم الجديد
 * @returns {Object} - كائن المستخدم الجديد
 */
function addUser(userData) {
    // التحقق من عدم وجود مستخدم بنفس البريد الإلكتروني
    const existingUser = users.find(u => u.email === userData.email);
    if (existingUser) {
        throw new Error("البريد الإلكتروني مستخدم بالفعل");
    }
    
    // إنشاء معرف جديد
    const newId = users.length > 0 ? Math.max(...users.map(u => u.id)) + 1 : 1;
    
    // إنشاء كائن المستخدم الجديد
    const newUser = {
        id: newId,
        name: userData.name,
        email: userData.email,
        password: userData.password, // في الإنتاج، يجب تشفير كلمة المرور
        role: userData.role || "employee",
        permissions: userData.permissions || [],
        lastLogin: null
    };
    
    // إضافة المستخدم إلى المصفوفة
    users.push(newUser);
    
    // حفظ التغييرات
    saveUsersData();
    
    // إرجاع نسخة من المستخدم بدون كلمة المرور
    const { password, ...userWithoutPassword } = newUser;
    return userWithoutPassword;
}

/**
 * دالة للتحقق من وجود مستخدم بالبريد الإلكتروني
 * @param {string} email - البريد الإلكتروني
 * @returns {boolean} - هل البريد الإلكتروني موجود
 */
function isEmailInUse(email) {
    return users.some(user => user.email.toLowerCase() === email.toLowerCase());
}

/**
 * دالة لتحديث بيانات مستخدم
 * @param {number} id - معرف المستخدم
 * @param {Object} updatedData - البيانات المحدثة
 * @returns {Object} - كائن المستخدم المحدث
 */
function updateUser(id, updatedData) {
    const userIndex = users.findIndex(u => u.id === id);
    if (userIndex === -1) {
        throw new Error("المستخدم غير موجود");
    }
    
    // التحقق من عدم تكرار البريد الإلكتروني
    if (updatedData.email && updatedData.email !== users[userIndex].email) {
        if (isEmailInUse(updatedData.email)) {
            throw new Error("البريد الإلكتروني مستخدم بالفعل");
        }
    }
    
    // تحديث بيانات المستخدم
    const updatedUser = {
        ...users[userIndex],
        name: updatedData.name || users[userIndex].name,
        email: updatedData.email || users[userIndex].email,
        role: updatedData.role || users[userIndex].role,
        permissions: updatedData.permissions || users[userIndex].permissions
    };
    
    // إذا تم تقديم كلمة مرور جديدة، قم بتحديثها
    if (updatedData.password) {
        updatedUser.password = updatedData.password;
    }
    
    // حفظ التغييرات
    users[userIndex] = updatedUser;
    saveUsersData();
    
    // إرجاع نسخة من المستخدم بدون كلمة المرور
    const { password, ...userWithoutPassword } = updatedUser;
    return userWithoutPassword;
}

/**
 * دالة لحذف مستخدم
 * @param {number} id - معرف المستخدم
 * @returns {boolean} - نجاح الحذف
 */
function deleteUser(id) {
    const initialLength = users.length;
    users = users.filter(u => u.id !== id);
    
    // التحقق من نجاح الحذف
    if (users.length < initialLength) {
        saveUsersData();
        return true;
    }
    
    return false;
}

/**
 * دالة لحفظ بيانات المستخدمين في التخزين المحلي
 */
function saveUsersData() {
    try {
        // حفظ البيانات في التخزين المحلي
        localStorage.setItem('velasweets_admin_users', JSON.stringify(users));
        
        // إرسال حدث تحديث بيانات المستخدمين
        if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('admin_users_updated'));
        }
        
        return true;
    } catch (error) {
        console.error('خطأ في حفظ بيانات المستخدمين:', error);
        return false;
    }
}

/**
 * دالة لتحميل بيانات المستخدمين من التخزين المحلي
 */
function loadUsersData() {
    try {
        const savedData = localStorage.getItem('velasweets_admin_users');
        if (savedData) {
            const parsedData = JSON.parse(savedData);
            if (Array.isArray(parsedData) && parsedData.length > 0) {
                users = parsedData;
                return true;
            }
        }
        
        // إذا لم يتم العثور على بيانات محفوظة، حفظ البيانات الافتراضية
        saveUsersData();
        return false;
    } catch (error) {
        console.error('خطأ في تحميل بيانات المستخدمين:', error);
        return false;
    }
}

// تنفيذ تحميل البيانات عند بدء تشغيل الصفحة
(function initializeUsers() {
    loadUsersData();
    
    // الاستماع لأحداث تخزين في نوافذ أخرى
    if (typeof window !== 'undefined') {
        window.addEventListener('storage', function(event) {
            if (event.key === 'velasweets_admin_users') {
                loadUsersData();
                
                // إرسال حدث لإخطار الصفحة بتحديث البيانات
                window.dispatchEvent(new CustomEvent('admin_users_reloaded'));
            }
        });
    }
})();

// تصدير الدوال
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        users,
        authenticateUser,
        getUserById,
        hasPermission,
        addUser,
        isEmailInUse,
        updateUser,
        deleteUser,
        saveUsersData,
        loadUsersData
    };
}

// تصدير الدوال للنافذة العامة
if (typeof window !== 'undefined') {
    window.users = users;
    window.authenticateUser = authenticateUser;
    window.getUserById = getUserById;
    window.hasPermission = hasPermission;
    window.addUser = addUser;
    window.isEmailInUse = isEmailInUse;
    window.updateUser = updateUser;
    window.deleteUser = deleteUser;
    window.saveUsersData = saveUsersData;
    window.loadUsersData = loadUsersData;
}
