<!DOCTYPE html>
<html lang="ar" dir="rtl" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - متجر VelaSweets</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap');
        
        :root {
            --primary-color: #5a287d;
            --secondary-color: #f8a5c2;
            --tertiary-color: #b07d28;
            --text-color: #333;
            --text-light: #fff;
            --bg-color: #f8f9fa;
            --card-bg: #fff;
            --sidebar-bg: #fff;
            --sidebar-hover: #f1f1f1;
            --sidebar-active: #5a287d;
            --sidebar-active-text: #fff;
            --border-color: #dee2e6;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --transition-speed: 0.3s;
        }
        
        [data-theme="dark"] {
            --primary-color: #7e3ca3;
            --secondary-color: #ff9aba;
            --tertiary-color: #d9a440;
            --text-color: #e0e0e0;
            --text-light: #fff;
            --bg-color: #121212;
            --card-bg: #1e1e1e;
            --sidebar-bg: #1e1e1e;
            --sidebar-hover: #2d2d2d;
            --sidebar-active: #7e3ca3;
            --sidebar-active-text: #fff;
            --border-color: #2d2d2d;
            --shadow-color: rgba(0, 0, 0, 0.5);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            direction: rtl;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            max-width: 400px;
            width: 90%;
            background-color: var(--card-bg);
            border-radius: 15px;
            box-shadow: 0 10px 30px var(--shadow-color);
            overflow: hidden;
            padding: 30px;
        }
        
        .login-logo {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-logo h1 {
            font-family: 'Playfair Display', serif;
            color: var(--primary-color);
            font-size: 2.5rem;
            margin-bottom: 5px;
        }
        
        .login-logo p {
            color: var(--text-color);
            opacity: 0.8;
        }
        
        .login-form h3 {
            color: var(--primary-color);
            margin-bottom: 20px;
            font-weight: 600;
            text-align: center;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: var(--bg-color);
            color: var(--text-color);
            transition: all 0.3s;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(90, 40, 125, 0.25);
            outline: none;
        }
        
        .password-field {
            position: relative;
        }
        
        .password-toggle {
            position: absolute;
            top: 50%;
            left: 15px;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--text-color);
            cursor: pointer;
            z-index: 5;
        }
        
        .form-check {
            margin: 15px 0;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            color: white;
            padding: 12px;
            border-radius: 8px;
            width: 100%;
            font-weight: 600;
            transition: all 0.3s;
            margin-top: 10px;
        }
        
        .btn-primary:hover {
            opacity: 0.9;
            transform: translateY(-2px);
        }
        
        .login-footer {
            text-align: center;
            margin-top: 20px;
            font-size: 0.9rem;
        }
        
        .theme-toggle {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
            cursor: pointer;
        }
        
        .theme-toggle i {
            margin-left: 8px;
        }
        
        /* تحسين مظهر رسالة الخطأ */
        .alert-danger {
            background-color: rgba(220, 53, 69, 0.1);
            color: var(--danger-color);
            border: 1px solid rgba(220, 53, 69, 0.2);
            border-radius: 8px;
            padding: 12px 15px;
            margin-bottom: 20px;
            position: relative;
            text-align: center;
            font-weight: 500;
        }
        
        /* تأثير التحميل */
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
            margin-left: 8px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-logo">
            <h1>VelaSweets</h1>
            <p>نظام إدارة متجر الحلويات</p>
        </div>
        
        <div class="login-form">
            <h3>تسجيل دخول الإدارة</h3>
            
            <div class="alert alert-danger d-none" id="loginError"></div>
            
            <form id="adminLoginForm">
                <div class="form-group">
                    <label for="adminEmail">البريد الإلكتروني</label>
                    <input type="email" id="adminEmail" class="form-control" placeholder="أدخل البريد الإلكتروني" required>
                </div>
                
                <div class="form-group">
                    <label for="adminPassword">كلمة المرور</label>
                    <div class="password-field">
                        <input type="password" id="adminPassword" class="form-control" placeholder="أدخل كلمة المرور" required>
                        <button type="button" class="password-toggle" id="togglePassword">
                            <i class="far fa-eye"></i>
                        </button>
                    </div>
                </div>
                
                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="rememberMe">
                    <label class="form-check-label" for="rememberMe">تذكرني</label>
                </div>
                
                <button type="submit" class="btn btn-primary" id="loginBtn">
                    <i class="bi bi-box-arrow-in-right me-2"></i>تسجيل الدخول
                    <span class="spinner d-none" id="loginSpinner"></span>
                </button>
            </form>
            
            <div class="theme-toggle" id="themeToggle">
                <i class="bi bi-moon"></i>
                <span>تبديل الوضع الداكن</span>
            </div>
        </div>
        
        <div class="login-footer">
            جميع الحقوق محفوظة &copy; 2025 VelaSweets
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="database/users.js"></script>
    <script src="scripts/admin_users.js"></script>
    <script src="scripts/auth.js"></script>
    <script>
        // التحقق من حالة تسجيل الدخول عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            checkAuthStatus();
            
            // إضافة مستمع حدث لنموذج تسجيل الدخول
            const loginForm = document.getElementById('adminLoginForm');
            if (loginForm) {
                loginForm.addEventListener('submit', handleLogin);
            }
            
            // إضافة مستمع حدث لزر إظهار/إخفاء كلمة المرور
            const togglePassword = document.getElementById('togglePassword');
            if (togglePassword) {
                togglePassword.addEventListener('click', function() {
                    const passwordInput = document.getElementById('adminPassword');
                    const icon = this.querySelector('i');
                    
                    if (passwordInput.type === 'password') {
                        passwordInput.type = 'text';
                        icon.classList.remove('fa-eye');
                        icon.classList.add('fa-eye-slash');
                    } else {
                        passwordInput.type = 'password';
                        icon.classList.remove('fa-eye-slash');
                        icon.classList.add('fa-eye');
                    }
                });
            }
            
            // مستمع الحدث لتبديل الوضع الداكن
            const themeToggle = document.getElementById('themeToggle');
            if (themeToggle) {
                themeToggle.addEventListener('click', function() {
                    const htmlElement = document.documentElement;
                    const currentTheme = htmlElement.getAttribute('data-theme');
                    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                    
                    htmlElement.setAttribute('data-theme', newTheme);
                    localStorage.setItem('theme', newTheme);
                    
                    // تحديث أيقونة التبديل
                    const icon = this.querySelector('i');
                    const text = this.querySelector('span');
                    
                    if (newTheme === 'dark') {
                        icon.classList.remove('bi-moon');
                        icon.classList.add('bi-sun');
                        text.textContent = 'تبديل الوضع الفاتح';
                    } else {
                        icon.classList.remove('bi-sun');
                        icon.classList.add('bi-moon');
                        text.textContent = 'تبديل الوضع الداكن';
                    }
                });
            }
            
            // تحقق من الوضع المحفوظ
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme) {
                document.documentElement.setAttribute('data-theme', savedTheme);
                
                if (savedTheme === 'dark') {
                    const themeToggle = document.getElementById('themeToggle');
                    const icon = themeToggle.querySelector('i');
                    const text = themeToggle.querySelector('span');
                    
                    icon.classList.remove('bi-moon');
                    icon.classList.add('bi-sun');
                    text.textContent = 'تبديل الوضع الفاتح';
                }
            }
        });
        
        // وظيفة للتحقق من حالة تسجيل الدخول
        function checkAuthStatus() {
            try {
                // محاولة استخدام localStorage
                const token = localStorage.getItem('auth_token');
                if (token) {
                    // تحويل المستخدم إلى لوحة التحكم
                    window.location.href = 'dashboard.html';
                    return;
                }
            } catch {
                // محاولة استخدام الكوكيز
                if (document.cookie.indexOf('auth_token=') !== -1) {
                    // تحويل المستخدم إلى لوحة التحكم
                    window.location.href = 'dashboard.html';
                    return;
                }
            }
        }
        
        // وظيفة لتخزين بيانات جلسة المستخدم
        function storeSession(user, rememberMe) {
            try {
                // إنشاء توكن بسيط
                const simpleToken = btoa(encodeURIComponent(JSON.stringify({
                    id: user.id,
                    name: user.name,
                    email: user.email,
                    role: user.role,
                    exp: new Date().getTime() + (rememberMe ? 30 * 24 * 60 * 60 * 1000 : 24 * 60 * 60 * 1000)
                })));
                
                // تخزين البيانات في localStorage
                localStorage.setItem('auth_token', simpleToken);
                localStorage.setItem('user_data', JSON.stringify({
                    id: user.id,
                    name: user.name,
                    email: user.email,
                    role: user.role,
                    permissions: user.permissions
                }));
                
                // تخزين البيانات في الكوكيز كاحتياطي
                document.cookie = `user_logged_in=true; path=/; max-age=${rememberMe ? 30 * 24 * 60 * 60 : 24 * 60 * 60}`;
                document.cookie = `admin_user=${user.email}; path=/; max-age=${rememberMe ? 30 * 24 * 60 * 60 : 24 * 60 * 60}`;
                document.cookie = `admin_role=${user.role}; path=/; max-age=${rememberMe ? 30 * 24 * 60 * 60 : 24 * 60 * 60}`;
            } catch {
                // في حالة فشل localStorage، استخدم الكوكيز فقط
                document.cookie = `user_logged_in=true; path=/; max-age=${rememberMe ? 30 * 24 * 60 * 60 : 24 * 60 * 60}`;
                document.cookie = `admin_user=${user.email}; path=/; max-age=${rememberMe ? 30 * 24 * 60 * 60 : 24 * 60 * 60}`;
                document.cookie = `admin_role=${user.role}; path=/; max-age=${rememberMe ? 30 * 24 * 60 * 60 : 24 * 60 * 60}`;
            }
        }
        
        // وظيفة تسجيل الدخول
        function handleLogin(event) {
            event.preventDefault();
            
            // الحصول على قيم الحقول
            const email = document.getElementById('adminEmail').value;
            const password = document.getElementById('adminPassword').value;
            const rememberMe = document.getElementById('rememberMe').checked;
            
            // عرض رسائل الخطأ
            const errorDiv = document.getElementById('loginError');
            const loginBtn = document.getElementById('loginBtn');
            const loginSpinner = document.getElementById('loginSpinner');
            
            // التحقق من ملء الحقول
            if (!email || !password) {
                errorDiv.textContent = 'يرجى ملء جميع الحقول المطلوبة';
                errorDiv.classList.remove('d-none');
                return;
            }
            
            // إظهار مؤشر التحميل
            loginSpinner.classList.remove('d-none');
            loginBtn.setAttribute('disabled', 'disabled');
            
            // إخفاء رسالة الخطأ السابقة إن وجدت
            errorDiv.classList.add('d-none');
            
            // إرسال طلب تسجيل الدخول (محاكاة)
            setTimeout(() => {
                try {
                    let user = null;
                    
                    // التحقق من وجود دالة loginAdmin
                    if (typeof loginAdmin === 'function') {
                        // استدعاء دالة تسجيل الدخول
                        loginAdmin(email, password).then(response => {
                            user = response;
                            
                            if (user) {
                                // تخزين بيانات الجلسة
                                storeSession(user, rememberMe);
                                
                                // توجيه المستخدم إلى لوحة التحكم
                                window.location.href = 'dashboard.html';
                            } else {
                                // عرض رسالة الخطأ
                                errorDiv.textContent = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
                                errorDiv.classList.remove('d-none');
                                // إخفاء مؤشر التحميل
                                loginSpinner.classList.add('d-none');
                                loginBtn.removeAttribute('disabled');
                            }
                        });
                    } else {
                        // محاكاة تسجيل الدخول إذا لم تكن الدالة متاحة
                        
                        // قائمة المستخدمين المحلية للتجريب
                        const localUsers = [
                            {
                                id: 1,
                                name: "المدير",
                                email: "<EMAIL>",
                                password: "Admin123!",
                                role: "admin",
                                permissions: ["all"]
                            },
                            {
                                id: 2,
                                name: "موظف1",
                                email: "<EMAIL>",
                                password: "2025",
                                role: "employee",
                                permissions: ["view_orders", "edit_orders", "view_products"]
                            },
                            {
                                id: 3,
                                name: "موظف 2",
                                email: "<EMAIL>",
                                password: "2025",
                                role: "employee",
                                permissions: ["view_orders", "view_products", "edit_products"]
                            },
                            {
                                id: 4,
                                name: "موظف 3",
                                email: "<EMAIL>",
                                password: "2025",
                                role: "employee",
                                permissions: ["view_customers", "view_orders", "view_statistics"]
                            }
                        ];
                        
                        user = localUsers.find(u => 
                            u.email.toLowerCase() === email.toLowerCase() && 
                            u.password === password
                        );
                        
                        if (user) {
                            // تخزين بيانات الجلسة
                            storeSession(user, rememberMe);
                            
                            // توجيه المستخدم إلى لوحة التحكم
                            window.location.href = 'dashboard.html';
                        } else {
                            // عرض رسالة الخطأ
                            errorDiv.textContent = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
                            errorDiv.classList.remove('d-none');
                            // إخفاء مؤشر التحميل
                            loginSpinner.classList.add('d-none');
                            loginBtn.removeAttribute('disabled');
                        }
                    }
                } catch (error) {
                    errorDiv.textContent = 'حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.';
                    errorDiv.classList.remove('d-none');
                    // إخفاء مؤشر التحميل
                    loginSpinner.classList.add('d-none');
                    loginBtn.removeAttribute('disabled');
                }
            }, 1500);
        }
    </script>
</body>
</html>