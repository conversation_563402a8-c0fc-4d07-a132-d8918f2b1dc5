// ملف لإضافة البيانات الافتراضية للنظام
// يتم تشغيله مرة واحدة لإعداد البيانات الأولية

(function() {
    'use strict';

    // التحقق من وجود البيانات مسبقاً
    const existingCustomers = localStorage.getItem('customers');
    const existingProducts = localStorage.getItem('velasweets_products');
    
    // إضافة عملاء افتراضيين إذا لم يكونوا موجودين
    if (!existingCustomers) {
        const defaultCustomers = [
            {
                id: 1001,
                fullName: "أحمد محمد علي",
                email: "<EMAIL>",
                phone: "07701234567",
                province: "بغداد",
                address: "منطقة الكرادة، شارع الرشيد",
                password: "123456",
                createdAt: new Date().toISOString(),
                isActive: true,
                role: "customer"
            },
            {
                id: 1002,
                fullName: "فاطمة حسن",
                email: "<EMAIL>",
                phone: "07801234567",
                province: "البصرة",
                address: "منطقة العشار، شارع الكورنيش",
                password: "123456",
                createdAt: new Date().toISOString(),
                isActive: true,
                role: "customer"
            },
            {
                id: 1003,
                fullName: "محمد خالد",
                email: "<EMAIL>",
                phone: "07901234567",
                province: "أربيل",
                address: "منطقة عنكاوا، شارع الجامعة",
                password: "123456",
                createdAt: new Date().toISOString(),
                isActive: true,
                role: "customer"
            },
            {
                id: 9999,
                fullName: "مدير النظام",
                email: "<EMAIL>",
                phone: "07700000000",
                province: "بغداد",
                address: "مقر الشركة الرئيسي",
                password: "admin123",
                createdAt: new Date().toISOString(),
                isActive: true,
                role: "admin"
            }
        ];

        localStorage.setItem('customers', JSON.stringify(defaultCustomers));
        console.log('تم إضافة العملاء الافتراضيين');
    }

    // إضافة طلبات تجريبية
    const existingOrders = localStorage.getItem('customer_orders');
    if (!existingOrders) {
        const defaultOrders = [
            {
                id: 2001,
                customerId: 1001,
                items: [
                    {
                        id: 1,
                        name: "كيك الشوكولاتة",
                        price: 15.99,
                        quantity: 1,
                        image: "https://images.unsplash.com/photo-1578985545062-69928b1d9587?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                    },
                    {
                        id: 2,
                        name: "بقلاوة",
                        price: 12.99,
                        quantity: 2,
                        image: "https://images.unsplash.com/photo-1519915028121-7d3463d5b1ff?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                    }
                ],
                total: 41.97,
                shipping: 5.00,
                status: "completed",
                date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // منذ يومين
                customerData: {
                    fullName: "أحمد محمد علي",
                    email: "<EMAIL>",
                    phone: "07701234567",
                    address: "منطقة الكرادة، شارع الرشيد"
                }
            },
            {
                id: 2002,
                customerId: 1002,
                items: [
                    {
                        id: 3,
                        name: "كنافة",
                        price: 10.99,
                        quantity: 1,
                        image: "https://images.unsplash.com/photo-1587314168485-3236d6710101?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                    }
                ],
                total: 10.99,
                shipping: 5.00,
                status: "pending",
                date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // منذ يوم
                customerData: {
                    fullName: "فاطمة حسن",
                    email: "<EMAIL>",
                    phone: "07801234567",
                    address: "منطقة العشار، شارع الكورنيش"
                }
            },
            {
                id: 2003,
                customerId: 1003,
                items: [
                    {
                        id: 4,
                        name: "كوكيز الشوكولاتة",
                        price: 8.99,
                        quantity: 3,
                        image: "https://images.unsplash.com/photo-1609541436483-f4d8304da3fb?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                    },
                    {
                        id: 5,
                        name: "تشيز كيك الفراولة",
                        price: 18.99,
                        quantity: 1,
                        image: "https://images.unsplash.com/photo-1565958011703-44f9829ba187?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                    }
                ],
                total: 45.96,
                shipping: 0.00, // شحن مجاني
                status: "processing",
                date: new Date().toISOString(), // اليوم
                customerData: {
                    fullName: "محمد خالد",
                    email: "<EMAIL>",
                    phone: "07901234567",
                    address: "منطقة عنكاوا، شارع الجامعة"
                }
            }
        ];

        localStorage.setItem('customer_orders', JSON.stringify(defaultOrders));
        console.log('تم إضافة الطلبات التجريبية');
    }

    // إضافة رسائل اتصال تجريبية
    const existingMessages = localStorage.getItem('contact_messages');
    if (!existingMessages) {
        const defaultMessages = [
            {
                id: 3001,
                name: "سارة أحمد",
                email: "<EMAIL>",
                phone: "07711234567",
                subject: "استفسار عن طلب",
                message: "أريد الاستفسار عن حالة طلبي رقم #2001",
                timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), // منذ 3 ساعات
                status: "new"
            },
            {
                id: 3002,
                name: "عمر حسين",
                email: "<EMAIL>",
                phone: "07821234567",
                subject: "اقتراح",
                message: "أقترح إضافة المزيد من أنواع الكيك الخالي من الجلوتين",
                timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // منذ يوم
                status: "new"
            },
            {
                id: 3003,
                name: "ليلى محمود",
                email: "<EMAIL>",
                phone: "07931234567",
                subject: "شكوى",
                message: "لم أتلق طلبي في الوقت المحدد",
                timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // منذ يومين
                status: "new"
            }
        ];

        localStorage.setItem('contact_messages', JSON.stringify(defaultMessages));
        console.log('تم إضافة رسائل الاتصال التجريبية');
    }

    // إضافة إعدادات النظام الافتراضية
    const existingSettings = localStorage.getItem('system_settings');
    if (!existingSettings) {
        const defaultSettings = {
            siteName: "VelaSweets",
            siteDescription: "متجر متخصص في تقديم أشهى الحلويات الشرقية والغربية",
            currency: "USD",
            language: "ar",
            timezone: "Asia/Baghdad",
            shippingCost: 5.00,
            freeShippingThreshold: 50.00,
            taxRate: 0.00,
            contactEmail: "<EMAIL>",
            contactPhone: "+*********** 789",
            address: "شارع الرشيد، منطقة الكرادة، بغداد، العراق",
            workingHours: {
                saturday: "9:00 AM - 10:00 PM",
                sunday: "9:00 AM - 10:00 PM",
                monday: "9:00 AM - 10:00 PM",
                tuesday: "9:00 AM - 10:00 PM",
                wednesday: "9:00 AM - 10:00 PM",
                thursday: "9:00 AM - 10:00 PM",
                friday: "2:00 PM - 10:00 PM"
            },
            socialMedia: {
                facebook: "https://facebook.com/velasweets",
                instagram: "https://instagram.com/velasweets",
                twitter: "https://twitter.com/velasweets"
            },
            paymentMethods: ["cash", "card", "online"],
            deliveryAreas: ["بغداد", "البصرة", "أربيل", "النجف", "كربلاء", "الموصل"],
            lastUpdated: new Date().toISOString()
        };

        localStorage.setItem('system_settings', JSON.stringify(defaultSettings));
        console.log('تم إضافة إعدادات النظام الافتراضية');
    }

    console.log('تم إعداد البيانات الافتراضية للنظام بنجاح');
})();

// دالة لإعادة تعيين جميع البيانات (للاختبار)
function resetAllData() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟ سيتم حذف جميع البيانات الحالية.')) {
        localStorage.removeItem('customers');
        localStorage.removeItem('customer_orders');
        localStorage.removeItem('contact_messages');
        localStorage.removeItem('system_settings');
        localStorage.removeItem('velasweets_products');
        localStorage.removeItem('customer_token');
        localStorage.removeItem('customer_data');
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_data');
        localStorage.removeItem('cart');
        
        alert('تم إعادة تعيين جميع البيانات. سيتم إعادة تحميل الصفحة.');
        window.location.reload();
    }
}

// دالة لعرض إحصائيات البيانات
function showDataStats() {
    const customers = JSON.parse(localStorage.getItem('customers') || '[]');
    const orders = JSON.parse(localStorage.getItem('customer_orders') || '[]');
    const messages = JSON.parse(localStorage.getItem('contact_messages') || '[]');
    const products = JSON.parse(localStorage.getItem('velasweets_products') || '[]');

    const stats = `إحصائيات البيانات:

العملاء: ${customers.length}
الطلبات: ${orders.length}
الرسائل: ${messages.length}
المنتجات: ${products.length}

إجمالي المبيعات: $${orders.filter(o => o.status === 'completed').reduce((sum, o) => sum + o.total + (o.shipping || 0), 0).toFixed(2)}
الطلبات المعلقة: ${orders.filter(o => o.status === 'pending').length}
الطلبات قيد التنفيذ: ${orders.filter(o => o.status === 'processing').length}`;

    alert(stats);
}

// إتاحة الدوال للاستخدام العام
window.resetAllData = resetAllData;
window.showDataStats = showDataStats;
